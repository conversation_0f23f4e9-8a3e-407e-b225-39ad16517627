// Common utilities for legacy format detection and processing

use std::collections::HashMap;
use crate::conversion::error::ConversionError;

/// Magic byte signatures for format detection
pub const DOC_MAGIC: &[u8] = &[0xD0, 0xCF, 0x11, 0xE0, 0xA1, 0xB1, 0x1A, 0xE1]; // OLE2 signature
pub const WORDPERFECT_MAGIC: &[u8] = &[0xFF, 0x57, 0x50, 0x43]; // WPC signature
pub const DBASE_MAGIC: &[u8] = &[0x03]; // dBase III signature
pub const DBASE4_MAGIC: &[u8] = &[0x04]; // dBase IV signature
pub const WORDSTAR_MAGIC: &[u8] = &[0x1D, 0x7D]; // WordStar signature
pub const LOTUS_MAGIC: &[u8] = &[0x00, 0x00, 0x02, 0x00]; // Lotus 1-2-3 signature

/// Check if content starts with magic bytes
pub fn has_magic_bytes(content: &[u8], magic: &[u8]) -> bool {
    if content.len() < magic.len() {
        return false;
    }
    content[..magic.len()] == *magic
}

/// Extract string from null-terminated bytes
pub fn extract_null_terminated_string(bytes: &[u8]) -> String {
    let end = bytes.iter().position(|&b| b == 0).unwrap_or(bytes.len());
    String::from_utf8_lossy(&bytes[..end]).to_string()
}

/// Convert bytes to little-endian u16
pub fn bytes_to_u16_le(bytes: &[u8]) -> u16 {
    if bytes.len() < 2 {
        return 0;
    }
    u16::from_le_bytes([bytes[0], bytes[1]])
}

/// Convert bytes to little-endian u32
pub fn bytes_to_u32_le(bytes: &[u8]) -> u32 {
    if bytes.len() < 4 {
        return 0;
    }
    u32::from_le_bytes([bytes[0], bytes[1], bytes[2], bytes[3]])
}

/// Check if content appears to be text-based
pub fn is_text_content(content: &[u8]) -> bool {
    if content.is_empty() {
        return false;
    }
    
    let sample_size = std::cmp::min(content.len(), 1024);
    let sample = &content[..sample_size];
    
    let mut printable_count = 0;
    let mut control_count = 0;
    
    for &byte in sample {
        match byte {
            0x09 | 0x0A | 0x0D => printable_count += 1, // Tab, LF, CR
            0x20..=0x7E => printable_count += 1,        // Printable ASCII
            0x00..=0x08 | 0x0B..=0x0C | 0x0E..=0x1F => control_count += 1, // Control chars
            _ => {} // Extended ASCII or binary
        }
    }
    
    let total = printable_count + control_count;
    if total == 0 {
        return false;
    }
    
    // Consider text if >70% printable characters
    (printable_count as f32 / total as f32) > 0.7
}

/// Detect character encoding
pub fn detect_encoding(content: &[u8]) -> String {
    // Simple encoding detection
    if content.len() >= 3 && content[0] == 0xEF && content[1] == 0xBB && content[2] == 0xBF {
        return "UTF-8".to_string();
    }
    
    if content.len() >= 2 {
        if content[0] == 0xFF && content[1] == 0xFE {
            return "UTF-16LE".to_string();
        }
        if content[0] == 0xFE && content[1] == 0xFF {
            return "UTF-16BE".to_string();
        }
    }
    
    // Check for high-bit characters (potential extended ASCII/Latin-1)
    let has_high_bits = content.iter().any(|&b| b > 127);
    if has_high_bits {
        "Latin-1".to_string()
    } else {
        "ASCII".to_string()
    }
}

/// Extract metadata from file header
pub fn extract_metadata(content: &[u8], format_name: &str) -> HashMap<String, String> {
    let mut metadata = HashMap::new();
    
    metadata.insert("format".to_string(), format_name.to_string());
    metadata.insert("size".to_string(), content.len().to_string());
    metadata.insert("encoding".to_string(), detect_encoding(content));
    metadata.insert("is_text".to_string(), is_text_content(content).to_string());
    
    metadata
}

/// Clean and normalize text content
pub fn clean_text_content(text: &str) -> String {
    text.chars()
        .filter(|&c| c.is_ascii_graphic() || c.is_ascii_whitespace())
        .collect::<String>()
        .lines()
        .map(|line| line.trim_end())
        .collect::<Vec<_>>()
        .join("\n")
}

/// Convert control characters to readable format
pub fn escape_control_chars(text: &str) -> String {
    text.chars()
        .map(|c| match c {
            '\x00' => "\\0".to_string(),
            '\x07' => "\\a".to_string(),
            '\x08' => "\\b".to_string(),
            '\x09' => "\t".to_string(),
            '\x0A' => "\n".to_string(),
            '\x0C' => "\\f".to_string(),
            '\x0D' => "\r".to_string(),
            '\x1B' => "\\e".to_string(),
            c if c.is_control() => format!("\\x{:02X}", c as u8),
            c => c.to_string(),
        })
        .collect()
}

/// Parse date from various legacy formats
pub fn parse_legacy_date(bytes: &[u8]) -> Option<String> {
    if bytes.len() < 3 {
        return None;
    }
    
    // Try different date formats common in legacy files
    
    // dBase date format (YYYYMMDD)
    if bytes.len() >= 8 {
        let date_str = String::from_utf8_lossy(&bytes[..8]);
        if date_str.chars().all(|c| c.is_ascii_digit()) {
            let year = &date_str[0..4];
            let month = &date_str[4..6];
            let day = &date_str[6..8];
            return Some(format!("{}-{}-{}", year, month, day));
        }
    }
    
    // Binary date format (little-endian)
    if bytes.len() >= 4 {
        let days_since_1900 = bytes_to_u32_le(bytes);
        if days_since_1900 > 0 && days_since_1900 < 100000 {
            // Approximate conversion (simplified)
            let year = 1900 + (days_since_1900 / 365);
            let month = ((days_since_1900 % 365) / 30) + 1;
            let day = (days_since_1900 % 30) + 1;
            return Some(format!("{:04}-{:02}-{:02}", year, month, day));
        }
    }
    
    None
}

/// Validate file structure integrity
pub fn validate_file_structure(content: &[u8], expected_size: Option<usize>) -> Result<(), ConversionError> {
    if content.is_empty() {
        return Err(ConversionError::InvalidInput("Empty file content".to_string()));
    }
    
    if let Some(size) = expected_size {
        if content.len() != size {
            return Err(ConversionError::InvalidInput(
                format!("File size mismatch: expected {}, got {}", size, content.len())
            ));
        }
    }
    
    Ok(())
}

/// Extract version information from file header
pub fn extract_version_info(content: &[u8], offset: usize, length: usize) -> Option<String> {
    if content.len() < offset + length {
        return None;
    }
    
    let version_bytes = &content[offset..offset + length];
    let version_str = String::from_utf8_lossy(version_bytes);
    
    // Clean up version string
    let cleaned = version_str
        .trim_matches('\0')
        .trim()
        .to_string();
    
    if cleaned.is_empty() {
        None
    } else {
        Some(cleaned)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_magic_bytes_detection() {
        let content = [0xD0, 0xCF, 0x11, 0xE0, 0xA1, 0xB1, 0x1A, 0xE1, 0x00, 0x00];
        assert!(has_magic_bytes(&content, DOC_MAGIC));
        assert!(!has_magic_bytes(&content, WORDPERFECT_MAGIC));
    }

    #[test]
    fn test_text_content_detection() {
        let text_content = b"Hello, World! This is a test.";
        let binary_content = [0x00, 0x01, 0x02, 0x03, 0xFF, 0xFE];
        
        assert!(is_text_content(text_content));
        assert!(!is_text_content(&binary_content));
    }

    #[test]
    fn test_encoding_detection() {
        let utf8_bom = [0xEF, 0xBB, 0xBF, 0x48, 0x65, 0x6C, 0x6C, 0x6F];
        let utf16le_bom = [0xFF, 0xFE, 0x48, 0x00, 0x65, 0x00];
        let ascii_content = b"Hello";
        
        assert_eq!(detect_encoding(&utf8_bom), "UTF-8");
        assert_eq!(detect_encoding(&utf16le_bom), "UTF-16LE");
        assert_eq!(detect_encoding(ascii_content), "ASCII");
    }

    #[test]
    fn test_bytes_conversion() {
        let bytes = [0x34, 0x12, 0x78, 0x56];
        assert_eq!(bytes_to_u16_le(&bytes), 0x1234);
        assert_eq!(bytes_to_u32_le(&bytes), 0x56781234);
    }

    #[test]
    fn test_null_terminated_string() {
        let bytes = b"Hello\0World\0";
        assert_eq!(extract_null_terminated_string(bytes), "Hello");
    }
}
