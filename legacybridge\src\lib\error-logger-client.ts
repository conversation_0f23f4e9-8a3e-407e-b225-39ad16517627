/**
 * Client-side Error Logging System for LegacyBridge
 * Browser-safe version without Node.js dependencies
 */

import { format } from 'date-fns';

// Log levels
export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  FATAL = 4
}

// Log entry interface
export interface LogEntry {
  id: string;
  timestamp: Date;
  level: LogLevel;
  message: string;
  context?: Record<string, unknown>;
  error?: Error;
  performanceData?: PerformanceData;
}

// Performance data interface
export interface PerformanceData {
  duration?: number;
  memoryUsage?: number;
  cpuUsage?: number;
}

// Error context interface
export interface ErrorContext {
  userId?: string;
  sessionId?: string;
  component?: string;
  action?: string;
  metadata?: Record<string, unknown>;
}

class ClientErrorLogger {
  private currentLevel: LogLevel = LogLevel.INFO;
  private logs: LogEntry[] = [];
  private maxLogsInMemory = 1000;

  constructor() {
    // Try to load existing logs from localStorage
    this.loadLogsFromStorage();
  }

  private generateId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private loadLogsFromStorage(): void {
    try {
      const stored = localStorage.getItem('legacybridge_logs');
      if (stored) {
        this.logs = JSON.parse(stored);
      }
    } catch (error) {
      console.error('Failed to load logs from storage:', error);
    }
  }

  private saveLogsToStorage(): void {
    try {
      // Keep only recent logs to avoid storage limits
      const recentLogs = this.logs.slice(-this.maxLogsInMemory);
      localStorage.setItem('legacybridge_logs', JSON.stringify(recentLogs));
    } catch (error) {
      console.error('Failed to save logs to storage:', error);
    }
  }

  private createLogEntry(
    level: LogLevel,
    message: string,
    context?: Record<string, unknown>,
    error?: Error
  ): LogEntry {
    return {
      id: this.generateId(),
      timestamp: new Date(),
      level,
      message,
      context,
      error: error ? {
        name: error.name,
        message: error.message,
        stack: error.stack
      } as any : undefined
    };
  }

  private shouldLog(level: LogLevel): boolean {
    return level >= this.currentLevel;
  }

  private log(entry: LogEntry): void {
    if (!this.shouldLog(entry.level)) return;

    // Add to memory
    this.logs.push(entry);

    // Trim if needed
    if (this.logs.length > this.maxLogsInMemory) {
      this.logs = this.logs.slice(-this.maxLogsInMemory);
    }

    // Console output
    const logMethod = this.getConsoleMethod(entry.level);
    const formattedMessage = this.formatMessage(entry);
    
    if (entry.error) {
      logMethod(formattedMessage, entry.error);
    } else {
      logMethod(formattedMessage);
    }

    // Save to storage
    this.saveLogsToStorage();

    // Send to remote if fatal
    if (entry.level === LogLevel.FATAL) {
      this.sendToRemote(entry);
    }
  }

  private getConsoleMethod(level: LogLevel): (...args: any[]) => void {
    switch (level) {
      case LogLevel.DEBUG:
        return console.debug;
      case LogLevel.INFO:
        return console.info;
      case LogLevel.WARN:
        return console.warn;
      case LogLevel.ERROR:
      case LogLevel.FATAL:
        return console.error;
      default:
        return console.log;
    }
  }

  private formatMessage(entry: LogEntry): string {
    const timestamp = format(entry.timestamp, 'yyyy-MM-dd HH:mm:ss.SSS');
    const level = LogLevel[entry.level];
    return `[${timestamp}] [${level}] ${entry.message}`;
  }

  private async sendToRemote(entry: LogEntry): Promise<void> {
    try {
      // In a real app, this would send to your logging service
      const response = await fetch('/api/logs', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(entry)
      });
      
      if (!response.ok) {
        console.error('Failed to send log to remote:', response.statusText);
      }
    } catch (error) {
      console.error('Failed to send log to remote:', error);
    }
  }

  // Public API
  setLevel(level: LogLevel): void {
    this.currentLevel = level;
  }

  debug(message: string, context?: Record<string, unknown>): void {
    this.log(this.createLogEntry(LogLevel.DEBUG, message, context));
  }

  info(message: string, context?: Record<string, unknown>): void {
    this.log(this.createLogEntry(LogLevel.INFO, message, context));
  }

  warn(message: string, context?: Record<string, unknown>): void {
    this.log(this.createLogEntry(LogLevel.WARN, message, context));
  }

  error(message: string, error?: Error, context?: Record<string, unknown>): void {
    this.log(this.createLogEntry(LogLevel.ERROR, message, context, error));
  }

  fatal(message: string, error?: Error, context?: Record<string, unknown>): void {
    this.log(this.createLogEntry(LogLevel.FATAL, message, context, error));
  }

  getLogs(level?: LogLevel): LogEntry[] {
    if (level !== undefined) {
      return this.logs.filter(log => log.level === level);
    }
    return [...this.logs];
  }

  clearLogs(): void {
    this.logs = [];
    this.saveLogsToStorage();
  }

  exportLogs(): string {
    return JSON.stringify(this.logs, null, 2);
  }

  startTimer(label: string): () => number {
    const start = performance.now();
    return () => {
      const duration = performance.now() - start;
      this.debug(`${label} completed`, { duration });
      return duration;
    };
  }
}

// Create singleton instance
export const logger = new ClientErrorLogger();

// Helper functions
export function startTimer(label: string): () => number {
  return logger.startTimer(label);
}

export function logError(error: Error, context?: ErrorContext): void {
  logger.error(error.message, error, context as any);
}

export function logPerformance(
  operation: string,
  duration: number,
  metadata?: Record<string, unknown>
): void {
  logger.info(`Performance: ${operation}`, {
    performanceData: { duration },
    ...metadata
  });
}