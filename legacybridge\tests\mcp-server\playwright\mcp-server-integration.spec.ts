// Playwright Integration Tests for MCP Server
// Tests the MCP server functionality using <PERSON>wright for API testing

import { test, expect } from '@playwright/test';
import { MCPServer } from '../../../src/mcp-server/core/mcp-server';
import { MCPConfig } from '../../../src/mcp-server/utils/mcp-config';
import { MCPLogger } from '../../../src/mcp-server/utils/mcp-logger';

// Test configuration for MCP server
const testConfig: MCPConfig = {
  port: 3032, // Use different port for testing
  environment: 'test',
  logLevel: 'error', // Reduce logging during tests
  version: '1.0.0-test',
  apiKeys: ['test-api-key'],
  rateLimit: 1000,
  enableDashboard: false,
  enableMetrics: false,
  enableFileStorage: false,
  cache: {
    enabled: false,
    type: 'memory',
    ttl: 3600,
    maxSize: 100
  },
  fileStorage: {
    type: 'local',
    basePath: './test-storage'
  },
  legacyFormats: {
    enableDOC: false, // Disable for testing to avoid LibreOffice dependency
    enableWordPerfect: false,
    enableOtherLegacyFormats: false
  },
  batchProcessing: {
    maxConcurrentJobs: 2,
    maxFilesPerBatch: 10,
    maxBatchSizeMB: 100
  }
};

let server: MCPServer;
let baseURL: string;

test.describe('MCP Server Integration Tests', () => {
  
  test.beforeAll(async () => {
    // For now, we'll test individual components without starting the full server
    // This avoids TypeScript compilation issues while we fix the server startup
    baseURL = `http://localhost:${testConfig.port}`;
    console.log('🧪 Testing MCP Server components...');
  });

  test.afterAll(async () => {
    // Cleanup if needed
    console.log('✅ MCP Server component tests complete');
  });

  test('should respond to health check endpoint', async ({ request }) => {
    const response = await request.get(`${baseURL}/health`);
    
    expect(response.status()).toBe(200);
    
    const body = await response.json();
    expect(body).toHaveProperty('status', 'ok');
    expect(body).toHaveProperty('timestamp');
    expect(body).toHaveProperty('version');
  });

  test('should require authentication for protected endpoints', async ({ request }) => {
    // Test without API key
    const response = await request.post(`${baseURL}/api/convert`, {
      data: {
        content: 'test content',
        format: 'rtf'
      }
    });
    
    expect(response.status()).toBe(401);
    
    const body = await response.json();
    expect(body).toHaveProperty('status', 'error');
    expect(body).toHaveProperty('errorCode', 'AUTHENTICATION_ERROR');
  });

  test('should accept valid API key', async ({ request }) => {
    const response = await request.post(`${baseURL}/api/convert`, {
      headers: {
        'X-API-Key': 'test-api-key'
      },
      data: {
        content: '\\rtf1\\ansi Test RTF content',
        format: 'rtf'
      }
    });
    
    // Should not be 401 (authentication error)
    expect(response.status()).not.toBe(401);
  });

  test('should handle RTF to Markdown conversion', async ({ request }) => {
    const rtfContent = '{\\rtf1\\ansi\\deff0 {\\fonttbl {\\f0 Times New Roman;}} \\f0\\fs24 Hello World!}';
    
    const response = await request.post(`${baseURL}/api/convert`, {
      headers: {
        'X-API-Key': 'test-api-key'
      },
      data: {
        content: rtfContent,
        format: 'rtf',
        targetFormat: 'markdown'
      }
    });
    
    expect(response.status()).toBe(200);
    
    const body = await response.json();
    expect(body).toHaveProperty('status', 'success');
    expect(body).toHaveProperty('data');
    expect(body.data).toHaveProperty('content');
    expect(body.data).toHaveProperty('metadata');
  });

  test('should handle validation requests', async ({ request }) => {
    const response = await request.post(`${baseURL}/api/validate`, {
      headers: {
        'X-API-Key': 'test-api-key'
      },
      data: {
        content: '# Valid Markdown Content',
        format: 'markdown'
      }
    });
    
    expect(response.status()).toBe(200);
    
    const body = await response.json();
    expect(body).toHaveProperty('status', 'success');
    expect(body).toHaveProperty('data');
    expect(body.data).toHaveProperty('valid');
  });

  test('should return server metrics', async ({ request }) => {
    const response = await request.get(`${baseURL}/api/metrics`, {
      headers: {
        'X-API-Key': 'test-api-key'
      }
    });
    
    expect(response.status()).toBe(200);
    
    const body = await response.json();
    expect(body).toHaveProperty('status', 'success');
    expect(body).toHaveProperty('data');
    expect(body.data).toHaveProperty('requests');
    expect(body.data).toHaveProperty('conversions');
  });

  test('should handle rate limiting', async ({ request }) => {
    // Make multiple rapid requests to test rate limiting
    const promises = Array.from({ length: 10 }, () =>
      request.get(`${baseURL}/health`)
    );
    
    const responses = await Promise.all(promises);
    
    // All should succeed since we set a high rate limit for testing
    responses.forEach(response => {
      expect(response.status()).toBeLessThan(500);
    });
  });

  test('should handle CORS preflight requests', async ({ request }) => {
    const response = await request.fetch(`${baseURL}/api/convert`, {
      method: 'OPTIONS',
      headers: {
        'Origin': 'http://localhost:3000',
        'Access-Control-Request-Method': 'POST',
        'Access-Control-Request-Headers': 'Content-Type,X-API-Key'
      }
    });
    
    expect(response.status()).toBe(200);
    expect(response.headers()['access-control-allow-origin']).toBeTruthy();
    expect(response.headers()['access-control-allow-methods']).toBeTruthy();
  });

  test('should handle invalid JSON gracefully', async ({ request }) => {
    const response = await request.post(`${baseURL}/api/convert`, {
      headers: {
        'X-API-Key': 'test-api-key',
        'Content-Type': 'application/json'
      },
      data: 'invalid json content'
    });
    
    expect(response.status()).toBe(400);
    
    const body = await response.json();
    expect(body).toHaveProperty('status', 'error');
  });

  test('should handle large content within limits', async ({ request }) => {
    const largeContent = 'A'.repeat(1000); // 1KB content
    
    const response = await request.post(`${baseURL}/api/convert`, {
      headers: {
        'X-API-Key': 'test-api-key'
      },
      data: {
        content: largeContent,
        format: 'markdown',
        targetFormat: 'rtf'
      }
    });
    
    expect(response.status()).toBe(200);
  });

  test('should return proper error for unsupported format', async ({ request }) => {
    const response = await request.post(`${baseURL}/api/convert`, {
      headers: {
        'X-API-Key': 'test-api-key'
      },
      data: {
        content: 'test content',
        format: 'unsupported-format'
      }
    });
    
    expect(response.status()).toBe(400);
    
    const body = await response.json();
    expect(body).toHaveProperty('status', 'error');
    expect(body).toHaveProperty('errorCode', 'VALIDATION_ERROR');
  });

});
