# MCP Server Tests

This directory contains tests for the LegacyBridge MCP (Model Context Protocol) server implementation.

## Test Structure

The tests are organized into the following directories:

- `unit/`: Unit tests for individual components
  - `core/`: Tests for the core server implementation
  - `middleware/`: Tests for middleware components
  - `routes/`: Tests for API routes
  - `services/`: Tests for service components
  - `utils/`: Tests for utility functions
- `integration/`: Integration tests for API endpoints and server functionality
- `mocks/`: Mock implementations for testing

## Running Tests

To run the tests, use the following commands:

```bash
# Run all tests
npm test

# Run unit tests only
npm run test:unit

# Run integration tests only
npm run test:integration

# Run tests with coverage report
npm run test:coverage
```

## Test Configuration

The tests use Jest as the testing framework. The configuration is defined in `jest.config.mcp.js` in the project root.

## Mocks

The `mocks/` directory contains mock implementations of various components used in testing:

- `config.mock.ts`: Mock configuration
- `logger.mock.ts`: Mock logger
- `cache.mock.ts`: Mock cache service
- `express.mock.ts`: Mock Express request/response objects

## Adding New Tests

When adding new tests, follow these guidelines:

1. Place unit tests in the appropriate subdirectory under `unit/`
2. Place integration tests in the appropriate subdirectory under `integration/`
3. Use the existing mock implementations when possible
4. Follow the naming convention: `*.test.ts`

## Test Coverage

The test coverage report is generated in the `coverage/mcp-server/` directory when running `npm run test:coverage`. The coverage thresholds are defined in `jest.config.mcp.js`.

## Continuous Integration

These tests are run as part of the CI/CD pipeline to ensure the MCP server implementation meets quality standards.