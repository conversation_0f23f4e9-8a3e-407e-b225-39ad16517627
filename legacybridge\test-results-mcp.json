{"config": {"configFile": "C:\\dev\\legacy-bridge\\legacybridge\\playwright.config.mcp.ts", "rootDir": "C:/dev/legacy-bridge/legacybridge/tests/mcp-server/playwright", "forbidOnly": false, "fullyParallel": true, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 6}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "playwright-report-mcp"}], ["json", {"outputFile": "test-results-mcp.json"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "C:/dev/legacy-bridge/legacybridge/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "api-tests", "name": "api-tests", "testDir": "C:/dev/legacy-bridge/legacybridge/tests/mcp-server/playwright", "testIgnore": [], "testMatch": ["**/*.spec.ts"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.54.1", "workers": 6, "webServer": null}, "suites": [{"title": "mcp-components.spec.ts", "file": "mcp-components.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "MCP Server Components", "file": "mcp-components.spec.ts", "line": 10, "column": 6, "specs": [{"title": "MCPConfig should load with default values", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "api-tests", "projectName": "api-tests", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 20, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-30T15:57:08.734Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4123bba5c2a11198ff62-2ee55f2d04658a447667", "file": "mcp-components.spec.ts", "line": 12, "column": 7}, {"title": "MCPLogger should create log entries", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "api-tests", "projectName": "api-tests", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "passed", "duration": 81, "errors": [], "stdout": [{"text": "2025-07-30T15:57:08.880Z [\u001b[31merror\u001b[39m]: Test error message {\"service\":\"mcp-server\"}\r\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-30T15:57:08.794Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4123bba5c2a11198ff62-8e81179c20651697c71c", "file": "mcp-components.spec.ts", "line": 24, "column": 7}, {"title": "MCPCache should handle memory caching", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "api-tests", "projectName": "api-tests", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "passed", "duration": 20, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-30T15:57:08.855Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4123bba5c2a11198ff62-f4f09f83377baa60cefb", "file": "mcp-components.spec.ts", "line": 36, "column": 7}, {"title": "MCPCache should handle disabled state", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "api-tests", "projectName": "api-tests", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "passed", "duration": 11, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-30T15:57:08.730Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4123bba5c2a11198ff62-fdd41fb10daf50dfca16", "file": "mcp-components.spec.ts", "line": 57, "column": 7}, {"title": "MCPMetricsCollector should track metrics", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "api-tests", "projectName": "api-tests", "results": [{"workerIndex": 4, "parallelIndex": 4, "status": "passed", "duration": 20, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-30T15:57:08.726Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4123bba5c2a11198ff62-cb64752a25755d0cf5a2", "file": "mcp-components.spec.ts", "line": 73, "column": 7}, {"title": "MCPMetricsCollector should calculate success rates", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "api-tests", "projectName": "api-tests", "results": [{"workerIndex": 5, "parallelIndex": 5, "status": "passed", "duration": 18, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-30T15:57:08.760Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4123bba5c2a11198ff62-53d92b3e755554067dc9", "file": "mcp-components.spec.ts", "line": 102, "column": 7}, {"title": "MCPConfig should validate configuration", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "api-tests", "projectName": "api-tests", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "passed", "duration": 9, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-30T15:57:08.779Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4123bba5c2a11198ff62-befcd09b221ae51fc49e", "file": "mcp-components.spec.ts", "line": 128, "column": 7}, {"title": "MCPCache should handle cache key generation", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "api-tests", "projectName": "api-tests", "results": [{"workerIndex": 4, "parallelIndex": 4, "status": "passed", "duration": 6, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-30T15:57:08.779Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4123bba5c2a11198ff62-5913ca32ce5af8d5bf10", "file": "mcp-components.spec.ts", "line": 138, "column": 7}, {"title": "MCPLogger should handle different log levels", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "api-tests", "projectName": "api-tests", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 32, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-30T15:57:08.806Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4123bba5c2a11198ff62-d88115ed69cdf324e928", "file": "mcp-components.spec.ts", "line": 162, "column": 7}, {"title": "MCPMetricsCollector should handle batch metrics", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "api-tests", "projectName": "api-tests", "results": [{"workerIndex": 5, "parallelIndex": 5, "status": "passed", "duration": 7, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-30T15:57:08.807Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4123bba5c2a11198ff62-b6fcbbe69abb205f30fd", "file": "mcp-components.spec.ts", "line": 176, "column": 7}, {"title": "MCPConfig should handle environment variables", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "api-tests", "projectName": "api-tests", "results": [{"workerIndex": 4, "parallelIndex": 4, "status": "passed", "duration": 4, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-30T15:57:08.808Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4123bba5c2a11198ff62-e784c969296d14132f87", "file": "mcp-components.spec.ts", "line": 197, "column": 7}]}]}, {"title": "mcp-server-integration.spec.ts", "file": "mcp-server-integration.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "MCP Server Integration Tests", "file": "mcp-server-integration.spec.ts", "line": 45, "column": 6, "specs": [{"title": "should respond to health check endpoint", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "api-tests", "projectName": "api-tests", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "failed", "duration": 355, "error": {"message": "Error: apiRequestContext.get: connect ECONNREFUSED ::1:3032\nCall log:\n\u001b[2m  - → GET http://localhost:3032/health\u001b[22m\n\u001b[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36\u001b[22m\n\u001b[2m    - accept: application/json\u001b[22m\n\u001b[2m    - accept-encoding: gzip,deflate,br\u001b[22m\n\u001b[2m    - Content-Type: application/json\u001b[22m\n", "stack": "Error: apiRequestContext.get: connect ECONNREFUSED ::1:3032\nCall log:\n\u001b[2m  - → GET http://localhost:3032/health\u001b[22m\n\u001b[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36\u001b[22m\n\u001b[2m    - accept: application/json\u001b[22m\n\u001b[2m    - accept-encoding: gzip,deflate,br\u001b[22m\n\u001b[2m    - Content-Type: application/json\u001b[22m\n\n    at apiRequestContext.get: connect ECONNREFUSED ::1:3032\n    at C:\\dev\\legacy-bridge\\legacybridge\\tests\\mcp-server\\playwright\\mcp-server-integration.spec.ts:60:36", "location": {"file": "apiRequestContext.get: connect ECONNREFUSED :", "column": 3032, "line": 1}}, "errors": [{"location": {"file": "apiRequestContext.get: connect ECONNREFUSED :", "column": 3032, "line": 1}, "message": "Error: apiRequestContext.get: connect ECONNREFUSED ::1:3032\nCall log:\n\u001b[2m  - → GET http://localhost:3032/health\u001b[22m\n\u001b[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36\u001b[22m\n\u001b[2m    - accept: application/json\u001b[22m\n\u001b[2m    - accept-encoding: gzip,deflate,br\u001b[22m\n\u001b[2m    - Content-Type: application/json\u001b[22m\n\n\u001b[2m    at apiRequestContext.get: connect ECONNREFUSED ::1:3032\u001b[22m\n\u001b[2m    at C:\\dev\\legacy-bridge\\legacybridge\\tests\\mcp-server\\playwright\\mcp-server-integration.spec.ts:60:36\u001b[22m"}], "stdout": [{"text": "🧪 Testing MCP Server components...\n"}, {"text": "✅ MCP Server component tests complete\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-30T15:57:08.814Z", "annotations": [], "attachments": [], "errorLocation": {"file": "apiRequestContext.get: connect ECONNREFUSED :", "column": 3032, "line": 1}}], "status": "unexpected"}], "id": "5f90dd8b44b8d2537767-de9852748e2c3724f982", "file": "mcp-server-integration.spec.ts", "line": 59, "column": 7}, {"title": "should require authentication for protected endpoints", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "api-tests", "projectName": "api-tests", "results": [{"workerIndex": 6, "parallelIndex": 3, "status": "failed", "duration": 347, "error": {"message": "Error: apiRequestContext.post: connect ECONNREFUSED ::1:3032\nCall log:\n\u001b[2m  - → POST http://localhost:3032/api/convert\u001b[22m\n\u001b[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36\u001b[22m\n\u001b[2m    - accept: application/json\u001b[22m\n\u001b[2m    - accept-encoding: gzip,deflate,br\u001b[22m\n\u001b[2m    - Content-Type: application/json\u001b[22m\n\u001b[2m    - content-length: 41\u001b[22m\n", "stack": "Error: apiRequestContext.post: connect ECONNREFUSED ::1:3032\nCall log:\n\u001b[2m  - → POST http://localhost:3032/api/convert\u001b[22m\n\u001b[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36\u001b[22m\n\u001b[2m    - accept: application/json\u001b[22m\n\u001b[2m    - accept-encoding: gzip,deflate,br\u001b[22m\n\u001b[2m    - Content-Type: application/json\u001b[22m\n\u001b[2m    - content-length: 41\u001b[22m\n\n    at apiRequestContext.post: connect ECONNREFUSED ::1:3032\n    at C:\\dev\\legacy-bridge\\legacybridge\\tests\\mcp-server\\playwright\\mcp-server-integration.spec.ts:72:36", "location": {"file": "apiRequestContext.post: connect ECONNREFUSED :", "column": 3032, "line": 1}}, "errors": [{"location": {"file": "apiRequestContext.post: connect ECONNREFUSED :", "column": 3032, "line": 1}, "message": "Error: apiRequestContext.post: connect ECONNREFUSED ::1:3032\nCall log:\n\u001b[2m  - → POST http://localhost:3032/api/convert\u001b[22m\n\u001b[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36\u001b[22m\n\u001b[2m    - accept: application/json\u001b[22m\n\u001b[2m    - accept-encoding: gzip,deflate,br\u001b[22m\n\u001b[2m    - Content-Type: application/json\u001b[22m\n\u001b[2m    - content-length: 41\u001b[22m\n\n\u001b[2m    at apiRequestContext.post: connect ECONNREFUSED ::1:3032\u001b[22m\n\u001b[2m    at C:\\dev\\legacy-bridge\\legacybridge\\tests\\mcp-server\\playwright\\mcp-server-integration.spec.ts:72:36\u001b[22m"}], "stdout": [{"text": "🧪 Testing MCP Server components...\n"}, {"text": "✅ MCP Server component tests complete\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-30T15:57:10.764Z", "annotations": [], "attachments": [], "errorLocation": {"file": "apiRequestContext.post: connect ECONNREFUSED :", "column": 3032, "line": 1}}], "status": "unexpected"}], "id": "5f90dd8b44b8d2537767-be4fc45b09e53ce752ab", "file": "mcp-server-integration.spec.ts", "line": 70, "column": 7}, {"title": "should accept valid API key", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "api-tests", "projectName": "api-tests", "results": [{"workerIndex": 4, "parallelIndex": 4, "status": "failed", "duration": 344, "error": {"message": "Error: apiRequestContext.post: connect ECONNREFUSED ::1:3032\nCall log:\n\u001b[2m  - → POST http://localhost:3032/api/convert\u001b[22m\n\u001b[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36\u001b[22m\n\u001b[2m    - accept: application/json\u001b[22m\n\u001b[2m    - accept-encoding: gzip,deflate,br\u001b[22m\n\u001b[2m    - Content-Type: application/json\u001b[22m\n\u001b[2m    - X-API-Key: test-api-key\u001b[22m\n\u001b[2m    - content-length: 58\u001b[22m\n", "stack": "Error: apiRequestContext.post: connect ECONNREFUSED ::1:3032\nCall log:\n\u001b[2m  - → POST http://localhost:3032/api/convert\u001b[22m\n\u001b[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36\u001b[22m\n\u001b[2m    - accept: application/json\u001b[22m\n\u001b[2m    - accept-encoding: gzip,deflate,br\u001b[22m\n\u001b[2m    - Content-Type: application/json\u001b[22m\n\u001b[2m    - X-API-Key: test-api-key\u001b[22m\n\u001b[2m    - content-length: 58\u001b[22m\n\n    at apiRequestContext.post: connect ECONNREFUSED ::1:3032\n    at C:\\dev\\legacy-bridge\\legacybridge\\tests\\mcp-server\\playwright\\mcp-server-integration.spec.ts:87:36", "location": {"file": "apiRequestContext.post: connect ECONNREFUSED :", "column": 3032, "line": 1}}, "errors": [{"location": {"file": "apiRequestContext.post: connect ECONNREFUSED :", "column": 3032, "line": 1}, "message": "Error: apiRequestContext.post: connect ECONNREFUSED ::1:3032\nCall log:\n\u001b[2m  - → POST http://localhost:3032/api/convert\u001b[22m\n\u001b[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36\u001b[22m\n\u001b[2m    - accept: application/json\u001b[22m\n\u001b[2m    - accept-encoding: gzip,deflate,br\u001b[22m\n\u001b[2m    - Content-Type: application/json\u001b[22m\n\u001b[2m    - X-API-Key: test-api-key\u001b[22m\n\u001b[2m    - content-length: 58\u001b[22m\n\n\u001b[2m    at apiRequestContext.post: connect ECONNREFUSED ::1:3032\u001b[22m\n\u001b[2m    at C:\\dev\\legacy-bridge\\legacybridge\\tests\\mcp-server\\playwright\\mcp-server-integration.spec.ts:87:36\u001b[22m"}], "stdout": [{"text": "🧪 Testing MCP Server components...\n"}, {"text": "✅ MCP Server component tests complete\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-30T15:57:08.863Z", "annotations": [], "attachments": [], "errorLocation": {"file": "apiRequestContext.post: connect ECONNREFUSED :", "column": 3032, "line": 1}}], "status": "unexpected"}], "id": "5f90dd8b44b8d2537767-99d058e433d9b19489ef", "file": "mcp-server-integration.spec.ts", "line": 86, "column": 7}, {"title": "should handle RTF to Markdown conversion", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "api-tests", "projectName": "api-tests", "results": [{"workerIndex": 7, "parallelIndex": 4, "status": "failed", "duration": 356, "error": {"message": "Error: apiRequestContext.post: connect ECONNREFUSED ::1:3032\nCall log:\n\u001b[2m  - → POST http://localhost:3032/api/convert\u001b[22m\n\u001b[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36\u001b[22m\n\u001b[2m    - accept: application/json\u001b[22m\n\u001b[2m    - accept-encoding: gzip,deflate,br\u001b[22m\n\u001b[2m    - Content-Type: application/json\u001b[22m\n\u001b[2m    - X-API-Key: test-api-key\u001b[22m\n\u001b[2m    - content-length: 136\u001b[22m\n", "stack": "Error: apiRequestContext.post: connect ECONNREFUSED ::1:3032\nCall log:\n\u001b[2m  - → POST http://localhost:3032/api/convert\u001b[22m\n\u001b[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36\u001b[22m\n\u001b[2m    - accept: application/json\u001b[22m\n\u001b[2m    - accept-encoding: gzip,deflate,br\u001b[22m\n\u001b[2m    - Content-Type: application/json\u001b[22m\n\u001b[2m    - X-API-Key: test-api-key\u001b[22m\n\u001b[2m    - content-length: 136\u001b[22m\n\n    at apiRequestContext.post: connect ECONNREFUSED ::1:3032\n    at C:\\dev\\legacy-bridge\\legacybridge\\tests\\mcp-server\\playwright\\mcp-server-integration.spec.ts:104:36", "location": {"file": "apiRequestContext.post: connect ECONNREFUSED :", "column": 3032, "line": 1}}, "errors": [{"location": {"file": "apiRequestContext.post: connect ECONNREFUSED :", "column": 3032, "line": 1}, "message": "Error: apiRequestContext.post: connect ECONNREFUSED ::1:3032\nCall log:\n\u001b[2m  - → POST http://localhost:3032/api/convert\u001b[22m\n\u001b[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36\u001b[22m\n\u001b[2m    - accept: application/json\u001b[22m\n\u001b[2m    - accept-encoding: gzip,deflate,br\u001b[22m\n\u001b[2m    - Content-Type: application/json\u001b[22m\n\u001b[2m    - X-API-Key: test-api-key\u001b[22m\n\u001b[2m    - content-length: 136\u001b[22m\n\n\u001b[2m    at apiRequestContext.post: connect ECONNREFUSED ::1:3032\u001b[22m\n\u001b[2m    at C:\\dev\\legacy-bridge\\legacybridge\\tests\\mcp-server\\playwright\\mcp-server-integration.spec.ts:104:36\u001b[22m"}], "stdout": [{"text": "🧪 Testing MCP Server components...\n"}, {"text": "✅ MCP Server component tests complete\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-30T15:57:10.791Z", "annotations": [], "attachments": [], "errorLocation": {"file": "apiRequestContext.post: connect ECONNREFUSED :", "column": 3032, "line": 1}}], "status": "unexpected"}], "id": "5f90dd8b44b8d2537767-2ef6a9d0a8060c5ad7c9", "file": "mcp-server-integration.spec.ts", "line": 101, "column": 7}, {"title": "should handle validation requests", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "api-tests", "projectName": "api-tests", "results": [{"workerIndex": 5, "parallelIndex": 5, "status": "failed", "duration": 362, "error": {"message": "Error: apiRequestContext.post: connect ECONNREFUSED ::1:3032\nCall log:\n\u001b[2m  - → POST http://localhost:3032/api/validate\u001b[22m\n\u001b[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36\u001b[22m\n\u001b[2m    - accept: application/json\u001b[22m\n\u001b[2m    - accept-encoding: gzip,deflate,br\u001b[22m\n\u001b[2m    - Content-Type: application/json\u001b[22m\n\u001b[2m    - X-API-Key: test-api-key\u001b[22m\n\u001b[2m    - content-length: 58\u001b[22m\n", "stack": "Error: apiRequestContext.post: connect ECONNREFUSED ::1:3032\nCall log:\n\u001b[2m  - → POST http://localhost:3032/api/validate\u001b[22m\n\u001b[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36\u001b[22m\n\u001b[2m    - accept: application/json\u001b[22m\n\u001b[2m    - accept-encoding: gzip,deflate,br\u001b[22m\n\u001b[2m    - Content-Type: application/json\u001b[22m\n\u001b[2m    - X-API-Key: test-api-key\u001b[22m\n\u001b[2m    - content-length: 58\u001b[22m\n\n    at apiRequestContext.post: connect ECONNREFUSED ::1:3032\n    at C:\\dev\\legacy-bridge\\legacybridge\\tests\\mcp-server\\playwright\\mcp-server-integration.spec.ts:125:36", "location": {"file": "apiRequestContext.post: connect ECONNREFUSED :", "column": 3032, "line": 1}}, "errors": [{"location": {"file": "apiRequestContext.post: connect ECONNREFUSED :", "column": 3032, "line": 1}, "message": "Error: apiRequestContext.post: connect ECONNREFUSED ::1:3032\nCall log:\n\u001b[2m  - → POST http://localhost:3032/api/validate\u001b[22m\n\u001b[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36\u001b[22m\n\u001b[2m    - accept: application/json\u001b[22m\n\u001b[2m    - accept-encoding: gzip,deflate,br\u001b[22m\n\u001b[2m    - Content-Type: application/json\u001b[22m\n\u001b[2m    - X-API-Key: test-api-key\u001b[22m\n\u001b[2m    - content-length: 58\u001b[22m\n\n\u001b[2m    at apiRequestContext.post: connect ECONNREFUSED ::1:3032\u001b[22m\n\u001b[2m    at C:\\dev\\legacy-bridge\\legacybridge\\tests\\mcp-server\\playwright\\mcp-server-integration.spec.ts:125:36\u001b[22m"}], "stdout": [{"text": "🧪 Testing MCP Server components...\n"}, {"text": "✅ MCP Server component tests complete\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-30T15:57:08.863Z", "annotations": [], "attachments": [], "errorLocation": {"file": "apiRequestContext.post: connect ECONNREFUSED :", "column": 3032, "line": 1}}], "status": "unexpected"}], "id": "5f90dd8b44b8d2537767-1a14a58b16ab73cfb57c", "file": "mcp-server-integration.spec.ts", "line": 124, "column": 7}, {"title": "should return server metrics", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "api-tests", "projectName": "api-tests", "results": [{"workerIndex": 8, "parallelIndex": 5, "status": "failed", "duration": 357, "error": {"message": "Error: apiRequestContext.get: connect ECONNREFUSED ::1:3032\nCall log:\n\u001b[2m  - → GET http://localhost:3032/api/metrics\u001b[22m\n\u001b[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36\u001b[22m\n\u001b[2m    - accept: application/json\u001b[22m\n\u001b[2m    - accept-encoding: gzip,deflate,br\u001b[22m\n\u001b[2m    - Content-Type: application/json\u001b[22m\n\u001b[2m    - X-API-Key: test-api-key\u001b[22m\n", "stack": "Error: apiRequestContext.get: connect ECONNREFUSED ::1:3032\nCall log:\n\u001b[2m  - → GET http://localhost:3032/api/metrics\u001b[22m\n\u001b[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36\u001b[22m\n\u001b[2m    - accept: application/json\u001b[22m\n\u001b[2m    - accept-encoding: gzip,deflate,br\u001b[22m\n\u001b[2m    - Content-Type: application/json\u001b[22m\n\u001b[2m    - X-API-Key: test-api-key\u001b[22m\n\n    at apiRequestContext.get: connect ECONNREFUSED ::1:3032\n    at C:\\dev\\legacy-bridge\\legacybridge\\tests\\mcp-server\\playwright\\mcp-server-integration.spec.ts:144:36", "location": {"file": "apiRequestContext.get: connect ECONNREFUSED :", "column": 3032, "line": 1}}, "errors": [{"location": {"file": "apiRequestContext.get: connect ECONNREFUSED :", "column": 3032, "line": 1}, "message": "Error: apiRequestContext.get: connect ECONNREFUSED ::1:3032\nCall log:\n\u001b[2m  - → GET http://localhost:3032/api/metrics\u001b[22m\n\u001b[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36\u001b[22m\n\u001b[2m    - accept: application/json\u001b[22m\n\u001b[2m    - accept-encoding: gzip,deflate,br\u001b[22m\n\u001b[2m    - Content-Type: application/json\u001b[22m\n\u001b[2m    - X-API-Key: test-api-key\u001b[22m\n\n\u001b[2m    at apiRequestContext.get: connect ECONNREFUSED ::1:3032\u001b[22m\n\u001b[2m    at C:\\dev\\legacy-bridge\\legacybridge\\tests\\mcp-server\\playwright\\mcp-server-integration.spec.ts:144:36\u001b[22m"}], "stdout": [{"text": "🧪 Testing MCP Server components...\n"}, {"text": "✅ MCP Server component tests complete\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-30T15:57:10.785Z", "annotations": [], "attachments": [], "errorLocation": {"file": "apiRequestContext.get: connect ECONNREFUSED :", "column": 3032, "line": 1}}], "status": "unexpected"}], "id": "5f90dd8b44b8d2537767-2486547cfaf293b37080", "file": "mcp-server-integration.spec.ts", "line": 143, "column": 7}, {"title": "should handle rate limiting", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "api-tests", "projectName": "api-tests", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "failed", "duration": 399, "error": {"message": "Error: apiRequestContext.get: connect ECONNREFUSED ::1:3032\nCall log:\n\u001b[2m  - → GET http://localhost:3032/health\u001b[22m\n\u001b[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36\u001b[22m\n\u001b[2m    - accept: application/json\u001b[22m\n\u001b[2m    - accept-encoding: gzip,deflate,br\u001b[22m\n\u001b[2m    - Content-Type: application/json\u001b[22m\n", "stack": "Error: apiRequestContext.get: connect ECONNREFUSED ::1:3032\nCall log:\n\u001b[2m  - → GET http://localhost:3032/health\u001b[22m\n\u001b[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36\u001b[22m\n\u001b[2m    - accept: application/json\u001b[22m\n\u001b[2m    - accept-encoding: gzip,deflate,br\u001b[22m\n\u001b[2m    - Content-Type: application/json\u001b[22m\n\n    at apiRequestContext.get: connect ECONNREFUSED ::1:3032\n    at from (C:\\dev\\legacy-bridge\\legacybridge\\tests\\mcp-server\\playwright\\mcp-server-integration.spec.ts:162:15)\n    at C:\\dev\\legacy-bridge\\legacybridge\\tests\\mcp-server\\playwright\\mcp-server-integration.spec.ts:161:28", "location": {"file": "apiRequestContext.get: connect ECONNREFUSED :", "column": 3032, "line": 1}}, "errors": [{"location": {"file": "apiRequestContext.get: connect ECONNREFUSED :", "column": 3032, "line": 1}, "message": "Error: apiRequestContext.get: connect ECONNREFUSED ::1:3032\nCall log:\n\u001b[2m  - → GET http://localhost:3032/health\u001b[22m\n\u001b[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36\u001b[22m\n\u001b[2m    - accept: application/json\u001b[22m\n\u001b[2m    - accept-encoding: gzip,deflate,br\u001b[22m\n\u001b[2m    - Content-Type: application/json\u001b[22m\n\n\u001b[2m    at apiRequestContext.get: connect ECONNREFUSED ::1:3032\u001b[22m\n\u001b[2m    at from (C:\\dev\\legacy-bridge\\legacybridge\\tests\\mcp-server\\playwright\\mcp-server-integration.spec.ts:162:15)\u001b[22m\n\u001b[2m    at C:\\dev\\legacy-bridge\\legacybridge\\tests\\mcp-server\\playwright\\mcp-server-integration.spec.ts:161:28\u001b[22m"}], "stdout": [{"text": "🧪 Testing MCP Server components...\n"}, {"text": "✅ MCP Server component tests complete\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-30T15:57:08.872Z", "annotations": [], "attachments": [], "errorLocation": {"file": "apiRequestContext.get: connect ECONNREFUSED :", "column": 3032, "line": 1}}], "status": "unexpected"}], "id": "5f90dd8b44b8d2537767-f7e48d486a0da7ad5d8c", "file": "mcp-server-integration.spec.ts", "line": 159, "column": 7}, {"title": "should handle CORS preflight requests", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "api-tests", "projectName": "api-tests", "results": [{"workerIndex": 9, "parallelIndex": 0, "status": "failed", "duration": 351, "error": {"message": "Error: apiRequestContext.fetch: connect ECONNREFUSED ::1:3032\nCall log:\n\u001b[2m  - → OPTIONS http://localhost:3032/api/convert\u001b[22m\n\u001b[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36\u001b[22m\n\u001b[2m    - accept: application/json\u001b[22m\n\u001b[2m    - accept-encoding: gzip,deflate,br\u001b[22m\n\u001b[2m    - Content-Type: application/json\u001b[22m\n\u001b[2m    - Origin: http://localhost:3000\u001b[22m\n\u001b[2m    - Access-Control-Request-Method: POST\u001b[22m\n\u001b[2m    - Access-Control-Request-Headers: Content-Type,X-API-Key\u001b[22m\n", "stack": "Error: apiRequestContext.fetch: connect ECONNREFUSED ::1:3032\nCall log:\n\u001b[2m  - → OPTIONS http://localhost:3032/api/convert\u001b[22m\n\u001b[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36\u001b[22m\n\u001b[2m    - accept: application/json\u001b[22m\n\u001b[2m    - accept-encoding: gzip,deflate,br\u001b[22m\n\u001b[2m    - Content-Type: application/json\u001b[22m\n\u001b[2m    - Origin: http://localhost:3000\u001b[22m\n\u001b[2m    - Access-Control-Request-Method: POST\u001b[22m\n\u001b[2m    - Access-Control-Request-Headers: Content-Type,X-API-Key\u001b[22m\n\n    at apiRequestContext.fetch: connect ECONNREFUSED ::1:3032\n    at C:\\dev\\legacy-bridge\\legacybridge\\tests\\mcp-server\\playwright\\mcp-server-integration.spec.ts:174:36", "location": {"file": "apiRequestContext.fetch: connect ECONNREFUSED :", "column": 3032, "line": 1}}, "errors": [{"location": {"file": "apiRequestContext.fetch: connect ECONNREFUSED :", "column": 3032, "line": 1}, "message": "Error: apiRequestContext.fetch: connect ECONNREFUSED ::1:3032\nCall log:\n\u001b[2m  - → OPTIONS http://localhost:3032/api/convert\u001b[22m\n\u001b[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36\u001b[22m\n\u001b[2m    - accept: application/json\u001b[22m\n\u001b[2m    - accept-encoding: gzip,deflate,br\u001b[22m\n\u001b[2m    - Content-Type: application/json\u001b[22m\n\u001b[2m    - Origin: http://localhost:3000\u001b[22m\n\u001b[2m    - Access-Control-Request-Method: POST\u001b[22m\n\u001b[2m    - Access-Control-Request-Headers: Content-Type,X-API-Key\u001b[22m\n\n\u001b[2m    at apiRequestContext.fetch: connect ECONNREFUSED ::1:3032\u001b[22m\n\u001b[2m    at C:\\dev\\legacy-bridge\\legacybridge\\tests\\mcp-server\\playwright\\mcp-server-integration.spec.ts:174:36\u001b[22m"}], "stdout": [{"text": "🧪 Testing MCP Server components...\n"}, {"text": "✅ MCP Server component tests complete\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-30T15:57:10.791Z", "annotations": [], "attachments": [], "errorLocation": {"file": "apiRequestContext.fetch: connect ECONNREFUSED :", "column": 3032, "line": 1}}], "status": "unexpected"}], "id": "5f90dd8b44b8d2537767-8fced9efd1574290d39a", "file": "mcp-server-integration.spec.ts", "line": 173, "column": 7}, {"title": "should handle invalid JSON gracefully", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "api-tests", "projectName": "api-tests", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "failed", "duration": 376, "error": {"message": "Error: apiRequestContext.post: connect ECONNREFUSED ::1:3032\nCall log:\n\u001b[2m  - → POST http://localhost:3032/api/convert\u001b[22m\n\u001b[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36\u001b[22m\n\u001b[2m    - accept: application/json\u001b[22m\n\u001b[2m    - accept-encoding: gzip,deflate,br\u001b[22m\n\u001b[2m    - Content-Type: application/json\u001b[22m\n\u001b[2m    - X-API-Key: test-api-key\u001b[22m\n\u001b[2m    - content-length: 22\u001b[22m\n", "stack": "Error: apiRequestContext.post: connect ECONNREFUSED ::1:3032\nCall log:\n\u001b[2m  - → POST http://localhost:3032/api/convert\u001b[22m\n\u001b[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36\u001b[22m\n\u001b[2m    - accept: application/json\u001b[22m\n\u001b[2m    - accept-encoding: gzip,deflate,br\u001b[22m\n\u001b[2m    - Content-Type: application/json\u001b[22m\n\u001b[2m    - X-API-Key: test-api-key\u001b[22m\n\u001b[2m    - content-length: 22\u001b[22m\n\n    at apiRequestContext.post: connect ECONNREFUSED ::1:3032\n    at C:\\dev\\legacy-bridge\\legacybridge\\tests\\mcp-server\\playwright\\mcp-server-integration.spec.ts:189:36", "location": {"file": "apiRequestContext.post: connect ECONNREFUSED :", "column": 3032, "line": 1}}, "errors": [{"location": {"file": "apiRequestContext.post: connect ECONNREFUSED :", "column": 3032, "line": 1}, "message": "Error: apiRequestContext.post: connect ECONNREFUSED ::1:3032\nCall log:\n\u001b[2m  - → POST http://localhost:3032/api/convert\u001b[22m\n\u001b[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36\u001b[22m\n\u001b[2m    - accept: application/json\u001b[22m\n\u001b[2m    - accept-encoding: gzip,deflate,br\u001b[22m\n\u001b[2m    - Content-Type: application/json\u001b[22m\n\u001b[2m    - X-API-Key: test-api-key\u001b[22m\n\u001b[2m    - content-length: 22\u001b[22m\n\n\u001b[2m    at apiRequestContext.post: connect ECONNREFUSED ::1:3032\u001b[22m\n\u001b[2m    at C:\\dev\\legacy-bridge\\legacybridge\\tests\\mcp-server\\playwright\\mcp-server-integration.spec.ts:189:36\u001b[22m"}], "stdout": [{"text": "🧪 Testing MCP Server components...\n"}, {"text": "✅ MCP Server component tests complete\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-30T15:57:08.938Z", "annotations": [], "attachments": [], "errorLocation": {"file": "apiRequestContext.post: connect ECONNREFUSED :", "column": 3032, "line": 1}}], "status": "unexpected"}], "id": "5f90dd8b44b8d2537767-3872fc74b27450ca09c1", "file": "mcp-server-integration.spec.ts", "line": 188, "column": 7}, {"title": "should handle large content within limits", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "api-tests", "projectName": "api-tests", "results": [{"workerIndex": 10, "parallelIndex": 1, "status": "failed", "duration": 338, "error": {"message": "Error: apiRequestContext.post: connect ECONNREFUSED ::1:3032\nCall log:\n\u001b[2m  - → POST http://localhost:3032/api/convert\u001b[22m\n\u001b[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36\u001b[22m\n\u001b[2m    - accept: application/json\u001b[22m\n\u001b[2m    - accept-encoding: gzip,deflate,br\u001b[22m\n\u001b[2m    - Content-Type: application/json\u001b[22m\n\u001b[2m    - X-API-Key: test-api-key\u001b[22m\n\u001b[2m    - content-length: 1055\u001b[22m\n", "stack": "Error: apiRequestContext.post: connect ECONNREFUSED ::1:3032\nCall log:\n\u001b[2m  - → POST http://localhost:3032/api/convert\u001b[22m\n\u001b[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36\u001b[22m\n\u001b[2m    - accept: application/json\u001b[22m\n\u001b[2m    - accept-encoding: gzip,deflate,br\u001b[22m\n\u001b[2m    - Content-Type: application/json\u001b[22m\n\u001b[2m    - X-API-Key: test-api-key\u001b[22m\n\u001b[2m    - content-length: 1055\u001b[22m\n\n    at apiRequestContext.post: connect ECONNREFUSED ::1:3032\n    at C:\\dev\\legacy-bridge\\legacybridge\\tests\\mcp-server\\playwright\\mcp-server-integration.spec.ts:206:36", "location": {"file": "apiRequestContext.post: connect ECONNREFUSED :", "column": 3032, "line": 1}}, "errors": [{"location": {"file": "apiRequestContext.post: connect ECONNREFUSED :", "column": 3032, "line": 1}, "message": "Error: apiRequestContext.post: connect ECONNREFUSED ::1:3032\nCall log:\n\u001b[2m  - → POST http://localhost:3032/api/convert\u001b[22m\n\u001b[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36\u001b[22m\n\u001b[2m    - accept: application/json\u001b[22m\n\u001b[2m    - accept-encoding: gzip,deflate,br\u001b[22m\n\u001b[2m    - Content-Type: application/json\u001b[22m\n\u001b[2m    - X-API-Key: test-api-key\u001b[22m\n\u001b[2m    - content-length: 1055\u001b[22m\n\n\u001b[2m    at apiRequestContext.post: connect ECONNREFUSED ::1:3032\u001b[22m\n\u001b[2m    at C:\\dev\\legacy-bridge\\legacybridge\\tests\\mcp-server\\playwright\\mcp-server-integration.spec.ts:206:36\u001b[22m"}], "stdout": [{"text": "🧪 Testing MCP Server components...\n"}, {"text": "✅ MCP Server component tests complete\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-30T15:57:10.858Z", "annotations": [], "attachments": [], "errorLocation": {"file": "apiRequestContext.post: connect ECONNREFUSED :", "column": 3032, "line": 1}}], "status": "unexpected"}], "id": "5f90dd8b44b8d2537767-27439badf12e2ae3270e", "file": "mcp-server-integration.spec.ts", "line": 203, "column": 7}, {"title": "should return proper error for unsupported format", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "api-tests", "projectName": "api-tests", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "failed", "duration": 363, "error": {"message": "Error: apiRequestContext.post: connect ECONNREFUSED ::1:3032\nCall log:\n\u001b[2m  - → POST http://localhost:3032/api/convert\u001b[22m\n\u001b[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36\u001b[22m\n\u001b[2m    - accept: application/json\u001b[22m\n\u001b[2m    - accept-encoding: gzip,deflate,br\u001b[22m\n\u001b[2m    - Content-Type: application/json\u001b[22m\n\u001b[2m    - X-API-Key: test-api-key\u001b[22m\n\u001b[2m    - content-length: 56\u001b[22m\n", "stack": "Error: apiRequestContext.post: connect ECONNREFUSED ::1:3032\nCall log:\n\u001b[2m  - → POST http://localhost:3032/api/convert\u001b[22m\n\u001b[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36\u001b[22m\n\u001b[2m    - accept: application/json\u001b[22m\n\u001b[2m    - accept-encoding: gzip,deflate,br\u001b[22m\n\u001b[2m    - Content-Type: application/json\u001b[22m\n\u001b[2m    - X-API-Key: test-api-key\u001b[22m\n\u001b[2m    - content-length: 56\u001b[22m\n\n    at apiRequestContext.post: connect ECONNREFUSED ::1:3032\n    at C:\\dev\\legacy-bridge\\legacybridge\\tests\\mcp-server\\playwright\\mcp-server-integration.spec.ts:221:36", "location": {"file": "apiRequestContext.post: connect ECONNREFUSED :", "column": 3032, "line": 1}}, "errors": [{"location": {"file": "apiRequestContext.post: connect ECONNREFUSED :", "column": 3032, "line": 1}, "message": "Error: apiRequestContext.post: connect ECONNREFUSED ::1:3032\nCall log:\n\u001b[2m  - → POST http://localhost:3032/api/convert\u001b[22m\n\u001b[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36\u001b[22m\n\u001b[2m    - accept: application/json\u001b[22m\n\u001b[2m    - accept-encoding: gzip,deflate,br\u001b[22m\n\u001b[2m    - Content-Type: application/json\u001b[22m\n\u001b[2m    - X-API-Key: test-api-key\u001b[22m\n\u001b[2m    - content-length: 56\u001b[22m\n\n\u001b[2m    at apiRequestContext.post: connect ECONNREFUSED ::1:3032\u001b[22m\n\u001b[2m    at C:\\dev\\legacy-bridge\\legacybridge\\tests\\mcp-server\\playwright\\mcp-server-integration.spec.ts:221:36\u001b[22m"}], "stdout": [{"text": "🧪 Testing MCP Server components...\n"}, {"text": "✅ MCP Server component tests complete\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-30T15:57:08.941Z", "annotations": [], "attachments": [], "errorLocation": {"file": "apiRequestContext.post: connect ECONNREFUSED :", "column": 3032, "line": 1}}], "status": "unexpected"}], "id": "5f90dd8b44b8d2537767-ed4c986ad5062d4ed3c7", "file": "mcp-server-integration.spec.ts", "line": 220, "column": 7}]}]}], "errors": [], "stats": {"startTime": "2025-07-30T15:57:06.340Z", "duration": 4900.635, "expected": 11, "skipped": 0, "unexpected": 11, "flaky": 0}}