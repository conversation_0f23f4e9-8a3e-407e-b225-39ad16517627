// Create a shared mock for execPromise that will be used across all tests
const mockExecPromise = jest.fn();

// Mock util - must be before imports
jest.mock('util', () => ({
  ...jest.requireActual('util'),
  promisify: jest.fn().mockReturnValue(mockExecPromise)
}));

// Mock child_process
jest.mock('child_process', () => ({
  exec: jest.fn()
}));

// Mock fs
jest.mock('fs', () => ({
  ...jest.requireActual('fs'),
  promises: {
    readFile: jest.fn(),
    writeFile: jest.fn(),
    unlink: jest.fn(),
    mkdir: jest.fn()
  },
  existsSync: jest.fn(),
  mkdirSync: jest.fn()
}));

// Mock path
jest.mock('path', () => ({
  ...jest.requireActual('path'),
  join: jest.fn().mockImplementation((...args) => args.join('/')),
  basename: jest.fn().mockImplementation((p, ext) => {
    const base = p.split('/').pop();
    if (ext && base.endsWith(ext)) {
      return base.slice(0, -ext.length);
    }
    return base;
  }),
  extname: jest.fn().mockImplementation(p => {
    const parts = p.split('.');
    return parts.length > 1 ? `.${parts.pop()}` : '';
  }),
  dirname: jest.fn().mockImplementation(p => {
    const parts = p.split('/');
    parts.pop();
    return parts.join('/');
  })
}));

// Unit tests for MCPLegacyFormatService
import { MCPLegacyFormatService } from '../../../../src/mcp-server/services/mcp-legacy-format-service';
import { mockMCPConfig } from '../../mocks/config.mock';
import { MockMCPLogger } from '../../mocks/logger.mock';
import fs from 'fs';
import path from 'path';
import { exec } from 'child_process';
import util from 'util';

describe('MCPLegacyFormatService', () => {
  let legacyFormatService: MCPLegacyFormatService;
  let mockLogger: MockMCPLogger;
  let config: any;

  beforeEach(async () => {
    jest.clearAllMocks();

    // Reset the shared mock
    mockExecPromise.mockClear();
    mockExecPromise.mockResolvedValue({ stdout: 'mocked version', stderr: '' });

    mockLogger = new MockMCPLogger();

    // Mock fs.existsSync
    (fs.existsSync as jest.Mock).mockReturnValue(true);

    // Mock fs.promises.readFile
    (fs.promises.readFile as jest.Mock).mockImplementation((filePath, encoding) => {
      if (filePath.endsWith('.doc')) {
        return Promise.resolve('DOC content');
      } else if (filePath.endsWith('.wpd')) {
        return Promise.resolve('WordPerfect content');
      } else if (filePath.endsWith('.md')) {
        return Promise.resolve('# Converted Markdown');
      } else {
        return Promise.resolve('Unknown content');
      }
    });

    // Create config with legacy formats enabled
    config = {
      ...mockMCPConfig,
      legacyFormats: {
        enableDOC: true,
        enableWordPerfect: true
      }
    };

    legacyFormatService = new MCPLegacyFormatService(config, mockLogger as any);

    // Wait for async checkDependencies to complete
    await new Promise(resolve => setTimeout(resolve, 10));
  });
  
  describe('checkDependencies', () => {
    test('should check for required dependencies', async () => {
      // Dependencies already checked in constructor
      expect(mockExecPromise).toHaveBeenCalledWith('libreoffice --version');
      expect(mockLogger.info).toHaveBeenCalledWith('All required dependencies are installed');
    });
    
    test('should disable legacy formats if dependencies are missing', async () => {
      // Clear previous calls
      mockLogger.reset();

      // Mock dependency check failure
      mockExecPromise.mockRejectedValue(new Error('Command not found'));

      // Create new service instance with fresh config
      const newConfig = {
        ...mockMCPConfig,
        legacyFormats: {
          enableDOC: true,
          enableWordPerfect: true
        }
      };

      const newService = new MCPLegacyFormatService(newConfig, mockLogger as any);

      // Wait for async constructor to complete
      await new Promise(resolve => setTimeout(resolve, 10));

      expect(mockLogger.warn).toHaveBeenCalledWith('Some dependencies are missing', expect.any(Error));
      expect(mockLogger.warn).toHaveBeenCalledWith('Legacy format conversion has been disabled due to missing dependencies');
      expect(newConfig.legacyFormats.enableDOC).toBe(false);
      expect(newConfig.legacyFormats.enableWordPerfect).toBe(false);
    });
  });
  
  describe('convertDocToMarkdown', () => {
    test('should convert DOC content to Markdown', async () => {
      // Mock the internal conversion method
      (legacyFormatService as any).convertWithLibreOffice = jest.fn().mockResolvedValue(undefined);
      
      // Base64 encoded "DOC content"
      const base64Content = Buffer.from('DOC content').toString('base64');
      
      const result = await legacyFormatService.convertDocToMarkdown(base64Content);
      
      expect(fs.promises.writeFile).toHaveBeenCalled();
      expect((legacyFormatService as any).convertWithLibreOffice).toHaveBeenCalled();
      expect(fs.promises.readFile).toHaveBeenCalled();
      expect(fs.promises.unlink).toHaveBeenCalled(); // Clean up temp file
      
      expect(result).toHaveProperty('content', '# Converted Markdown');
      expect(result).toHaveProperty('metadata');
      expect(result.metadata).toHaveProperty('convertedAt');
      expect(result.metadata).toHaveProperty('originalFormat', 'DOC');
    });
    
    test('should throw error if DOC conversion is disabled', async () => {
      // Create config with DOC format disabled
      const disabledConfig = {
        ...mockMCPConfig,
        legacyFormats: {
          enableDOC: false,
          enableWordPerfect: true
        }
      };
      
      const disabledService = new MCPLegacyFormatService(disabledConfig, mockLogger as any);
      
      // Base64 encoded "DOC content"
      const base64Content = Buffer.from('DOC content').toString('base64');
      
      await expect(disabledService.convertDocToMarkdown(base64Content)).rejects.toThrow(
        'DOC conversion is not enabled'
      );
    });
    
    test('should handle conversion errors', async () => {
      // Mock conversion error
      (legacyFormatService as any).convertWithLibreOffice = jest.fn().mockRejectedValue(
        new Error('Conversion failed')
      );
      
      // Base64 encoded "DOC content"
      const base64Content = Buffer.from('DOC content').toString('base64');
      
      await expect(legacyFormatService.convertDocToMarkdown(base64Content)).rejects.toThrow(
        'Failed to convert DOC to Markdown'
      );
      
      expect(mockLogger.error).toHaveBeenCalled();
    });
  });
  
  describe('convertWordPerfectToMarkdown', () => {
    test('should convert WordPerfect content to Markdown', async () => {
      // Mock the internal conversion method
      (legacyFormatService as any).convertWithLibreOffice = jest.fn().mockResolvedValue(undefined);
      
      // Base64 encoded "WordPerfect content"
      const base64Content = Buffer.from('WordPerfect content').toString('base64');
      
      const result = await legacyFormatService.convertWordPerfectToMarkdown(base64Content);
      
      expect(fs.promises.writeFile).toHaveBeenCalled();
      expect((legacyFormatService as any).convertWithLibreOffice).toHaveBeenCalled();
      expect(fs.promises.readFile).toHaveBeenCalled();
      expect(fs.promises.unlink).toHaveBeenCalled(); // Clean up temp file
      
      expect(result).toHaveProperty('content', '# Converted Markdown');
      expect(result).toHaveProperty('metadata');
      expect(result.metadata).toHaveProperty('convertedAt');
      expect(result.metadata).toHaveProperty('originalFormat', 'WordPerfect');
    });
    
    test('should throw error if WordPerfect conversion is disabled', async () => {
      // Create config with WordPerfect format disabled
      const disabledConfig = {
        ...mockMCPConfig,
        legacyFormats: {
          enableDOC: true,
          enableWordPerfect: false
        }
      };
      
      const disabledService = new MCPLegacyFormatService(disabledConfig, mockLogger as any);
      
      // Base64 encoded "WordPerfect content"
      const base64Content = Buffer.from('WordPerfect content').toString('base64');
      
      await expect(disabledService.convertWordPerfectToMarkdown(base64Content)).rejects.toThrow(
        'WordPerfect conversion is not enabled'
      );
    });
    
    test('should handle conversion errors', async () => {
      // Mock conversion error
      (legacyFormatService as any).convertWithLibreOffice = jest.fn().mockRejectedValue(
        new Error('Conversion failed')
      );
      
      // Base64 encoded "WordPerfect content"
      const base64Content = Buffer.from('WordPerfect content').toString('base64');
      
      await expect(legacyFormatService.convertWordPerfectToMarkdown(base64Content)).rejects.toThrow(
        'Failed to convert WordPerfect to Markdown'
      );
      
      expect(mockLogger.error).toHaveBeenCalled();
    });
  });
  
  describe('convertDocFileToMarkdown', () => {
    test('should convert DOC file to Markdown', async () => {
      // Mock the internal conversion method
      (legacyFormatService as any).convertWithLibreOffice = jest.fn().mockResolvedValue(undefined);
      
      const result = await legacyFormatService.convertDocFileToMarkdown('/path/to/test.doc');
      
      expect((legacyFormatService as any).convertWithLibreOffice).toHaveBeenCalled();
      expect(fs.promises.readFile).toHaveBeenCalled();
      
      expect(result).toHaveProperty('content', '# Converted Markdown');
      expect(result).toHaveProperty('metadata');
      expect(result.metadata).toHaveProperty('convertedAt');
      expect(result.metadata).toHaveProperty('originalFormat', 'DOC');
      expect(result).toHaveProperty('outputPath');
      expect(result).toHaveProperty('outputFileName', 'test.md');
    });
    
    test('should throw error if file does not exist', async () => {
      // Mock file not existing
      (fs.existsSync as jest.Mock).mockReturnValue(false);
      
      await expect(legacyFormatService.convertDocFileToMarkdown('/path/to/test.doc')).rejects.toThrow(
        'File not found: /path/to/test.doc'
      );
    });
    
    test('should throw error if DOC conversion is disabled', async () => {
      // Create config with DOC format disabled
      const disabledConfig = {
        ...mockMCPConfig,
        legacyFormats: {
          enableDOC: false,
          enableWordPerfect: true
        }
      };
      
      const disabledService = new MCPLegacyFormatService(disabledConfig, mockLogger as any);
      
      await expect(disabledService.convertDocFileToMarkdown('/path/to/test.doc')).rejects.toThrow(
        'DOC conversion is not enabled'
      );
    });
  });
  
  describe('convertWordPerfectFileToMarkdown', () => {
    test('should convert WordPerfect file to Markdown', async () => {
      // Mock the internal conversion method
      (legacyFormatService as any).convertWithLibreOffice = jest.fn().mockResolvedValue(undefined);
      
      const result = await legacyFormatService.convertWordPerfectFileToMarkdown('/path/to/test.wpd');
      
      expect((legacyFormatService as any).convertWithLibreOffice).toHaveBeenCalled();
      expect(fs.promises.readFile).toHaveBeenCalled();
      
      expect(result).toHaveProperty('content', '# Converted Markdown');
      expect(result).toHaveProperty('metadata');
      expect(result.metadata).toHaveProperty('convertedAt');
      expect(result.metadata).toHaveProperty('originalFormat', 'WordPerfect');
      expect(result).toHaveProperty('outputPath');
      expect(result).toHaveProperty('outputFileName', 'test.md');
    });
    
    test('should throw error if file does not exist', async () => {
      // Mock file not existing
      (fs.existsSync as jest.Mock).mockReturnValue(false);
      
      await expect(legacyFormatService.convertWordPerfectFileToMarkdown('/path/to/test.wpd')).rejects.toThrow(
        'File not found: /path/to/test.wpd'
      );
    });
    
    test('should throw error if WordPerfect conversion is disabled', async () => {
      // Create config with WordPerfect format disabled
      const disabledConfig = {
        ...mockMCPConfig,
        legacyFormats: {
          enableDOC: true,
          enableWordPerfect: false
        }
      };
      
      const disabledService = new MCPLegacyFormatService(disabledConfig, mockLogger as any);
      
      await expect(disabledService.convertWordPerfectFileToMarkdown('/path/to/test.wpd')).rejects.toThrow(
        'WordPerfect conversion is not enabled'
      );
    });
  });
  
  describe('convertWithLibreOffice', () => {
    test('should convert file using LibreOffice and lightweight conversion', async () => {
      // Mock successful conversions
      mockExecPromise.mockResolvedValue({ stdout: 'Command executed successfully' });

      // Mock the convertOdtToMarkdown method
      jest.spyOn(legacyFormatService as any, 'convertOdtToMarkdown').mockResolvedValueOnce(undefined);

      await (legacyFormatService as any).convertWithLibreOffice(
        '/path/to/input.doc',
        '/path/to/output.md',
        { preserveFormatting: true }
      );

      // Check LibreOffice command was called
      expect(mockExecPromise).toHaveBeenCalledWith(
        expect.stringContaining('libreoffice --headless --convert-to odt')
      );

      // Check that convertOdtToMarkdown was called (lightweight conversion)
      expect((legacyFormatService as any).convertOdtToMarkdown).toHaveBeenCalled();

      // Check cleanup
      expect(fs.promises.unlink).toHaveBeenCalled();
    });
    
    test('should handle LibreOffice conversion errors', async () => {
      // Mock LibreOffice error
      mockExecPromise.mockRejectedValueOnce(new Error('LibreOffice conversion failed'));

      await expect((legacyFormatService as any).convertWithLibreOffice(
        '/path/to/input.doc',
        '/path/to/output.md'
      )).rejects.toThrow('Failed to convert file with LibreOffice');
    });
    
    test('should handle Pandoc conversion errors', async () => {
      // Mock successful LibreOffice but failed ODT conversion
      mockExecPromise.mockResolvedValueOnce({ stdout: 'LibreOffice conversion successful' });

      // Mock the convertOdtToMarkdown method to throw an error
      jest.spyOn(legacyFormatService as any, 'convertOdtToMarkdown').mockRejectedValueOnce(new Error('ODT conversion failed'));

      await expect((legacyFormatService as any).convertWithLibreOffice(
        '/path/to/input.doc',
        '/path/to/output.md'
      )).rejects.toThrow('Failed to convert file with LibreOffice');
    });
    
    test('should extract images if requested', async () => {
      // Mock successful conversions
      mockExecPromise.mockResolvedValue({ stdout: 'Command executed successfully' });

      // Mock the convertOdtToMarkdown method
      jest.spyOn(legacyFormatService as any, 'convertOdtToMarkdown').mockResolvedValueOnce(undefined);

      // Mock the extractImages method
      jest.spyOn(legacyFormatService as any, 'extractImages').mockResolvedValueOnce(undefined);

      await (legacyFormatService as any).convertWithLibreOffice(
        '/path/to/input.doc',
        '/path/to/output.md',
        { extractImages: true, imageOutputDir: '/path/to/images' }
      );

      // Check that extractImages was called
      expect((legacyFormatService as any).extractImages).toHaveBeenCalledWith(
        expect.stringContaining('.odt'),
        '/path/to/images'
      );
    });
  });
});