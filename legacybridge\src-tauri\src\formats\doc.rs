// Microsoft DOC format parser
// Critical for VB6/VFP9 legacy system integration

use std::collections::HashMap;
use crate::conversion::error::ConversionError;
use super::common::*;
use super::{FormatDetection, ConversionResult, FormatType};

/// DOC file structure constants
const OLE2_SIGNATURE: &[u8] = &[0xD0, 0xCF, 0x11, 0xE0, 0xA1, 0xB1, 0x1A, 0xE1];
const SECTOR_SIZE: usize = 512;
const MINI_SECTOR_SIZE: usize = 64;

/// DOC file header structure
#[derive(Debug)]
struct DocHeader {
    signature: [u8; 8],
    minor_version: u16,
    major_version: u16,
    byte_order: u16,
    sector_size: u16,
    mini_sector_size: u16,
    num_dir_sectors: u32,
    num_fat_sectors: u32,
    dir_first_sector: u32,
    mini_stream_cutoff: u32,
    mini_fat_first_sector: u32,
    num_mini_fat_sectors: u32,
    difat_first_sector: u32,
    num_difat_sectors: u32,
}

impl DocHeader {
    fn parse(content: &[u8]) -> Result<Self, ConversionError> {
        if content.len() < 76 {
            return Err(ConversionError::InvalidInput("DOC header too short".to_string()));
        }

        let mut signature = [0u8; 8];
        signature.copy_from_slice(&content[0..8]);

        Ok(DocHeader {
            signature,
            minor_version: bytes_to_u16_le(&content[24..26]),
            major_version: bytes_to_u16_le(&content[26..28]),
            byte_order: bytes_to_u16_le(&content[28..30]),
            sector_size: bytes_to_u16_le(&content[30..32]),
            mini_sector_size: bytes_to_u16_le(&content[32..34]),
            num_dir_sectors: bytes_to_u32_le(&content[44..48]),
            num_fat_sectors: bytes_to_u32_le(&content[48..52]),
            dir_first_sector: bytes_to_u32_le(&content[52..56]),
            mini_stream_cutoff: bytes_to_u32_le(&content[56..60]),
            mini_fat_first_sector: bytes_to_u32_le(&content[60..64]),
            num_mini_fat_sectors: bytes_to_u32_le(&content[64..68]),
            difat_first_sector: bytes_to_u32_le(&content[68..72]),
            num_difat_sectors: bytes_to_u32_le(&content[72..76]),
        })
    }

    fn is_valid(&self) -> bool {
        self.signature == OLE2_SIGNATURE &&
        self.byte_order == 0xFFFE &&
        (self.sector_size == 9 || self.sector_size == 12) // 512 or 4096 bytes
    }
}

/// Directory entry in DOC file
#[derive(Debug)]
struct DirectoryEntry {
    name: String,
    name_length: u16,
    entry_type: u8,
    color: u8,
    left_sibling: u32,
    right_sibling: u32,
    child: u32,
    start_sector: u32,
    size: u64,
}

impl DirectoryEntry {
    fn parse(data: &[u8]) -> Result<Self, ConversionError> {
        if data.len() < 128 {
            return Err(ConversionError::InvalidInput("Directory entry too short".to_string()));
        }

        let name_length = bytes_to_u16_le(&data[64..66]);
        let name_bytes = &data[0..std::cmp::min(64, name_length as usize)];
        let name = String::from_utf8_lossy(name_bytes)
            .trim_end_matches('\0')
            .to_string();

        Ok(DirectoryEntry {
            name,
            name_length,
            entry_type: data[66],
            color: data[67],
            left_sibling: bytes_to_u32_le(&data[68..72]),
            right_sibling: bytes_to_u32_le(&data[72..76]),
            child: bytes_to_u32_le(&data[76..80]),
            start_sector: bytes_to_u32_le(&data[116..120]),
            size: bytes_to_u32_le(&data[120..124]) as u64,
        })
    }

    fn is_stream(&self) -> bool {
        self.entry_type == 2
    }

    fn is_word_document(&self) -> bool {
        self.name == "WordDocument" || self.name.contains("WordDocument")
    }
}

/// Detect DOC format
pub fn detect_doc_format(content: &[u8]) -> Result<FormatDetection, ConversionError> {
    if content.len() < 512 {
        return Ok(FormatDetection {
            format_type: FormatType::Unknown,
            confidence: 0.0,
            version: None,
            metadata: HashMap::new(),
        });
    }

    // Check OLE2 signature
    if !has_magic_bytes(content, OLE2_SIGNATURE) {
        return Ok(FormatDetection {
            format_type: FormatType::Unknown,
            confidence: 0.0,
            version: None,
            metadata: HashMap::new(),
        });
    }

    let header = DocHeader::parse(content)?;
    if !header.is_valid() {
        return Ok(FormatDetection {
            format_type: FormatType::Unknown,
            confidence: 0.3,
            version: None,
            metadata: HashMap::new(),
        });
    }

    // Look for Word-specific streams
    let mut confidence = 0.7;
    let mut version = None;
    let mut metadata = extract_metadata(content, "DOC");

    // Try to find WordDocument stream
    if let Ok(entries) = parse_directory_entries(content, &header) {
        for entry in entries {
            if entry.is_word_document() {
                confidence = 0.95;
                metadata.insert("has_word_stream".to_string(), "true".to_string());
                break;
            }
        }
    }

    // Detect version based on header
    let version_str = match (header.major_version, header.minor_version) {
        (0x003E, _) => Some("Word 97-2003".to_string()),
        (0x003F, _) => Some("Word 2007+".to_string()),
        _ => Some(format!("Unknown (v{}.{})", header.major_version, header.minor_version)),
    };

    if version_str.is_some() {
        version = version_str;
        confidence = f32::max(confidence, 0.8);
    }

    metadata.insert("ole_version".to_string(), format!("{}.{}", header.major_version, header.minor_version));
    metadata.insert("sector_size".to_string(), (1 << header.sector_size).to_string());

    Ok(FormatDetection {
        format_type: FormatType::Doc,
        confidence,
        version,
        metadata,
    })
}

/// Parse directory entries from DOC file
fn parse_directory_entries(content: &[u8], header: &DocHeader) -> Result<Vec<DirectoryEntry>, ConversionError> {
    let sector_size = 1 << header.sector_size;
    let dir_sector_offset = (header.dir_first_sector as usize + 1) * sector_size;
    
    if content.len() < dir_sector_offset + sector_size {
        return Err(ConversionError::InvalidInput("Directory sector out of bounds".to_string()));
    }

    let mut entries = Vec::new();
    let dir_data = &content[dir_sector_offset..dir_sector_offset + sector_size];
    
    // Each directory entry is 128 bytes
    for chunk in dir_data.chunks(128) {
        if chunk.len() == 128 {
            if let Ok(entry) = DirectoryEntry::parse(chunk) {
                if !entry.name.is_empty() {
                    entries.push(entry);
                }
            }
        }
    }

    Ok(entries)
}

/// Convert DOC to Markdown
pub fn convert_doc_to_markdown(content: &[u8]) -> Result<ConversionResult, ConversionError> {
    let detection = detect_doc_format(content)?;
    if detection.format_type != FormatType::Doc {
        return Err(ConversionError::UnsupportedFormat("Not a valid DOC file".to_string()));
    }

    let header = DocHeader::parse(content)?;
    let entries = parse_directory_entries(content, &header)?;

    // Find WordDocument stream
    let word_stream = entries.iter()
        .find(|entry| entry.is_word_document())
        .ok_or_else(|| ConversionError::InvalidInput("WordDocument stream not found".to_string()))?;

    // Extract text content (simplified extraction)
    let text_content = extract_text_from_stream(content, &header, word_stream)?;
    let cleaned_text = clean_text_content(&text_content);

    let mut metadata = detection.metadata;
    metadata.insert("extracted_text_length".to_string(), cleaned_text.len().to_string());
    metadata.insert("stream_size".to_string(), word_stream.size.to_string());

    let mut warnings = Vec::new();
    if cleaned_text.len() < 10 {
        warnings.push("Very little text content extracted - file may be corrupted or heavily formatted".to_string());
    }

    Ok(ConversionResult {
        content: format!("# Document Content\n\n{}", cleaned_text),
        format: "markdown".to_string(),
        metadata,
        warnings,
    })
}

/// Convert DOC to RTF
pub fn convert_doc_to_rtf(content: &[u8]) -> Result<ConversionResult, ConversionError> {
    let markdown_result = convert_doc_to_markdown(content)?;
    
    // Convert markdown to RTF (simplified)
    let rtf_content = format!(
        "{{\\rtf1\\ansi\\deff0 {{\\fonttbl {{\\f0 Times New Roman;}}}} \\f0\\fs24 {}}}",
        markdown_result.content.replace('\n', "\\par ")
    );

    Ok(ConversionResult {
        content: rtf_content,
        format: "rtf".to_string(),
        metadata: markdown_result.metadata,
        warnings: markdown_result.warnings,
    })
}

/// Extract text from WordDocument stream (simplified)
fn extract_text_from_stream(content: &[u8], header: &DocHeader, stream: &DirectoryEntry) -> Result<String, ConversionError> {
    let sector_size = 1 << header.sector_size;
    let stream_offset = (stream.start_sector as usize + 1) * sector_size;
    let stream_size = std::cmp::min(stream.size as usize, 8192); // Limit extraction size
    
    if content.len() < stream_offset + stream_size {
        return Err(ConversionError::InvalidInput("Stream data out of bounds".to_string()));
    }

    let stream_data = &content[stream_offset..stream_offset + stream_size];
    
    // Simple text extraction - look for readable text
    let mut text = String::new();
    let mut i = 0;
    
    while i < stream_data.len() {
        let byte = stream_data[i];
        
        // Skip control characters and look for text
        if byte >= 0x20 && byte <= 0x7E {
            text.push(byte as char);
        } else if byte == 0x0A || byte == 0x0D {
            text.push('\n');
        } else if byte == 0x09 {
            text.push('\t');
        }
        
        i += 1;
    }

    // Clean up extracted text
    let cleaned = text
        .lines()
        .map(|line| line.trim())
        .filter(|line| !line.is_empty())
        .collect::<Vec<_>>()
        .join("\n");

    if cleaned.is_empty() {
        Ok("(No readable text content found)".to_string())
    } else {
        Ok(cleaned)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_doc_header_parsing() {
        let mut header_data = vec![0u8; 76];
        header_data[0..8].copy_from_slice(OLE2_SIGNATURE);
        header_data[28] = 0xFE;
        header_data[29] = 0xFF;
        header_data[30] = 9; // 512-byte sectors

        let header = DocHeader::parse(&header_data).unwrap();
        assert!(header.is_valid());
    }

    #[test]
    fn test_doc_detection() {
        let mut content = vec![0u8; 512];
        content[0..8].copy_from_slice(OLE2_SIGNATURE);
        content[28] = 0xFE;
        content[29] = 0xFF;
        content[30] = 9;

        let detection = detect_doc_format(&content).unwrap();
        assert_eq!(detection.format_type, FormatType::Doc);
        assert!(detection.confidence > 0.5);
    }
}
