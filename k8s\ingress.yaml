apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: legacybridge
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/limit-rps: "10"
    nginx.ingress.kubernetes.io/limit-connections: "20"
spec:
  tls:
  - hosts:
    - legacybridge.example.com
    secretName: legacybridge-tls
  rules:
  - host: legacybridge.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: legacybridge
            port:
              number: 80