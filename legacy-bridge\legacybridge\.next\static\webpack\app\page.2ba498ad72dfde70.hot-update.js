"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Archive_ArrowRight_CheckCircle2_Download_FileText_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,ArrowRight,CheckCircle2,Download,FileText,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_ArrowRight_CheckCircle2_Download_FileText_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,ArrowRight,CheckCircle2,Download,FileText,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_ArrowRight_CheckCircle2_Download_FileText_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,ArrowRight,CheckCircle2,Download,FileText,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_ArrowRight_CheckCircle2_Download_FileText_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,ArrowRight,CheckCircle2,Download,FileText,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_ArrowRight_CheckCircle2_Download_FileText_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,ArrowRight,CheckCircle2,Download,FileText,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/archive.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_ArrowRight_CheckCircle2_Download_FileText_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,ArrowRight,CheckCircle2,Download,FileText,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _components_DragDropZone__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/DragDropZone */ \"(app-pages-browser)/./src/components/DragDropZone.tsx\");\n/* harmony import */ var _components_ConversionProgress__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ConversionProgress */ \"(app-pages-browser)/./src/components/ConversionProgress.tsx\");\n/* harmony import */ var _components_PreviewPanel__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/PreviewPanel */ \"(app-pages-browser)/./src/components/PreviewPanel.tsx\");\n/* harmony import */ var _components_DownloadManager__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/DownloadManager */ \"(app-pages-browser)/./src/components/DownloadManager.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/switch */ \"(app-pages-browser)/./src/components/ui/switch.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _lib_stores_files__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/lib/stores/files */ \"(app-pages-browser)/./src/lib/stores/files.ts\");\n/* harmony import */ var _lib_tauri_api__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/lib/tauri-api */ \"(app-pages-browser)/./src/lib/tauri-api.ts\");\n/* harmony import */ var _lib_download_service__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/lib/download-service */ \"(app-pages-browser)/./src/lib/download-service.ts\");\n/* harmony import */ var _components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ErrorBoundary */ \"(app-pages-browser)/./src/components/ErrorBoundary.tsx\");\n/* harmony import */ var _lib_error_logger__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/lib/error-logger */ \"(app-pages-browser)/./src/lib/error-logger.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction Home() {\n    _s();\n    const { files, updateFileStatus, updateFileProgress, clearFiles } = (0,_lib_stores_files__WEBPACK_IMPORTED_MODULE_12__.useFileStore)();\n    const [isConverting, setIsConverting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [conversionResults, setConversionResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showPreview, setShowPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [selectedFileContent, setSelectedFileContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedFileType, setSelectedFileType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('rtf');\n    const [selectedFileName, setSelectedFileName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [showDownloadManager, setShowDownloadManager] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleConvertToMarkdown = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Home.useCallback[handleConvertToMarkdown]\": async ()=>{\n            setIsConverting(true);\n            const rtfFiles = files.filter({\n                \"Home.useCallback[handleConvertToMarkdown].rtfFiles\": (file)=>file.type === 'rtf'\n            }[\"Home.useCallback[handleConvertToMarkdown].rtfFiles\"]);\n            const results = [];\n            for (const file of rtfFiles){\n                updateFileStatus(file.id, 'converting');\n                updateFileProgress(file.id, 0);\n                // Simulate progress with cleanup\n                let progressInterval = null;\n                progressInterval = setInterval({\n                    \"Home.useCallback[handleConvertToMarkdown]\": ()=>{\n                        updateFileProgress(file.id, Math.min(90, Math.random() * 100));\n                    }\n                }[\"Home.useCallback[handleConvertToMarkdown]\"], 200);\n                try {\n                    const result = await _lib_tauri_api__WEBPACK_IMPORTED_MODULE_13__.tauriApi.convertRtfToMarkdown(file.path);\n                    if (progressInterval) clearInterval(progressInterval);\n                    updateFileProgress(file.id, 100);\n                    updateFileStatus(file.id, result.success ? 'completed' : 'error', result);\n                    results.push({\n                        file,\n                        result\n                    });\n                } catch (error) {\n                    if (progressInterval) clearInterval(progressInterval);\n                    _lib_error_logger__WEBPACK_IMPORTED_MODULE_16__.logger.error('Conversion', 'Failed to convert RTF to Markdown', error, {\n                        fileId: file.id,\n                        fileName: file.name\n                    });\n                    updateFileStatus(file.id, 'error');\n                }\n            }\n            setConversionResults(results);\n            setIsConverting(false);\n        }\n    }[\"Home.useCallback[handleConvertToMarkdown]\"], [\n        files,\n        updateFileStatus,\n        updateFileProgress\n    ]);\n    const handleConvertToRtf = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Home.useCallback[handleConvertToRtf]\": async ()=>{\n            setIsConverting(true);\n            const mdFiles = files.filter({\n                \"Home.useCallback[handleConvertToRtf].mdFiles\": (file)=>file.type === 'md'\n            }[\"Home.useCallback[handleConvertToRtf].mdFiles\"]);\n            const results = [];\n            for (const file of mdFiles){\n                updateFileStatus(file.id, 'converting');\n                updateFileProgress(file.id, 0);\n                // Simulate progress with cleanup\n                let progressInterval = null;\n                progressInterval = setInterval({\n                    \"Home.useCallback[handleConvertToRtf]\": ()=>{\n                        updateFileProgress(file.id, Math.min(90, Math.random() * 100));\n                    }\n                }[\"Home.useCallback[handleConvertToRtf]\"], 200);\n                try {\n                    const result = await _lib_tauri_api__WEBPACK_IMPORTED_MODULE_13__.tauriApi.convertMarkdownToRtf(file.path);\n                    if (progressInterval) clearInterval(progressInterval);\n                    updateFileProgress(file.id, 100);\n                    updateFileStatus(file.id, result.success ? 'completed' : 'error', result);\n                    results.push({\n                        file,\n                        result\n                    });\n                } catch (error) {\n                    if (progressInterval) clearInterval(progressInterval);\n                    _lib_error_logger__WEBPACK_IMPORTED_MODULE_16__.logger.error('Conversion', 'Failed to convert Markdown to RTF', error, {\n                        fileId: file.id,\n                        fileName: file.name\n                    });\n                    updateFileStatus(file.id, 'error');\n                }\n            }\n            setConversionResults(results);\n            setIsConverting(false);\n        }\n    }[\"Home.useCallback[handleConvertToRtf]\"], [\n        files,\n        updateFileStatus,\n        updateFileProgress\n    ]);\n    const rtfCount = files.filter((f)=>f.type === 'rtf').length;\n    const mdCount = files.filter((f)=>f.type === 'md').length;\n    const completedCount = files.filter((f)=>f.status === 'completed').length;\n    // Load file content for preview\n    const loadFileContent = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Home.useCallback[loadFileContent]\": async (file)=>{\n            try {\n                // Read file content using Tauri API\n                const response = await _lib_tauri_api__WEBPACK_IMPORTED_MODULE_13__.tauriApi.readFileContent(file.path);\n                if (response.success && response.content) {\n                    setSelectedFileContent(response.content);\n                    setSelectedFileType(file.type);\n                    setSelectedFileName(file.name);\n                }\n            } catch (error) {\n                _lib_error_logger__WEBPACK_IMPORTED_MODULE_16__.logger.error('FilePreview', 'Failed to load file content', error, {\n                    fileName: file.name,\n                    filePath: file.path\n                });\n                console.error('Failed to load file content:', error);\n            }\n        }\n    }[\"Home.useCallback[loadFileContent]\"], []);\n    // Auto-load first file when files are added\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            if (files.length > 0 && showPreview && !selectedFileContent) {\n                const firstFile = files[0];\n                if (firstFile) {\n                    loadFileContent(firstFile);\n                }\n            }\n        }\n    }[\"Home.useEffect\"], [\n        files,\n        showPreview,\n        selectedFileContent,\n        loadFileContent\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_15__.ConversionErrorBoundary, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-b from-background to-secondary/20\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-12 max-w-5xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_17__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: -20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        className: \"text-center mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"inline-flex items-center gap-2 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_17__.motion.div, {\n                                        animate: {\n                                            rotate: [\n                                                0,\n                                                360\n                                            ]\n                                        },\n                                        transition: {\n                                            duration: 20,\n                                            repeat: Infinity,\n                                            ease: \"linear\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_ArrowRight_CheckCircle2_Download_FileText_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"w-6 h-6 text-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-4xl font-bold text-foreground\",\n                                        children: \"LegacyBridge\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground text-lg\",\n                                children: \"Convert between RTF and Markdown with ease\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DragDropZone__WEBPACK_IMPORTED_MODULE_2__.DragDropZone, {\n                                className: \"mb-8\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_19__.AnimatePresence, {\n                                children: files.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_17__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        scale: 0.95\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        scale: 1\n                                    },\n                                    exit: {\n                                        opacity: 0,\n                                        scale: 0.95\n                                    },\n                                    transition: {\n                                        duration: 0.3\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                                        className: \"p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col sm:flex-row items-center justify-between gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_ArrowRight_CheckCircle2_Download_FileText_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                            className: \"w-5 h-5 text-muted-foreground\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 181,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: [\n                                                                        files.length,\n                                                                        \" files selected\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 183,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex gap-2 mt-1\",\n                                                                    children: [\n                                                                        rtfCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                                            variant: \"outline\",\n                                                                            children: [\n                                                                                rtfCount,\n                                                                                \" RTF\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 186,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        mdCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                                            variant: \"outline\",\n                                                                            children: [\n                                                                                mdCount,\n                                                                                \" Markdown\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 189,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        completedCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                                            variant: \"default\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_ArrowRight_CheckCircle2_Download_FileText_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                    className: \"w-3 h-3 mr-1\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 193,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                completedCount,\n                                                                                \" completed\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 192,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 184,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 182,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 180,\n                                                    columnNumber: 21\n                                                }, this),\n                                                showPreview && files.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 mt-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-muted-foreground\",\n                                                            children: \"Preview:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 204,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            className: \"text-xs bg-background border rounded px-2 py-1\",\n                                                            value: selectedFileName,\n                                                            onChange: (e)=>{\n                                                                const file = files.find((f)=>f.name === e.target.value);\n                                                                if (file) loadFileContent(file);\n                                                            },\n                                                            children: files.map((file)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: file.name,\n                                                                    children: file.name\n                                                                }, file.id, false, {\n                                                                    fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 214,\n                                                                    columnNumber: 29\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 205,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                            onClick: handleConvertToMarkdown,\n                                                            disabled: rtfCount === 0 || isConverting,\n                                                            className: \"group\",\n                                                            children: [\n                                                                \"Convert to Markdown\",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_ArrowRight_CheckCircle2_Download_FileText_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    className: \"ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 229,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 223,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                            onClick: handleConvertToRtf,\n                                                            disabled: mdCount === 0 || isConverting,\n                                                            variant: \"secondary\",\n                                                            className: \"group\",\n                                                            children: [\n                                                                \"Convert to RTF\",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_ArrowRight_CheckCircle2_Download_FileText_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    className: \"ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 238,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 231,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                            onClick: clearFiles,\n                                                            variant: \"outline\",\n                                                            disabled: isConverting,\n                                                            children: \"Clear All\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 240,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 222,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_19__.AnimatePresence, {\n                                children: files.length > 0 && files.some((f)=>f.status !== 'idle') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_17__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    exit: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ConversionProgress__WEBPACK_IMPORTED_MODULE_3__.ConversionProgress, {\n                                        onDownload: async (file)=>{\n                                            try {\n                                                await _lib_download_service__WEBPACK_IMPORTED_MODULE_14__.downloadService.downloadFile(file);\n                                            } catch (error) {\n                                                _lib_error_logger__WEBPACK_IMPORTED_MODULE_16__.logger.error('Download', 'File download failed', error, {\n                                                    fileId: file.id,\n                                                    fileName: file.name\n                                                });\n                                                console.error('Download failed:', error);\n                                            }\n                                        },\n                                        onPreview: (file)=>{\n                                            var _file_result;\n                                            if ((_file_result = file.result) === null || _file_result === void 0 ? void 0 : _file_result.content) {\n                                                var _file_result_metadata, _file_result_metadata1;\n                                                setSelectedFileContent(file.result.content);\n                                                setSelectedFileType(((_file_result_metadata = file.result.metadata) === null || _file_result_metadata === void 0 ? void 0 : _file_result_metadata.convertedFormat) || 'md');\n                                                setSelectedFileName(file.file.name.replace(/\\.(rtf|md)$/i, \".\".concat(((_file_result_metadata1 = file.result.metadata) === null || _file_result_metadata1 === void 0 ? void 0 : _file_result_metadata1.convertedFormat) || 'md')));\n                                                setShowPreview(true);\n                                            }\n                                        },\n                                        onRetry: async (file)=>{\n                                            // Re-run conversion for failed file\n                                            updateFileStatus(file.id, 'converting');\n                                            updateFileProgress(file.id, 0);\n                                            try {\n                                                const result = file.file.type === 'rtf' ? await _lib_tauri_api__WEBPACK_IMPORTED_MODULE_13__.tauriApi.convertRtfToMarkdown(file.file.path) : await _lib_tauri_api__WEBPACK_IMPORTED_MODULE_13__.tauriApi.convertMarkdownToRtf(file.file.path);\n                                                updateFileStatus(file.id, result.success ? 'completed' : 'error', result);\n                                            } catch (error) {\n                                                _lib_error_logger__WEBPACK_IMPORTED_MODULE_16__.logger.error('Conversion', 'Retry conversion failed', error, {\n                                                    fileId: file.id,\n                                                    fileName: file.file.name,\n                                                    fileType: file.file.type\n                                                });\n                                                updateFileStatus(file.id, 'error');\n                                            }\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_19__.AnimatePresence, {\n                                children: files.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_17__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 10\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    exit: {\n                                        opacity: 0,\n                                        y: 10\n                                    },\n                                    className: \"flex items-center justify-between p-4 bg-secondary/30 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_11__.Label, {\n                                                    htmlFor: \"preview-toggle\",\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Real-time Preview\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_10__.Switch, {\n                                                    id: \"preview-toggle\",\n                                                    checked: showPreview,\n                                                    onCheckedChange: setShowPreview\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 320,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-muted-foreground\",\n                                                    children: showPreview ? 'Preview enabled' : 'Preview disabled'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 327,\n                                                    columnNumber: 19\n                                                }, this),\n                                                completedCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                    size: \"sm\",\n                                                    variant: \"outline\",\n                                                    onClick: ()=>setShowDownloadManager(true),\n                                                    className: \"gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_ArrowRight_CheckCircle2_Download_FileText_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 337,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"Download Manager (\",\n                                                        completedCount,\n                                                        \")\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 331,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 326,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 310,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_19__.AnimatePresence, {\n                                children: showPreview && selectedFileContent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_17__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        height: 0\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        height: 'auto'\n                                    },\n                                    exit: {\n                                        opacity: 0,\n                                        height: 0\n                                    },\n                                    transition: {\n                                        duration: 0.3\n                                    },\n                                    className: \"overflow-hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PreviewPanel__WEBPACK_IMPORTED_MODULE_4__.PreviewPanel, {\n                                        sourceContent: selectedFileContent,\n                                        sourceType: selectedFileType,\n                                        fileName: selectedFileName,\n                                        className: \"h-[600px]\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 356,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 349,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 347,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_19__.AnimatePresence, {\n                                children: conversionResults.length > 0 && !showPreview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_17__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    exit: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold mb-4 flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_ArrowRight_CheckCircle2_Download_FileText_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            className: \"w-5 h-5 text-green-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 377,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"Conversion Results\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 376,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.Tabs, {\n                                                    defaultValue: \"preview\",\n                                                    className: \"w-full\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsList, {\n                                                            className: \"grid w-full grid-cols-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsTrigger, {\n                                                                    value: \"preview\",\n                                                                    children: \"Preview\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 383,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsTrigger, {\n                                                                    value: \"download\",\n                                                                    children: \"Download\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 384,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 382,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsContent, {\n                                                            value: \"preview\",\n                                                            className: \"mt-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-4 max-h-96 overflow-y-auto\",\n                                                                children: conversionResults.map((result, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_17__.motion.div, {\n                                                                        initial: {\n                                                                            opacity: 0,\n                                                                            x: -20\n                                                                        },\n                                                                        animate: {\n                                                                            opacity: 1,\n                                                                            x: 0\n                                                                        },\n                                                                        transition: {\n                                                                            delay: index * 0.1\n                                                                        },\n                                                                        className: \"p-4 bg-secondary/50 rounded-lg\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center justify-between mb-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-medium\",\n                                                                                        children: result.file.name\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 398,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                                                        variant: result.result.success ? 'default' : 'destructive',\n                                                                                        children: result.result.success ? 'Success' : 'Failed'\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 399,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 397,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            result.result.content && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                                                className: \"text-xs bg-background p-3 rounded overflow-x-auto\",\n                                                                                children: [\n                                                                                    result.result.content.slice(0, 200),\n                                                                                    \"...\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 404,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, index, true, {\n                                                                        fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 390,\n                                                                        columnNumber: 29\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 388,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 387,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsContent, {\n                                                            value: \"download\",\n                                                            className: \"mt-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-3\",\n                                                                children: conversionResults.filter((r)=>r.result.success).map((result, index)=>{\n                                                                    var _result_result_metadata;\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_17__.motion.div, {\n                                                                        initial: {\n                                                                            opacity: 0,\n                                                                            y: 10\n                                                                        },\n                                                                        animate: {\n                                                                            opacity: 1,\n                                                                            y: 0\n                                                                        },\n                                                                        transition: {\n                                                                            delay: index * 0.05\n                                                                        },\n                                                                        className: \"flex items-center justify-between p-3 bg-secondary/30 rounded-lg\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm font-medium\",\n                                                                                children: [\n                                                                                    result.file.name.replace(/\\.(rtf|md)$/, ''),\n                                                                                    ((_result_result_metadata = result.result.metadata) === null || _result_result_metadata === void 0 ? void 0 : _result_result_metadata.convertedFormat) === 'md' ? '.md' : '.rtf'\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 425,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                                size: \"sm\",\n                                                                                variant: \"ghost\",\n                                                                                className: \"gap-2\",\n                                                                                onClick: async ()=>{\n                                                                                    const processedFile = files.find((f)=>f.id === result.file.id);\n                                                                                    if (processedFile) {\n                                                                                        try {\n                                                                                            await _lib_download_service__WEBPACK_IMPORTED_MODULE_14__.downloadService.downloadFile(processedFile);\n                                                                                        } catch (error) {\n                                                                                            _lib_error_logger__WEBPACK_IMPORTED_MODULE_16__.logger.error('Download', 'Result download failed', error, {\n                                                                                                fileId: result.file.id,\n                                                                                                fileName: result.file.name\n                                                                                            });\n                                                                                            console.error('Download failed:', error);\n                                                                                        }\n                                                                                    }\n                                                                                },\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_ArrowRight_CheckCircle2_Download_FileText_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                        className: \"w-4 h-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 448,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    \"Download\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 429,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, index, true, {\n                                                                        fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 418,\n                                                                        columnNumber: 31\n                                                                    }, this);\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 414,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 413,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 381,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 375,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 369,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 367,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_19__.AnimatePresence, {\n                                children: showDownloadManager && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_17__.motion.div, {\n                                    initial: {\n                                        opacity: 0\n                                    },\n                                    animate: {\n                                        opacity: 1\n                                    },\n                                    exit: {\n                                        opacity: 0\n                                    },\n                                    className: \"fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4\",\n                                    onClick: ()=>setShowDownloadManager(false),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_17__.motion.div, {\n                                        initial: {\n                                            scale: 0.9,\n                                            opacity: 0\n                                        },\n                                        animate: {\n                                            scale: 1,\n                                            opacity: 1\n                                        },\n                                        exit: {\n                                            scale: 0.9,\n                                            opacity: 0\n                                        },\n                                        onClick: (e)=>e.stopPropagation(),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DownloadManager__WEBPACK_IMPORTED_MODULE_5__.DownloadManager, {\n                                                files: files,\n                                                onClose: ()=>setShowDownloadManager(false)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 478,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                className: \"mt-4 w-full\",\n                                                variant: \"outline\",\n                                                onClick: ()=>setShowDownloadManager(false),\n                                                children: \"Close\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 482,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 472,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 465,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 463,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 141,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 140,\n            columnNumber: 5\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 139,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"mTvJR9jr28gT1SYDvs54+Z82hAo=\", false, function() {\n    return [\n        _lib_stores_files__WEBPACK_IMPORTED_MODULE_12__.useFileStore\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFOEM7QUFDVTtBQUN1QztBQUN0QztBQUNZO0FBQ1o7QUFDTTtBQUNmO0FBQ0o7QUFDRTtBQUNrQztBQUNoQztBQUNGO0FBQ0k7QUFDUDtBQUNjO0FBQ3ZCO0FBR2tEO0FBQ3hDO0FBRTdCLFNBQVM2Qjs7SUFDdEIsTUFBTSxFQUFFQyxLQUFLLEVBQUVDLGdCQUFnQixFQUFFQyxrQkFBa0IsRUFBRUMsVUFBVSxFQUFFLEdBQUdWLGdFQUFZQTtJQUNoRixNQUFNLENBQUNXLGNBQWNDLGdCQUFnQixHQUFHbkMsK0NBQVFBLENBQUM7SUFDakQsTUFBTSxDQUFDb0MsbUJBQW1CQyxxQkFBcUIsR0FBR3JDLCtDQUFRQSxDQUE0RCxFQUFFO0lBQ3hILE1BQU0sQ0FBQ3NDLGFBQWFDLGVBQWUsR0FBR3ZDLCtDQUFRQSxDQUFDO0lBQy9DLE1BQU0sQ0FBQ3dDLHFCQUFxQkMsdUJBQXVCLEdBQUd6QywrQ0FBUUEsQ0FBUztJQUN2RSxNQUFNLENBQUMwQyxrQkFBa0JDLG9CQUFvQixHQUFHM0MsK0NBQVFBLENBQTRCO0lBQ3BGLE1BQU0sQ0FBQzRDLGtCQUFrQkMsb0JBQW9CLEdBQUc3QywrQ0FBUUEsQ0FBUztJQUNqRSxNQUFNLENBQUM4QyxxQkFBcUJDLHVCQUF1QixHQUFHL0MsK0NBQVFBLENBQUM7SUFFL0QsTUFBTWdELDBCQUEwQi9DLGtEQUFXQTtxREFBQztZQUMxQ2tDLGdCQUFnQjtZQUNoQixNQUFNYyxXQUFXbkIsTUFBTW9CLE1BQU07c0VBQUNDLENBQUFBLE9BQVFBLEtBQUtDLElBQUksS0FBSzs7WUFDcEQsTUFBTUMsVUFBVSxFQUFFO1lBRWxCLEtBQUssTUFBTUYsUUFBUUYsU0FBVTtnQkFDM0JsQixpQkFBaUJvQixLQUFLRyxFQUFFLEVBQUU7Z0JBQzFCdEIsbUJBQW1CbUIsS0FBS0csRUFBRSxFQUFFO2dCQUU1QixpQ0FBaUM7Z0JBQ2pDLElBQUlDLG1CQUEwQztnQkFDOUNBLG1CQUFtQkM7aUVBQVk7d0JBQzdCeEIsbUJBQW1CbUIsS0FBS0csRUFBRSxFQUFFRyxLQUFLQyxHQUFHLENBQUMsSUFBSUQsS0FBS0UsTUFBTSxLQUFLO29CQUMzRDtnRUFBRztnQkFFSCxJQUFJO29CQUNGLE1BQU1DLFNBQVMsTUFBTXBDLHFEQUFRQSxDQUFDcUMsb0JBQW9CLENBQUNWLEtBQUtXLElBQUk7b0JBQzVELElBQUlQLGtCQUFrQlEsY0FBY1I7b0JBQ3BDdkIsbUJBQW1CbUIsS0FBS0csRUFBRSxFQUFFO29CQUM1QnZCLGlCQUFpQm9CLEtBQUtHLEVBQUUsRUFBRU0sT0FBT0ksT0FBTyxHQUFHLGNBQWMsU0FBU0o7b0JBQ2xFUCxRQUFRWSxJQUFJLENBQUM7d0JBQUVkO3dCQUFNUztvQkFBTztnQkFDOUIsRUFBRSxPQUFPTSxPQUFPO29CQUNkLElBQUlYLGtCQUFrQlEsY0FBY1I7b0JBQ3BDM0Isc0RBQU1BLENBQUNzQyxLQUFLLENBQUMsY0FBYyxxQ0FBcUNBLE9BQU87d0JBQ3JFQyxRQUFRaEIsS0FBS0csRUFBRTt3QkFDZmMsVUFBVWpCLEtBQUtrQixJQUFJO29CQUNyQjtvQkFDQXRDLGlCQUFpQm9CLEtBQUtHLEVBQUUsRUFBRTtnQkFDNUI7WUFDRjtZQUVBakIscUJBQXFCZ0I7WUFDckJsQixnQkFBZ0I7UUFDbEI7b0RBQUc7UUFBQ0w7UUFBT0M7UUFBa0JDO0tBQW1CO0lBRWhELE1BQU1zQyxxQkFBcUJyRSxrREFBV0E7Z0RBQUM7WUFDckNrQyxnQkFBZ0I7WUFDaEIsTUFBTW9DLFVBQVV6QyxNQUFNb0IsTUFBTTtnRUFBQ0MsQ0FBQUEsT0FBUUEsS0FBS0MsSUFBSSxLQUFLOztZQUNuRCxNQUFNQyxVQUFVLEVBQUU7WUFFbEIsS0FBSyxNQUFNRixRQUFRb0IsUUFBUztnQkFDMUJ4QyxpQkFBaUJvQixLQUFLRyxFQUFFLEVBQUU7Z0JBQzFCdEIsbUJBQW1CbUIsS0FBS0csRUFBRSxFQUFFO2dCQUU1QixpQ0FBaUM7Z0JBQ2pDLElBQUlDLG1CQUEwQztnQkFDOUNBLG1CQUFtQkM7NERBQVk7d0JBQzdCeEIsbUJBQW1CbUIsS0FBS0csRUFBRSxFQUFFRyxLQUFLQyxHQUFHLENBQUMsSUFBSUQsS0FBS0UsTUFBTSxLQUFLO29CQUMzRDsyREFBRztnQkFFSCxJQUFJO29CQUNGLE1BQU1DLFNBQVMsTUFBTXBDLHFEQUFRQSxDQUFDZ0Qsb0JBQW9CLENBQUNyQixLQUFLVyxJQUFJO29CQUM1RCxJQUFJUCxrQkFBa0JRLGNBQWNSO29CQUNwQ3ZCLG1CQUFtQm1CLEtBQUtHLEVBQUUsRUFBRTtvQkFDNUJ2QixpQkFBaUJvQixLQUFLRyxFQUFFLEVBQUVNLE9BQU9JLE9BQU8sR0FBRyxjQUFjLFNBQVNKO29CQUNsRVAsUUFBUVksSUFBSSxDQUFDO3dCQUFFZDt3QkFBTVM7b0JBQU87Z0JBQzlCLEVBQUUsT0FBT00sT0FBTztvQkFDZCxJQUFJWCxrQkFBa0JRLGNBQWNSO29CQUNwQzNCLHNEQUFNQSxDQUFDc0MsS0FBSyxDQUFDLGNBQWMscUNBQXFDQSxPQUFPO3dCQUNyRUMsUUFBUWhCLEtBQUtHLEVBQUU7d0JBQ2ZjLFVBQVVqQixLQUFLa0IsSUFBSTtvQkFDckI7b0JBQ0F0QyxpQkFBaUJvQixLQUFLRyxFQUFFLEVBQUU7Z0JBQzVCO1lBQ0Y7WUFFQWpCLHFCQUFxQmdCO1lBQ3JCbEIsZ0JBQWdCO1FBQ2xCOytDQUFHO1FBQUNMO1FBQU9DO1FBQWtCQztLQUFtQjtJQUVoRCxNQUFNeUMsV0FBVzNDLE1BQU1vQixNQUFNLENBQUN3QixDQUFBQSxJQUFLQSxFQUFFdEIsSUFBSSxLQUFLLE9BQU91QixNQUFNO0lBQzNELE1BQU1DLFVBQVU5QyxNQUFNb0IsTUFBTSxDQUFDd0IsQ0FBQUEsSUFBS0EsRUFBRXRCLElBQUksS0FBSyxNQUFNdUIsTUFBTTtJQUN6RCxNQUFNRSxpQkFBaUIvQyxNQUFNb0IsTUFBTSxDQUFDd0IsQ0FBQUEsSUFBS0EsRUFBRUksTUFBTSxLQUFLLGFBQWFILE1BQU07SUFFekUsZ0NBQWdDO0lBQ2hDLE1BQU1JLGtCQUFrQjlFLGtEQUFXQTs2Q0FBQyxPQUFPa0Q7WUFDekMsSUFBSTtnQkFDRixvQ0FBb0M7Z0JBQ3BDLE1BQU02QixXQUFXLE1BQU14RCxxREFBUUEsQ0FBQ3lELGVBQWUsQ0FBQzlCLEtBQUtXLElBQUk7Z0JBQ3pELElBQUlrQixTQUFTaEIsT0FBTyxJQUFJZ0IsU0FBU0UsT0FBTyxFQUFFO29CQUN4Q3pDLHVCQUF1QnVDLFNBQVNFLE9BQU87b0JBQ3ZDdkMsb0JBQW9CUSxLQUFLQyxJQUFJO29CQUM3QlAsb0JBQW9CTSxLQUFLa0IsSUFBSTtnQkFDL0I7WUFDRixFQUFFLE9BQU9ILE9BQU87Z0JBQ2R0QyxzREFBTUEsQ0FBQ3NDLEtBQUssQ0FBQyxlQUFlLCtCQUErQkEsT0FBTztvQkFDaEVFLFVBQVVqQixLQUFLa0IsSUFBSTtvQkFDbkJjLFVBQVVoQyxLQUFLVyxJQUFJO2dCQUNyQjtnQkFDQXNCLFFBQVFsQixLQUFLLENBQUMsZ0NBQWdDQTtZQUNoRDtRQUNGOzRDQUFHLEVBQUU7SUFFTCw0Q0FBNEM7SUFDNUN4QyxnREFBU0E7MEJBQUM7WUFDUixJQUFJSSxNQUFNNkMsTUFBTSxHQUFHLEtBQUtyQyxlQUFlLENBQUNFLHFCQUFxQjtnQkFDM0QsTUFBTTZDLFlBQVl2RCxLQUFLLENBQUMsRUFBRTtnQkFDMUIsSUFBSXVELFdBQVc7b0JBQ2JOLGdCQUFnQk07Z0JBQ2xCO1lBQ0Y7UUFDRjt5QkFBRztRQUFDdkQ7UUFBT1E7UUFBYUU7UUFBcUJ1QztLQUFnQjtJQUU3RCxxQkFDRSw4REFBQ3BELCtFQUF1QkE7a0JBQ3hCLDRFQUFDMkQ7WUFBSUMsV0FBVTtzQkFDYiw0RUFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUViLDhEQUFDckYsa0RBQU1BLENBQUNvRixHQUFHO3dCQUNURSxTQUFTOzRCQUFFQyxTQUFTOzRCQUFHQyxHQUFHLENBQUM7d0JBQUc7d0JBQzlCQyxTQUFTOzRCQUFFRixTQUFTOzRCQUFHQyxHQUFHO3dCQUFFO3dCQUM1QkgsV0FBVTs7MENBRVYsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ3JGLGtEQUFNQSxDQUFDb0YsR0FBRzt3Q0FDVEssU0FBUzs0Q0FBRUMsUUFBUTtnREFBQztnREFBRzs2Q0FBSTt3Q0FBQzt3Q0FDNUJDLFlBQVk7NENBQUVDLFVBQVU7NENBQUlDLFFBQVFDOzRDQUFVQyxNQUFNO3dDQUFTO2tEQUU3RCw0RUFBQzFGLHVJQUFRQTs0Q0FBQ2dGLFdBQVU7Ozs7Ozs7Ozs7O2tEQUV0Qiw4REFBQ1c7d0NBQUdYLFdBQVU7a0RBQXFDOzs7Ozs7Ozs7Ozs7MENBSXJELDhEQUFDWTtnQ0FBRVosV0FBVTswQ0FBZ0M7Ozs7Ozs7Ozs7OztrQ0FNL0MsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FFYiw4REFBQzdFLGtFQUFZQTtnQ0FBQzZFLFdBQVU7Ozs7OzswQ0FHeEIsOERBQUNwRiwyREFBZUE7MENBQ2IyQixNQUFNNkMsTUFBTSxHQUFHLG1CQUNkLDhEQUFDekUsa0RBQU1BLENBQUNvRixHQUFHO29DQUNURSxTQUFTO3dDQUFFQyxTQUFTO3dDQUFHVyxPQUFPO29DQUFLO29DQUNuQ1QsU0FBUzt3Q0FBRUYsU0FBUzt3Q0FBR1csT0FBTztvQ0FBRTtvQ0FDaENDLE1BQU07d0NBQUVaLFNBQVM7d0NBQUdXLE9BQU87b0NBQUs7b0NBQ2hDUCxZQUFZO3dDQUFFQyxVQUFVO29DQUFJOzhDQUU1Qiw0RUFBQy9FLHFEQUFJQTt3Q0FBQ3dFLFdBQVU7a0RBQ2QsNEVBQUNEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0Q7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDbEYsdUlBQVFBOzREQUFDa0YsV0FBVTs7Ozs7O3NFQUNwQiw4REFBQ0Q7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDZTtvRUFBS2YsV0FBVTs7d0VBQWV6RCxNQUFNNkMsTUFBTTt3RUFBQzs7Ozs7Ozs4RUFDNUMsOERBQUNXO29FQUFJQyxXQUFVOzt3RUFDWmQsV0FBVyxtQkFDViw4REFBQ3pELHVEQUFLQTs0RUFBQ3VGLFNBQVE7O2dGQUFXOUI7Z0ZBQVM7Ozs7Ozs7d0VBRXBDRyxVQUFVLG1CQUNULDhEQUFDNUQsdURBQUtBOzRFQUFDdUYsU0FBUTs7Z0ZBQVczQjtnRkFBUTs7Ozs7Ozt3RUFFbkNDLGlCQUFpQixtQkFDaEIsOERBQUM3RCx1REFBS0E7NEVBQUN1RixTQUFROzs4RkFDYiw4REFBQy9GLHVJQUFZQTtvRkFBQytFLFdBQVU7Ozs7OztnRkFDdkJWO2dGQUFlOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2dEQVF6QnZDLGVBQWVSLE1BQU02QyxNQUFNLEdBQUcsbUJBQzdCLDhEQUFDVztvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNlOzREQUFLZixXQUFVO3NFQUFnQzs7Ozs7O3NFQUNoRCw4REFBQ2lCOzREQUNDakIsV0FBVTs0REFDVmtCLE9BQU83RDs0REFDUDhELFVBQVUsQ0FBQ0M7Z0VBQ1QsTUFBTXhELE9BQU9yQixNQUFNOEUsSUFBSSxDQUFDbEMsQ0FBQUEsSUFBS0EsRUFBRUwsSUFBSSxLQUFLc0MsRUFBRUUsTUFBTSxDQUFDSixLQUFLO2dFQUN0RCxJQUFJdEQsTUFBTTRCLGdCQUFnQjVCOzREQUM1QjtzRUFFQ3JCLE1BQU1nRixHQUFHLENBQUMzRCxDQUFBQSxxQkFDVCw4REFBQzREO29FQUFxQk4sT0FBT3RELEtBQUtrQixJQUFJOzhFQUNuQ2xCLEtBQUtrQixJQUFJO21FQURDbEIsS0FBS0csRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs4REFRNUIsOERBQUNnQztvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUN6RSx5REFBTUE7NERBQ0xrRyxTQUFTaEU7NERBQ1RpRSxVQUFVeEMsYUFBYSxLQUFLdkM7NERBQzVCcUQsV0FBVTs7Z0VBQ1g7OEVBRUMsOERBQUNuRix1SUFBVUE7b0VBQUNtRixXQUFVOzs7Ozs7Ozs7Ozs7c0VBRXhCLDhEQUFDekUseURBQU1BOzREQUNMa0csU0FBUzFDOzREQUNUMkMsVUFBVXJDLFlBQVksS0FBSzFDOzREQUMzQnFFLFNBQVE7NERBQ1JoQixXQUFVOztnRUFDWDs4RUFFQyw4REFBQ25GLHVJQUFVQTtvRUFBQ21GLFdBQVU7Ozs7Ozs7Ozs7OztzRUFFeEIsOERBQUN6RSx5REFBTUE7NERBQ0xrRyxTQUFTL0U7NERBQ1RzRSxTQUFROzREQUNSVSxVQUFVL0U7c0VBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FXYiw4REFBQy9CLDJEQUFlQTswQ0FDYjJCLE1BQU02QyxNQUFNLEdBQUcsS0FBSzdDLE1BQU1vRixJQUFJLENBQUN4QyxDQUFBQSxJQUFLQSxFQUFFSSxNQUFNLEtBQUsseUJBQ2hELDhEQUFDNUUsa0RBQU1BLENBQUNvRixHQUFHO29DQUNURSxTQUFTO3dDQUFFQyxTQUFTO3dDQUFHQyxHQUFHO29DQUFHO29DQUM3QkMsU0FBUzt3Q0FBRUYsU0FBUzt3Q0FBR0MsR0FBRztvQ0FBRTtvQ0FDNUJXLE1BQU07d0NBQUVaLFNBQVM7d0NBQUdDLEdBQUc7b0NBQUc7OENBRTFCLDRFQUFDL0UsOEVBQWtCQTt3Q0FDakJ3RyxZQUFZLE9BQU9oRTs0Q0FDakIsSUFBSTtnREFDRixNQUFNMUIsbUVBQWVBLENBQUMyRixZQUFZLENBQUNqRTs0Q0FDckMsRUFBRSxPQUFPZSxPQUFPO2dEQUNkdEMsc0RBQU1BLENBQUNzQyxLQUFLLENBQUMsWUFBWSx3QkFBd0JBLE9BQU87b0RBQ3REQyxRQUFRaEIsS0FBS0csRUFBRTtvREFDZmMsVUFBVWpCLEtBQUtrQixJQUFJO2dEQUNyQjtnREFDQWUsUUFBUWxCLEtBQUssQ0FBQyxvQkFBb0JBOzRDQUNwQzt3Q0FDRjt3Q0FDQW1ELFdBQVcsQ0FBQ2xFO2dEQUNOQTs0Q0FBSixLQUFJQSxlQUFBQSxLQUFLUyxNQUFNLGNBQVhULG1DQUFBQSxhQUFhK0IsT0FBTyxFQUFFO29EQUVKL0IsdUJBQzJDQTtnREFGL0RWLHVCQUF1QlUsS0FBS1MsTUFBTSxDQUFDc0IsT0FBTztnREFDMUN2QyxvQkFBb0JRLEVBQUFBLHdCQUFBQSxLQUFLUyxNQUFNLENBQUMwRCxRQUFRLGNBQXBCbkUsNENBQUFBLHNCQUFzQm9FLGVBQWUsS0FBaUM7Z0RBQzFGMUUsb0JBQW9CTSxLQUFLQSxJQUFJLENBQUNrQixJQUFJLENBQUNtRCxPQUFPLENBQUMsZ0JBQWdCLElBQWtELE9BQTlDckUsRUFBQUEseUJBQUFBLEtBQUtTLE1BQU0sQ0FBQzBELFFBQVEsY0FBcEJuRSw2Q0FBQUEsdUJBQXNCb0UsZUFBZSxLQUFJO2dEQUN4R2hGLGVBQWU7NENBQ2pCO3dDQUNGO3dDQUNBa0YsU0FBUyxPQUFPdEU7NENBQ2Qsb0NBQW9DOzRDQUNwQ3BCLGlCQUFpQm9CLEtBQUtHLEVBQUUsRUFBRTs0Q0FDMUJ0QixtQkFBbUJtQixLQUFLRyxFQUFFLEVBQUU7NENBRTVCLElBQUk7Z0RBQ0YsTUFBTU0sU0FBU1QsS0FBS0EsSUFBSSxDQUFDQyxJQUFJLEtBQUssUUFDOUIsTUFBTTVCLHFEQUFRQSxDQUFDcUMsb0JBQW9CLENBQUNWLEtBQUtBLElBQUksQ0FBQ1csSUFBSSxJQUNsRCxNQUFNdEMscURBQVFBLENBQUNnRCxvQkFBb0IsQ0FBQ3JCLEtBQUtBLElBQUksQ0FBQ1csSUFBSTtnREFFdEQvQixpQkFBaUJvQixLQUFLRyxFQUFFLEVBQUVNLE9BQU9JLE9BQU8sR0FBRyxjQUFjLFNBQVNKOzRDQUNwRSxFQUFFLE9BQU9NLE9BQU87Z0RBQ2R0QyxzREFBTUEsQ0FBQ3NDLEtBQUssQ0FBQyxjQUFjLDJCQUEyQkEsT0FBTztvREFDM0RDLFFBQVFoQixLQUFLRyxFQUFFO29EQUNmYyxVQUFVakIsS0FBS0EsSUFBSSxDQUFDa0IsSUFBSTtvREFDeEJxRCxVQUFVdkUsS0FBS0EsSUFBSSxDQUFDQyxJQUFJO2dEQUMxQjtnREFDQXJCLGlCQUFpQm9CLEtBQUtHLEVBQUUsRUFBRTs0Q0FDNUI7d0NBQ0Y7Ozs7Ozs7Ozs7Ozs7Ozs7MENBT1IsOERBQUNuRCwyREFBZUE7MENBQ2IyQixNQUFNNkMsTUFBTSxHQUFHLG1CQUNkLDhEQUFDekUsa0RBQU1BLENBQUNvRixHQUFHO29DQUNURSxTQUFTO3dDQUFFQyxTQUFTO3dDQUFHQyxHQUFHO29DQUFHO29DQUM3QkMsU0FBUzt3Q0FBRUYsU0FBUzt3Q0FBR0MsR0FBRztvQ0FBRTtvQ0FDNUJXLE1BQU07d0NBQUVaLFNBQVM7d0NBQUdDLEdBQUc7b0NBQUc7b0NBQzFCSCxXQUFVOztzREFFViw4REFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDakUsd0RBQUtBO29EQUFDcUcsU0FBUTtvREFBaUJwQyxXQUFVOzhEQUFzQjs7Ozs7OzhEQUdoRSw4REFBQ2xFLDBEQUFNQTtvREFDTGlDLElBQUc7b0RBQ0hzRSxTQUFTdEY7b0RBQ1R1RixpQkFBaUJ0Rjs7Ozs7Ozs7Ozs7O3NEQUdyQiw4REFBQytDOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ1k7b0RBQUVaLFdBQVU7OERBQ1ZqRCxjQUFjLG9CQUFvQjs7Ozs7O2dEQUVwQ3VDLGlCQUFpQixtQkFDaEIsOERBQUMvRCx5REFBTUE7b0RBQ0xnSCxNQUFLO29EQUNMdkIsU0FBUTtvREFDUlMsU0FBUyxJQUFNakUsdUJBQXVCO29EQUN0Q3dDLFdBQVU7O3NFQUVWLDhEQUFDOUUsdUlBQU9BOzREQUFDOEUsV0FBVTs7Ozs7O3dEQUFZO3dEQUNaVjt3REFBZTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQVM5Qyw4REFBQzFFLDJEQUFlQTswQ0FDYm1DLGVBQWVFLHFDQUNkLDhEQUFDdEMsa0RBQU1BLENBQUNvRixHQUFHO29DQUNURSxTQUFTO3dDQUFFQyxTQUFTO3dDQUFHc0MsUUFBUTtvQ0FBRTtvQ0FDakNwQyxTQUFTO3dDQUFFRixTQUFTO3dDQUFHc0MsUUFBUTtvQ0FBTztvQ0FDdEMxQixNQUFNO3dDQUFFWixTQUFTO3dDQUFHc0MsUUFBUTtvQ0FBRTtvQ0FDOUJsQyxZQUFZO3dDQUFFQyxVQUFVO29DQUFJO29DQUM1QlAsV0FBVTs4Q0FFViw0RUFBQzNFLGtFQUFZQTt3Q0FDWG9ILGVBQWV4Rjt3Q0FDZnlGLFlBQVl2Rjt3Q0FDWjBCLFVBQVV4Qjt3Q0FDVjJDLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7MENBT2xCLDhEQUFDcEYsMkRBQWVBOzBDQUNiaUMsa0JBQWtCdUMsTUFBTSxHQUFHLEtBQUssQ0FBQ3JDLDZCQUNoQyw4REFBQ3BDLGtEQUFNQSxDQUFDb0YsR0FBRztvQ0FDVEUsU0FBUzt3Q0FBRUMsU0FBUzt3Q0FBR0MsR0FBRztvQ0FBRztvQ0FDN0JDLFNBQVM7d0NBQUVGLFNBQVM7d0NBQUdDLEdBQUc7b0NBQUU7b0NBQzVCVyxNQUFNO3dDQUFFWixTQUFTO3dDQUFHQyxHQUFHO29DQUFHOzhDQUUxQiw0RUFBQzNFLHFEQUFJQTtrREFDSCw0RUFBQ3VFOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQzJDO29EQUFHM0MsV0FBVTs7c0VBQ1osOERBQUMvRSx1SUFBWUE7NERBQUMrRSxXQUFVOzs7Ozs7d0RBQTJCOzs7Ozs7OzhEQUlyRCw4REFBQ3RFLHFEQUFJQTtvREFBQ2tILGNBQWE7b0RBQVU1QyxXQUFVOztzRUFDckMsOERBQUNwRSx5REFBUUE7NERBQUNvRSxXQUFVOzs4RUFDbEIsOERBQUNuRSw0REFBV0E7b0VBQUNxRixPQUFNOzhFQUFVOzs7Ozs7OEVBQzdCLDhEQUFDckYsNERBQVdBO29FQUFDcUYsT0FBTTs4RUFBVzs7Ozs7Ozs7Ozs7O3NFQUdoQyw4REFBQ3ZGLDREQUFXQTs0REFBQ3VGLE9BQU07NERBQVVsQixXQUFVO3NFQUNyQyw0RUFBQ0Q7Z0VBQUlDLFdBQVU7MEVBQ1puRCxrQkFBa0IwRSxHQUFHLENBQUMsQ0FBQ2xELFFBQVF3RSxzQkFDOUIsOERBQUNsSSxrREFBTUEsQ0FBQ29GLEdBQUc7d0VBRVRFLFNBQVM7NEVBQUVDLFNBQVM7NEVBQUc0QyxHQUFHLENBQUM7d0VBQUc7d0VBQzlCMUMsU0FBUzs0RUFBRUYsU0FBUzs0RUFBRzRDLEdBQUc7d0VBQUU7d0VBQzVCeEMsWUFBWTs0RUFBRXlDLE9BQU9GLFFBQVE7d0VBQUk7d0VBQ2pDN0MsV0FBVTs7MEZBRVYsOERBQUNEO2dGQUFJQyxXQUFVOztrR0FDYiw4REFBQ2U7d0ZBQUtmLFdBQVU7a0dBQWUzQixPQUFPVCxJQUFJLENBQUNrQixJQUFJOzs7Ozs7a0dBQy9DLDhEQUFDckQsdURBQUtBO3dGQUFDdUYsU0FBUzNDLE9BQU9BLE1BQU0sQ0FBQ0ksT0FBTyxHQUFHLFlBQVk7a0dBQ2pESixPQUFPQSxNQUFNLENBQUNJLE9BQU8sR0FBRyxZQUFZOzs7Ozs7Ozs7Ozs7NEVBR3hDSixPQUFPQSxNQUFNLENBQUNzQixPQUFPLGtCQUNwQiw4REFBQ3FEO2dGQUFJaEQsV0FBVTs7b0ZBQ1ozQixPQUFPQSxNQUFNLENBQUNzQixPQUFPLENBQUNzRCxLQUFLLENBQUMsR0FBRztvRkFBSzs7Ozs7Ozs7dUVBZHBDSjs7Ozs7Ozs7Ozs7Ozs7O3NFQXNCYiw4REFBQ2xILDREQUFXQTs0REFBQ3VGLE9BQU07NERBQVdsQixXQUFVO3NFQUN0Qyw0RUFBQ0Q7Z0VBQUlDLFdBQVU7MEVBQ1puRCxrQkFDRWMsTUFBTSxDQUFDdUYsQ0FBQUEsSUFBS0EsRUFBRTdFLE1BQU0sQ0FBQ0ksT0FBTyxFQUM1QjhDLEdBQUcsQ0FBQyxDQUFDbEQsUUFBUXdFO3dFQVVQeEU7eUZBVEwsOERBQUMxRCxrREFBTUEsQ0FBQ29GLEdBQUc7d0VBRVRFLFNBQVM7NEVBQUVDLFNBQVM7NEVBQUdDLEdBQUc7d0VBQUc7d0VBQzdCQyxTQUFTOzRFQUFFRixTQUFTOzRFQUFHQyxHQUFHO3dFQUFFO3dFQUM1QkcsWUFBWTs0RUFBRXlDLE9BQU9GLFFBQVE7d0VBQUs7d0VBQ2xDN0MsV0FBVTs7MEZBRVYsOERBQUNlO2dGQUFLZixXQUFVOztvRkFDYjNCLE9BQU9ULElBQUksQ0FBQ2tCLElBQUksQ0FBQ21ELE9BQU8sQ0FBQyxlQUFlO29GQUN4QzVELEVBQUFBLDBCQUFBQSxPQUFPQSxNQUFNLENBQUMwRCxRQUFRLGNBQXRCMUQsOENBQUFBLHdCQUF3QjJELGVBQWUsTUFBSyxPQUFPLFFBQVE7Ozs7Ozs7MEZBRTlELDhEQUFDekcseURBQU1BO2dGQUNMZ0gsTUFBSztnRkFDTHZCLFNBQVE7Z0ZBQ1JoQixXQUFVO2dGQUNWeUIsU0FBUztvRkFDUCxNQUFNMEIsZ0JBQWdCNUcsTUFBTThFLElBQUksQ0FBQ2xDLENBQUFBLElBQUtBLEVBQUVwQixFQUFFLEtBQUtNLE9BQU9ULElBQUksQ0FBQ0csRUFBRTtvRkFDN0QsSUFBSW9GLGVBQWU7d0ZBQ2pCLElBQUk7NEZBQ0YsTUFBTWpILG1FQUFlQSxDQUFDMkYsWUFBWSxDQUFDc0I7d0ZBQ3JDLEVBQUUsT0FBT3hFLE9BQU87NEZBQ2R0QyxzREFBTUEsQ0FBQ3NDLEtBQUssQ0FBQyxZQUFZLDBCQUEwQkEsT0FBTztnR0FDeERDLFFBQVFQLE9BQU9ULElBQUksQ0FBQ0csRUFBRTtnR0FDdEJjLFVBQVVSLE9BQU9ULElBQUksQ0FBQ2tCLElBQUk7NEZBQzVCOzRGQUNBZSxRQUFRbEIsS0FBSyxDQUFDLG9CQUFvQkE7d0ZBQ3BDO29GQUNGO2dGQUNGOztrR0FFQSw4REFBQzVELHVJQUFRQTt3RkFBQ2lGLFdBQVU7Ozs7OztvRkFBWTs7Ozs7Ozs7dUVBN0I3QjZDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQTRDM0IsOERBQUNqSSwyREFBZUE7MENBQ2IyQyxxQ0FDQyw4REFBQzVDLGtEQUFNQSxDQUFDb0YsR0FBRztvQ0FDVEUsU0FBUzt3Q0FBRUMsU0FBUztvQ0FBRTtvQ0FDdEJFLFNBQVM7d0NBQUVGLFNBQVM7b0NBQUU7b0NBQ3RCWSxNQUFNO3dDQUFFWixTQUFTO29DQUFFO29DQUNuQkYsV0FBVTtvQ0FDVnlCLFNBQVMsSUFBTWpFLHVCQUF1Qjs4Q0FFdEMsNEVBQUM3QyxrREFBTUEsQ0FBQ29GLEdBQUc7d0NBQ1RFLFNBQVM7NENBQUVZLE9BQU87NENBQUtYLFNBQVM7d0NBQUU7d0NBQ2xDRSxTQUFTOzRDQUFFUyxPQUFPOzRDQUFHWCxTQUFTO3dDQUFFO3dDQUNoQ1ksTUFBTTs0Q0FBRUQsT0FBTzs0Q0FBS1gsU0FBUzt3Q0FBRTt3Q0FDL0J1QixTQUFTLENBQUNMLElBQU1BLEVBQUVnQyxlQUFlOzswREFFakMsOERBQUM5SCx3RUFBZUE7Z0RBQ2RpQixPQUFPQTtnREFDUDhHLFNBQVMsSUFBTTdGLHVCQUF1Qjs7Ozs7OzBEQUV4Qyw4REFBQ2pDLHlEQUFNQTtnREFDTHlFLFdBQVU7Z0RBQ1ZnQixTQUFRO2dEQUNSUyxTQUFTLElBQU1qRSx1QkFBdUI7MERBQ3ZDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVluQjtHQXpkd0JsQjs7UUFDOENOLDREQUFZQTs7O0tBRDFETSIsInNvdXJjZXMiOlsiQzpcXGRldlxcbGVnYWN5LWJyaWRnZVxcbGVnYWN5LWJyaWRnZVxcbGVnYWN5YnJpZGdlXFxzcmNcXGFwcFxccGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xyXG5cclxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUNhbGxiYWNrIH0gZnJvbSAncmVhY3QnO1xyXG5pbXBvcnQgeyBtb3Rpb24sIEFuaW1hdGVQcmVzZW5jZSB9IGZyb20gJ2ZyYW1lci1tb3Rpb24nO1xyXG5pbXBvcnQgeyBBcnJvd1JpZ2h0LCBGaWxlVGV4dCwgRG93bmxvYWQsIFNwYXJrbGVzLCBDaGVja0NpcmNsZTIsIEFyY2hpdmUgfSBmcm9tICdsdWNpZGUtcmVhY3QnO1xyXG5pbXBvcnQgeyBEcmFnRHJvcFpvbmUgfSBmcm9tICdAL2NvbXBvbmVudHMvRHJhZ0Ryb3Bab25lJztcclxuaW1wb3J0IHsgQ29udmVyc2lvblByb2dyZXNzIH0gZnJvbSAnQC9jb21wb25lbnRzL0NvbnZlcnNpb25Qcm9ncmVzcyc7XHJcbmltcG9ydCB7IFByZXZpZXdQYW5lbCB9IGZyb20gJ0AvY29tcG9uZW50cy9QcmV2aWV3UGFuZWwnO1xyXG5pbXBvcnQgeyBEb3dubG9hZE1hbmFnZXIgfSBmcm9tICdAL2NvbXBvbmVudHMvRG93bmxvYWRNYW5hZ2VyJztcclxuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2J1dHRvbic7XHJcbmltcG9ydCB7IENhcmQgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvY2FyZCc7XHJcbmltcG9ydCB7IEJhZGdlIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2JhZGdlJztcclxuaW1wb3J0IHsgVGFicywgVGFic0NvbnRlbnQsIFRhYnNMaXN0LCBUYWJzVHJpZ2dlciB9IGZyb20gJ0AvY29tcG9uZW50cy91aS90YWJzJztcclxuaW1wb3J0IHsgU3dpdGNoIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL3N3aXRjaCc7XHJcbmltcG9ydCB7IExhYmVsIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2xhYmVsJztcclxuaW1wb3J0IHsgdXNlRmlsZVN0b3JlIH0gZnJvbSAnQC9saWIvc3RvcmVzL2ZpbGVzJztcclxuaW1wb3J0IHsgdGF1cmlBcGkgfSBmcm9tICdAL2xpYi90YXVyaS1hcGknO1xyXG5pbXBvcnQgeyBkb3dubG9hZFNlcnZpY2UgfSBmcm9tICdAL2xpYi9kb3dubG9hZC1zZXJ2aWNlJztcclxuaW1wb3J0IHsgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xyXG5pbXBvcnQgdHlwZSB7IEZpbGVXaXRoU3RhdHVzIH0gZnJvbSAnQC9saWIvc3RvcmVzL2ZpbGVzJztcclxuaW1wb3J0IHR5cGUgeyBDb252ZXJzaW9uUmVzdWx0IH0gZnJvbSAnQC9saWIvdGF1cmktYXBpJztcclxuaW1wb3J0IHsgRXJyb3JCb3VuZGFyeSwgQ29udmVyc2lvbkVycm9yQm91bmRhcnkgfSBmcm9tICdAL2NvbXBvbmVudHMvRXJyb3JCb3VuZGFyeSc7XHJcbmltcG9ydCB7IGxvZ2dlciB9IGZyb20gJ0AvbGliL2Vycm9yLWxvZ2dlcic7XHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBIb21lKCkge1xyXG4gIGNvbnN0IHsgZmlsZXMsIHVwZGF0ZUZpbGVTdGF0dXMsIHVwZGF0ZUZpbGVQcm9ncmVzcywgY2xlYXJGaWxlcyB9ID0gdXNlRmlsZVN0b3JlKCk7XHJcbiAgY29uc3QgW2lzQ29udmVydGluZywgc2V0SXNDb252ZXJ0aW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbY29udmVyc2lvblJlc3VsdHMsIHNldENvbnZlcnNpb25SZXN1bHRzXSA9IHVzZVN0YXRlPEFycmF5PHsgZmlsZTogRmlsZVdpdGhTdGF0dXM7IHJlc3VsdDogQ29udmVyc2lvblJlc3VsdCB9Pj4oW10pO1xyXG4gIGNvbnN0IFtzaG93UHJldmlldywgc2V0U2hvd1ByZXZpZXddID0gdXNlU3RhdGUodHJ1ZSk7XHJcbiAgY29uc3QgW3NlbGVjdGVkRmlsZUNvbnRlbnQsIHNldFNlbGVjdGVkRmlsZUNvbnRlbnRdID0gdXNlU3RhdGU8c3RyaW5nPignJyk7XHJcbiAgY29uc3QgW3NlbGVjdGVkRmlsZVR5cGUsIHNldFNlbGVjdGVkRmlsZVR5cGVdID0gdXNlU3RhdGU8J3J0ZicgfCAnbWQnIHwgJ21hcmtkb3duJz4oJ3J0ZicpO1xyXG4gIGNvbnN0IFtzZWxlY3RlZEZpbGVOYW1lLCBzZXRTZWxlY3RlZEZpbGVOYW1lXSA9IHVzZVN0YXRlPHN0cmluZz4oJycpO1xyXG4gIGNvbnN0IFtzaG93RG93bmxvYWRNYW5hZ2VyLCBzZXRTaG93RG93bmxvYWRNYW5hZ2VyXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuXHJcbiAgY29uc3QgaGFuZGxlQ29udmVydFRvTWFya2Rvd24gPSB1c2VDYWxsYmFjayhhc3luYyAoKSA9PiB7XHJcbiAgICBzZXRJc0NvbnZlcnRpbmcodHJ1ZSk7XHJcbiAgICBjb25zdCBydGZGaWxlcyA9IGZpbGVzLmZpbHRlcihmaWxlID0+IGZpbGUudHlwZSA9PT0gJ3J0ZicpO1xyXG4gICAgY29uc3QgcmVzdWx0cyA9IFtdO1xyXG5cclxuICAgIGZvciAoY29uc3QgZmlsZSBvZiBydGZGaWxlcykge1xyXG4gICAgICB1cGRhdGVGaWxlU3RhdHVzKGZpbGUuaWQsICdjb252ZXJ0aW5nJyk7XHJcbiAgICAgIHVwZGF0ZUZpbGVQcm9ncmVzcyhmaWxlLmlkLCAwKTtcclxuXHJcbiAgICAgIC8vIFNpbXVsYXRlIHByb2dyZXNzIHdpdGggY2xlYW51cFxyXG4gICAgICBsZXQgcHJvZ3Jlc3NJbnRlcnZhbDogTm9kZUpTLlRpbWVvdXQgfCBudWxsID0gbnVsbDtcclxuICAgICAgcHJvZ3Jlc3NJbnRlcnZhbCA9IHNldEludGVydmFsKCgpID0+IHtcclxuICAgICAgICB1cGRhdGVGaWxlUHJvZ3Jlc3MoZmlsZS5pZCwgTWF0aC5taW4oOTAsIE1hdGgucmFuZG9tKCkgKiAxMDApKTtcclxuICAgICAgfSwgMjAwKTtcclxuXHJcbiAgICAgIHRyeSB7XHJcbiAgICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgdGF1cmlBcGkuY29udmVydFJ0ZlRvTWFya2Rvd24oZmlsZS5wYXRoKTtcclxuICAgICAgICBpZiAocHJvZ3Jlc3NJbnRlcnZhbCkgY2xlYXJJbnRlcnZhbChwcm9ncmVzc0ludGVydmFsKTtcclxuICAgICAgICB1cGRhdGVGaWxlUHJvZ3Jlc3MoZmlsZS5pZCwgMTAwKTtcclxuICAgICAgICB1cGRhdGVGaWxlU3RhdHVzKGZpbGUuaWQsIHJlc3VsdC5zdWNjZXNzID8gJ2NvbXBsZXRlZCcgOiAnZXJyb3InLCByZXN1bHQpO1xyXG4gICAgICAgIHJlc3VsdHMucHVzaCh7IGZpbGUsIHJlc3VsdCB9KTtcclxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICBpZiAocHJvZ3Jlc3NJbnRlcnZhbCkgY2xlYXJJbnRlcnZhbChwcm9ncmVzc0ludGVydmFsKTtcclxuICAgICAgICBsb2dnZXIuZXJyb3IoJ0NvbnZlcnNpb24nLCAnRmFpbGVkIHRvIGNvbnZlcnQgUlRGIHRvIE1hcmtkb3duJywgZXJyb3IsIHsgXHJcbiAgICAgICAgICBmaWxlSWQ6IGZpbGUuaWQsXHJcbiAgICAgICAgICBmaWxlTmFtZTogZmlsZS5uYW1lIFxyXG4gICAgICAgIH0pO1xyXG4gICAgICAgIHVwZGF0ZUZpbGVTdGF0dXMoZmlsZS5pZCwgJ2Vycm9yJyk7XHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICBzZXRDb252ZXJzaW9uUmVzdWx0cyhyZXN1bHRzKTtcclxuICAgIHNldElzQ29udmVydGluZyhmYWxzZSk7XHJcbiAgfSwgW2ZpbGVzLCB1cGRhdGVGaWxlU3RhdHVzLCB1cGRhdGVGaWxlUHJvZ3Jlc3NdKTtcclxuXHJcbiAgY29uc3QgaGFuZGxlQ29udmVydFRvUnRmID0gdXNlQ2FsbGJhY2soYXN5bmMgKCkgPT4ge1xyXG4gICAgc2V0SXNDb252ZXJ0aW5nKHRydWUpO1xyXG4gICAgY29uc3QgbWRGaWxlcyA9IGZpbGVzLmZpbHRlcihmaWxlID0+IGZpbGUudHlwZSA9PT0gJ21kJyk7XHJcbiAgICBjb25zdCByZXN1bHRzID0gW107XHJcblxyXG4gICAgZm9yIChjb25zdCBmaWxlIG9mIG1kRmlsZXMpIHtcclxuICAgICAgdXBkYXRlRmlsZVN0YXR1cyhmaWxlLmlkLCAnY29udmVydGluZycpO1xyXG4gICAgICB1cGRhdGVGaWxlUHJvZ3Jlc3MoZmlsZS5pZCwgMCk7XHJcblxyXG4gICAgICAvLyBTaW11bGF0ZSBwcm9ncmVzcyB3aXRoIGNsZWFudXBcclxuICAgICAgbGV0IHByb2dyZXNzSW50ZXJ2YWw6IE5vZGVKUy5UaW1lb3V0IHwgbnVsbCA9IG51bGw7XHJcbiAgICAgIHByb2dyZXNzSW50ZXJ2YWwgPSBzZXRJbnRlcnZhbCgoKSA9PiB7XHJcbiAgICAgICAgdXBkYXRlRmlsZVByb2dyZXNzKGZpbGUuaWQsIE1hdGgubWluKDkwLCBNYXRoLnJhbmRvbSgpICogMTAwKSk7XHJcbiAgICAgIH0sIDIwMCk7XHJcblxyXG4gICAgICB0cnkge1xyXG4gICAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHRhdXJpQXBpLmNvbnZlcnRNYXJrZG93blRvUnRmKGZpbGUucGF0aCk7XHJcbiAgICAgICAgaWYgKHByb2dyZXNzSW50ZXJ2YWwpIGNsZWFySW50ZXJ2YWwocHJvZ3Jlc3NJbnRlcnZhbCk7XHJcbiAgICAgICAgdXBkYXRlRmlsZVByb2dyZXNzKGZpbGUuaWQsIDEwMCk7XHJcbiAgICAgICAgdXBkYXRlRmlsZVN0YXR1cyhmaWxlLmlkLCByZXN1bHQuc3VjY2VzcyA/ICdjb21wbGV0ZWQnIDogJ2Vycm9yJywgcmVzdWx0KTtcclxuICAgICAgICByZXN1bHRzLnB1c2goeyBmaWxlLCByZXN1bHQgfSk7XHJcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgaWYgKHByb2dyZXNzSW50ZXJ2YWwpIGNsZWFySW50ZXJ2YWwocHJvZ3Jlc3NJbnRlcnZhbCk7XHJcbiAgICAgICAgbG9nZ2VyLmVycm9yKCdDb252ZXJzaW9uJywgJ0ZhaWxlZCB0byBjb252ZXJ0IE1hcmtkb3duIHRvIFJURicsIGVycm9yLCB7IFxyXG4gICAgICAgICAgZmlsZUlkOiBmaWxlLmlkLFxyXG4gICAgICAgICAgZmlsZU5hbWU6IGZpbGUubmFtZSBcclxuICAgICAgICB9KTtcclxuICAgICAgICB1cGRhdGVGaWxlU3RhdHVzKGZpbGUuaWQsICdlcnJvcicpO1xyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgc2V0Q29udmVyc2lvblJlc3VsdHMocmVzdWx0cyk7XHJcbiAgICBzZXRJc0NvbnZlcnRpbmcoZmFsc2UpO1xyXG4gIH0sIFtmaWxlcywgdXBkYXRlRmlsZVN0YXR1cywgdXBkYXRlRmlsZVByb2dyZXNzXSk7XHJcblxyXG4gIGNvbnN0IHJ0ZkNvdW50ID0gZmlsZXMuZmlsdGVyKGYgPT4gZi50eXBlID09PSAncnRmJykubGVuZ3RoO1xyXG4gIGNvbnN0IG1kQ291bnQgPSBmaWxlcy5maWx0ZXIoZiA9PiBmLnR5cGUgPT09ICdtZCcpLmxlbmd0aDtcclxuICBjb25zdCBjb21wbGV0ZWRDb3VudCA9IGZpbGVzLmZpbHRlcihmID0+IGYuc3RhdHVzID09PSAnY29tcGxldGVkJykubGVuZ3RoO1xyXG5cclxuICAvLyBMb2FkIGZpbGUgY29udGVudCBmb3IgcHJldmlld1xyXG4gIGNvbnN0IGxvYWRGaWxlQ29udGVudCA9IHVzZUNhbGxiYWNrKGFzeW5jIChmaWxlOiBGaWxlV2l0aFN0YXR1cykgPT4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgLy8gUmVhZCBmaWxlIGNvbnRlbnQgdXNpbmcgVGF1cmkgQVBJXHJcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGF1cmlBcGkucmVhZEZpbGVDb250ZW50KGZpbGUucGF0aCk7XHJcbiAgICAgIGlmIChyZXNwb25zZS5zdWNjZXNzICYmIHJlc3BvbnNlLmNvbnRlbnQpIHtcclxuICAgICAgICBzZXRTZWxlY3RlZEZpbGVDb250ZW50KHJlc3BvbnNlLmNvbnRlbnQpO1xyXG4gICAgICAgIHNldFNlbGVjdGVkRmlsZVR5cGUoZmlsZS50eXBlKTtcclxuICAgICAgICBzZXRTZWxlY3RlZEZpbGVOYW1lKGZpbGUubmFtZSk7XHJcbiAgICAgIH1cclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGxvZ2dlci5lcnJvcignRmlsZVByZXZpZXcnLCAnRmFpbGVkIHRvIGxvYWQgZmlsZSBjb250ZW50JywgZXJyb3IsIHsgXHJcbiAgICAgICAgZmlsZU5hbWU6IGZpbGUubmFtZSxcclxuICAgICAgICBmaWxlUGF0aDogZmlsZS5wYXRoIFxyXG4gICAgICB9KTtcclxuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGxvYWQgZmlsZSBjb250ZW50OicsIGVycm9yKTtcclxuICAgIH1cclxuICB9LCBbXSk7XHJcblxyXG4gIC8vIEF1dG8tbG9hZCBmaXJzdCBmaWxlIHdoZW4gZmlsZXMgYXJlIGFkZGVkXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGlmIChmaWxlcy5sZW5ndGggPiAwICYmIHNob3dQcmV2aWV3ICYmICFzZWxlY3RlZEZpbGVDb250ZW50KSB7XHJcbiAgICAgIGNvbnN0IGZpcnN0RmlsZSA9IGZpbGVzWzBdO1xyXG4gICAgICBpZiAoZmlyc3RGaWxlKSB7XHJcbiAgICAgICAgbG9hZEZpbGVDb250ZW50KGZpcnN0RmlsZSk7XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9LCBbZmlsZXMsIHNob3dQcmV2aWV3LCBzZWxlY3RlZEZpbGVDb250ZW50LCBsb2FkRmlsZUNvbnRlbnRdKTtcclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxDb252ZXJzaW9uRXJyb3JCb3VuZGFyeT5cclxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYWRpZW50LXRvLWIgZnJvbS1iYWNrZ3JvdW5kIHRvLXNlY29uZGFyeS8yMFwiPlxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lciBteC1hdXRvIHB4LTQgcHktMTIgbWF4LXctNXhsXCI+XHJcbiAgICAgICAgey8qIEhlYWRlciAqL31cclxuICAgICAgICA8bW90aW9uLmRpdlxyXG4gICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiAtMjAgfX1cclxuICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSwgeTogMCB9fVxyXG4gICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgbWItMTJcIlxyXG4gICAgICAgID5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIGdhcC0yIG1iLTRcIj5cclxuICAgICAgICAgICAgPG1vdGlvbi5kaXZcclxuICAgICAgICAgICAgICBhbmltYXRlPXt7IHJvdGF0ZTogWzAsIDM2MF0gfX1cclxuICAgICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAyMCwgcmVwZWF0OiBJbmZpbml0eSwgZWFzZTogXCJsaW5lYXJcIiB9fVxyXG4gICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgPFNwYXJrbGVzIGNsYXNzTmFtZT1cInctNiBoLTYgdGV4dC1wcmltYXJ5XCIgLz5cclxuICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxyXG4gICAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC00eGwgZm9udC1ib2xkIHRleHQtZm9yZWdyb3VuZFwiPlxyXG4gICAgICAgICAgICAgIExlZ2FjeUJyaWRnZVxyXG4gICAgICAgICAgICA8L2gxPlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LW11dGVkLWZvcmVncm91bmQgdGV4dC1sZ1wiPlxyXG4gICAgICAgICAgICBDb252ZXJ0IGJldHdlZW4gUlRGIGFuZCBNYXJrZG93biB3aXRoIGVhc2VcclxuICAgICAgICAgIDwvcD5cclxuICAgICAgICA8L21vdGlvbi5kaXY+XHJcblxyXG4gICAgICAgIHsvKiBNYWluIENvbnRlbnQgKi99XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LThcIj5cclxuICAgICAgICAgIHsvKiBEcm9wIFpvbmUgKi99XHJcbiAgICAgICAgICA8RHJhZ0Ryb3Bab25lIGNsYXNzTmFtZT1cIm1iLThcIiAvPlxyXG5cclxuICAgICAgICAgIHsvKiBDb252ZXJzaW9uIEFjdGlvbnMgKi99XHJcbiAgICAgICAgICA8QW5pbWF0ZVByZXNlbmNlPlxyXG4gICAgICAgICAgICB7ZmlsZXMubGVuZ3RoID4gMCAmJiAoXHJcbiAgICAgICAgICAgICAgPG1vdGlvbi5kaXZcclxuICAgICAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgc2NhbGU6IDAuOTUgfX1cclxuICAgICAgICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSwgc2NhbGU6IDEgfX1cclxuICAgICAgICAgICAgICAgIGV4aXQ9e3sgb3BhY2l0eTogMCwgc2NhbGU6IDAuOTUgfX1cclxuICAgICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuMyB9fVxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIDxDYXJkIGNsYXNzTmFtZT1cInAtNlwiPlxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgc206ZmxleC1yb3cgaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBnYXAtNFwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxGaWxlVGV4dCBjbGFzc05hbWU9XCJ3LTUgaC01IHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc21cIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj57ZmlsZXMubGVuZ3RofSBmaWxlcyBzZWxlY3RlZDwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGdhcC0yIG10LTFcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICB7cnRmQ291bnQgPiAwICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxCYWRnZSB2YXJpYW50PVwib3V0bGluZVwiPntydGZDb3VudH0gUlRGPC9CYWRnZT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHttZENvdW50ID4gMCAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QmFkZ2UgdmFyaWFudD1cIm91dGxpbmVcIj57bWRDb3VudH0gTWFya2Rvd248L0JhZGdlPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAge2NvbXBsZXRlZENvdW50ID4gMCAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QmFkZ2UgdmFyaWFudD1cImRlZmF1bHRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPENoZWNrQ2lyY2xlMiBjbGFzc05hbWU9XCJ3LTMgaC0zIG1yLTFcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Y29tcGxldGVkQ291bnR9IGNvbXBsZXRlZFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9CYWRnZT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICAgICAgICB7LyogRmlsZSBzZWxlY3RvciBmb3IgcHJldmlldyAqL31cclxuICAgICAgICAgICAgICAgICAgICB7c2hvd1ByZXZpZXcgJiYgZmlsZXMubGVuZ3RoID4gMSAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yIG10LTJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5QcmV2aWV3Ojwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPHNlbGVjdFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQteHMgYmctYmFja2dyb3VuZCBib3JkZXIgcm91bmRlZCBweC0yIHB5LTFcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtzZWxlY3RlZEZpbGVOYW1lfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgZmlsZSA9IGZpbGVzLmZpbmQoZiA9PiBmLm5hbWUgPT09IGUudGFyZ2V0LnZhbHVlKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmIChmaWxlKSBsb2FkRmlsZUNvbnRlbnQoZmlsZSk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtmaWxlcy5tYXAoZmlsZSA9PiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIGtleT17ZmlsZS5pZH0gdmFsdWU9e2ZpbGUubmFtZX0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtmaWxlLm5hbWV9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L29wdGlvbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICApKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9zZWxlY3Q+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICApfVxyXG5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZ2FwLTNcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlQ29udmVydFRvTWFya2Rvd259XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtydGZDb3VudCA9PT0gMCB8fCBpc0NvbnZlcnRpbmd9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImdyb3VwXCJcclxuICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgQ29udmVydCB0byBNYXJrZG93blxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8QXJyb3dSaWdodCBjbGFzc05hbWU9XCJtbC0yIHctNCBoLTQgZ3JvdXAtaG92ZXI6dHJhbnNsYXRlLXgtMSB0cmFuc2l0aW9uLXRyYW5zZm9ybVwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlQ29udmVydFRvUnRmfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17bWRDb3VudCA9PT0gMCB8fCBpc0NvbnZlcnRpbmd9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJzZWNvbmRhcnlcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJncm91cFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIENvbnZlcnQgdG8gUlRGXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxBcnJvd1JpZ2h0IGNsYXNzTmFtZT1cIm1sLTIgdy00IGgtNCBncm91cC1ob3Zlcjp0cmFuc2xhdGUteC0xIHRyYW5zaXRpb24tdHJhbnNmb3JtXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtjbGVhckZpbGVzfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtpc0NvbnZlcnRpbmd9XHJcbiAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIENsZWFyIEFsbFxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgPC9DYXJkPlxyXG4gICAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cclxuICAgICAgICAgICAgKX1cclxuICAgICAgICAgIDwvQW5pbWF0ZVByZXNlbmNlPlxyXG5cclxuICAgICAgICAgIHsvKiBDb252ZXJzaW9uIFByb2dyZXNzICovfVxyXG4gICAgICAgICAgPEFuaW1hdGVQcmVzZW5jZT5cclxuICAgICAgICAgICAge2ZpbGVzLmxlbmd0aCA+IDAgJiYgZmlsZXMuc29tZShmID0+IGYuc3RhdHVzICE9PSAnaWRsZScpICYmIChcclxuICAgICAgICAgICAgICA8bW90aW9uLmRpdlxyXG4gICAgICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiAyMCB9fVxyXG4gICAgICAgICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCB5OiAwIH19XHJcbiAgICAgICAgICAgICAgICBleGl0PXt7IG9wYWNpdHk6IDAsIHk6IDIwIH19XHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgPENvbnZlcnNpb25Qcm9ncmVzc1xyXG4gICAgICAgICAgICAgICAgICBvbkRvd25sb2FkPXthc3luYyAoZmlsZSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgIHRyeSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICBhd2FpdCBkb3dubG9hZFNlcnZpY2UuZG93bmxvYWRGaWxlKGZpbGUpO1xyXG4gICAgICAgICAgICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICBsb2dnZXIuZXJyb3IoJ0Rvd25sb2FkJywgJ0ZpbGUgZG93bmxvYWQgZmFpbGVkJywgZXJyb3IsIHsgXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGZpbGVJZDogZmlsZS5pZCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgZmlsZU5hbWU6IGZpbGUubmFtZSBcclxuICAgICAgICAgICAgICAgICAgICAgIH0pO1xyXG4gICAgICAgICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcignRG93bmxvYWQgZmFpbGVkOicsIGVycm9yKTtcclxuICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgIG9uUHJldmlldz17KGZpbGUpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICBpZiAoZmlsZS5yZXN1bHQ/LmNvbnRlbnQpIHtcclxuICAgICAgICAgICAgICAgICAgICAgIHNldFNlbGVjdGVkRmlsZUNvbnRlbnQoZmlsZS5yZXN1bHQuY29udGVudCk7XHJcbiAgICAgICAgICAgICAgICAgICAgICBzZXRTZWxlY3RlZEZpbGVUeXBlKGZpbGUucmVzdWx0Lm1ldGFkYXRhPy5jb252ZXJ0ZWRGb3JtYXQgYXMgJ3J0ZicgfCAnbWQnIHwgJ21hcmtkb3duJyB8fCAnbWQnKTtcclxuICAgICAgICAgICAgICAgICAgICAgIHNldFNlbGVjdGVkRmlsZU5hbWUoZmlsZS5maWxlLm5hbWUucmVwbGFjZSgvXFwuKHJ0ZnxtZCkkL2ksIGAuJHtmaWxlLnJlc3VsdC5tZXRhZGF0YT8uY29udmVydGVkRm9ybWF0IHx8ICdtZCd9YCkpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgc2V0U2hvd1ByZXZpZXcodHJ1ZSk7XHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICBvblJldHJ5PXthc3luYyAoZmlsZSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgIC8vIFJlLXJ1biBjb252ZXJzaW9uIGZvciBmYWlsZWQgZmlsZVxyXG4gICAgICAgICAgICAgICAgICAgIHVwZGF0ZUZpbGVTdGF0dXMoZmlsZS5pZCwgJ2NvbnZlcnRpbmcnKTtcclxuICAgICAgICAgICAgICAgICAgICB1cGRhdGVGaWxlUHJvZ3Jlc3MoZmlsZS5pZCwgMCk7XHJcbiAgICAgICAgICAgICAgICAgICAgXHJcbiAgICAgICAgICAgICAgICAgICAgdHJ5IHtcclxuICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHJlc3VsdCA9IGZpbGUuZmlsZS50eXBlID09PSAncnRmJyBcclxuICAgICAgICAgICAgICAgICAgICAgICAgPyBhd2FpdCB0YXVyaUFwaS5jb252ZXJ0UnRmVG9NYXJrZG93bihmaWxlLmZpbGUucGF0aClcclxuICAgICAgICAgICAgICAgICAgICAgICAgOiBhd2FpdCB0YXVyaUFwaS5jb252ZXJ0TWFya2Rvd25Ub1J0ZihmaWxlLmZpbGUucGF0aCk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIFxyXG4gICAgICAgICAgICAgICAgICAgICAgdXBkYXRlRmlsZVN0YXR1cyhmaWxlLmlkLCByZXN1bHQuc3VjY2VzcyA/ICdjb21wbGV0ZWQnIDogJ2Vycm9yJywgcmVzdWx0KTtcclxuICAgICAgICAgICAgICAgICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgICAgICAgICAgICAgICAgbG9nZ2VyLmVycm9yKCdDb252ZXJzaW9uJywgJ1JldHJ5IGNvbnZlcnNpb24gZmFpbGVkJywgZXJyb3IsIHsgXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGZpbGVJZDogZmlsZS5pZCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgZmlsZU5hbWU6IGZpbGUuZmlsZS5uYW1lLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBmaWxlVHlwZTogZmlsZS5maWxlLnR5cGUgXHJcbiAgICAgICAgICAgICAgICAgICAgICB9KTtcclxuICAgICAgICAgICAgICAgICAgICAgIHVwZGF0ZUZpbGVTdGF0dXMoZmlsZS5pZCwgJ2Vycm9yJyk7XHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICA8L21vdGlvbi5kaXY+XHJcbiAgICAgICAgICAgICl9XHJcbiAgICAgICAgICA8L0FuaW1hdGVQcmVzZW5jZT5cclxuXHJcbiAgICAgICAgICB7LyogUHJldmlldyBUb2dnbGUgKi99XHJcbiAgICAgICAgICA8QW5pbWF0ZVByZXNlbmNlPlxyXG4gICAgICAgICAgICB7ZmlsZXMubGVuZ3RoID4gMCAmJiAoXHJcbiAgICAgICAgICAgICAgPG1vdGlvbi5kaXZcclxuICAgICAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeTogMTAgfX1cclxuICAgICAgICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSwgeTogMCB9fVxyXG4gICAgICAgICAgICAgICAgZXhpdD17eyBvcGFjaXR5OiAwLCB5OiAxMCB9fVxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHAtNCBiZy1zZWNvbmRhcnkvMzAgcm91bmRlZC1sZ1wiXHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxyXG4gICAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cInByZXZpZXctdG9nZ2xlXCIgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bVwiPlxyXG4gICAgICAgICAgICAgICAgICAgIFJlYWwtdGltZSBQcmV2aWV3XHJcbiAgICAgICAgICAgICAgICAgIDwvTGFiZWw+XHJcbiAgICAgICAgICAgICAgICAgIDxTd2l0Y2hcclxuICAgICAgICAgICAgICAgICAgICBpZD1cInByZXZpZXctdG9nZ2xlXCJcclxuICAgICAgICAgICAgICAgICAgICBjaGVja2VkPXtzaG93UHJldmlld31cclxuICAgICAgICAgICAgICAgICAgICBvbkNoZWNrZWRDaGFuZ2U9e3NldFNob3dQcmV2aWV3fVxyXG4gICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0zXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+XHJcbiAgICAgICAgICAgICAgICAgICAge3Nob3dQcmV2aWV3ID8gJ1ByZXZpZXcgZW5hYmxlZCcgOiAnUHJldmlldyBkaXNhYmxlZCd9XHJcbiAgICAgICAgICAgICAgICAgIDwvcD5cclxuICAgICAgICAgICAgICAgICAge2NvbXBsZXRlZENvdW50ID4gMCAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcclxuICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcclxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dEb3dubG9hZE1hbmFnZXIodHJ1ZSl9XHJcbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJnYXAtMlwiXHJcbiAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPEFyY2hpdmUgY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICBEb3dubG9hZCBNYW5hZ2VyICh7Y29tcGxldGVkQ291bnR9KVxyXG4gICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxyXG4gICAgICAgICAgICApfVxyXG4gICAgICAgICAgPC9BbmltYXRlUHJlc2VuY2U+XHJcblxyXG4gICAgICAgICAgey8qIExpdmUgUHJldmlldyBQYW5lbCAqL31cclxuICAgICAgICAgIDxBbmltYXRlUHJlc2VuY2U+XHJcbiAgICAgICAgICAgIHtzaG93UHJldmlldyAmJiBzZWxlY3RlZEZpbGVDb250ZW50ICYmIChcclxuICAgICAgICAgICAgICA8bW90aW9uLmRpdlxyXG4gICAgICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCBoZWlnaHQ6IDAgfX1cclxuICAgICAgICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSwgaGVpZ2h0OiAnYXV0bycgfX1cclxuICAgICAgICAgICAgICAgIGV4aXQ9e3sgb3BhY2l0eTogMCwgaGVpZ2h0OiAwIH19XHJcbiAgICAgICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjMgfX1cclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm92ZXJmbG93LWhpZGRlblwiXHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgPFByZXZpZXdQYW5lbFxyXG4gICAgICAgICAgICAgICAgICBzb3VyY2VDb250ZW50PXtzZWxlY3RlZEZpbGVDb250ZW50fVxyXG4gICAgICAgICAgICAgICAgICBzb3VyY2VUeXBlPXtzZWxlY3RlZEZpbGVUeXBlfVxyXG4gICAgICAgICAgICAgICAgICBmaWxlTmFtZT17c2VsZWN0ZWRGaWxlTmFtZX1cclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaC1bNjAwcHhdXCJcclxuICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxyXG4gICAgICAgICAgICApfVxyXG4gICAgICAgICAgPC9BbmltYXRlUHJlc2VuY2U+XHJcblxyXG4gICAgICAgICAgey8qIFJlc3VsdHMgU2VjdGlvbiAqL31cclxuICAgICAgICAgIDxBbmltYXRlUHJlc2VuY2U+XHJcbiAgICAgICAgICAgIHtjb252ZXJzaW9uUmVzdWx0cy5sZW5ndGggPiAwICYmICFzaG93UHJldmlldyAmJiAoXHJcbiAgICAgICAgICAgICAgPG1vdGlvbi5kaXZcclxuICAgICAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeTogMjAgfX1cclxuICAgICAgICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSwgeTogMCB9fVxyXG4gICAgICAgICAgICAgICAgZXhpdD17eyBvcGFjaXR5OiAwLCB5OiAyMCB9fVxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIDxDYXJkPlxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNlwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgbWItNCBmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPENoZWNrQ2lyY2xlMiBjbGFzc05hbWU9XCJ3LTUgaC01IHRleHQtZ3JlZW4tNTAwXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgIENvbnZlcnNpb24gUmVzdWx0c1xyXG4gICAgICAgICAgICAgICAgICAgIDwvaDM+XHJcbiAgICAgICAgICAgICAgICAgICAgXHJcbiAgICAgICAgICAgICAgICAgICAgPFRhYnMgZGVmYXVsdFZhbHVlPVwicHJldmlld1wiIGNsYXNzTmFtZT1cInctZnVsbFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPFRhYnNMaXN0IGNsYXNzTmFtZT1cImdyaWQgdy1mdWxsIGdyaWQtY29scy0yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxUYWJzVHJpZ2dlciB2YWx1ZT1cInByZXZpZXdcIj5QcmV2aWV3PC9UYWJzVHJpZ2dlcj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPFRhYnNUcmlnZ2VyIHZhbHVlPVwiZG93bmxvYWRcIj5Eb3dubG9hZDwvVGFic1RyaWdnZXI+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L1RhYnNMaXN0PlxyXG4gICAgICAgICAgICAgICAgICAgICAgXHJcbiAgICAgICAgICAgICAgICAgICAgICA8VGFic0NvbnRlbnQgdmFsdWU9XCJwcmV2aWV3XCIgY2xhc3NOYW1lPVwibXQtNFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNCBtYXgtaC05NiBvdmVyZmxvdy15LWF1dG9cIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICB7Y29udmVyc2lvblJlc3VsdHMubWFwKChyZXN1bHQsIGluZGV4KSA9PiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8bW90aW9uLmRpdlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBrZXk9e2luZGV4fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHg6IC0yMCB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHg6IDAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkZWxheTogaW5kZXggKiAwLjEgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicC00IGJnLXNlY29uZGFyeS81MCByb3VuZGVkLWxnXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+e3Jlc3VsdC5maWxlLm5hbWV9PC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxCYWRnZSB2YXJpYW50PXtyZXN1bHQucmVzdWx0LnN1Y2Nlc3MgPyAnZGVmYXVsdCcgOiAnZGVzdHJ1Y3RpdmUnfT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtyZXN1bHQucmVzdWx0LnN1Y2Nlc3MgPyAnU3VjY2VzcycgOiAnRmFpbGVkJ31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0JhZGdlPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3Jlc3VsdC5yZXN1bHQuY29udGVudCAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHByZSBjbGFzc05hbWU9XCJ0ZXh0LXhzIGJnLWJhY2tncm91bmQgcC0zIHJvdW5kZWQgb3ZlcmZsb3cteC1hdXRvXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7cmVzdWx0LnJlc3VsdC5jb250ZW50LnNsaWNlKDAsIDIwMCl9Li4uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wcmU+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L21vdGlvbi5kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgKSl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9UYWJzQ29udGVudD5cclxuICAgICAgICAgICAgICAgICAgICAgIFxyXG4gICAgICAgICAgICAgICAgICAgICAgPFRhYnNDb250ZW50IHZhbHVlPVwiZG93bmxvYWRcIiBjbGFzc05hbWU9XCJtdC00XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0zXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAge2NvbnZlcnNpb25SZXN1bHRzXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAuZmlsdGVyKHIgPT4gci5yZXN1bHQuc3VjY2VzcylcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC5tYXAoKHJlc3VsdCwgaW5kZXgpID0+IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPG1vdGlvbi5kaXZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBrZXk9e2luZGV4fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeTogMTAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHk6IDAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGRlbGF5OiBpbmRleCAqIDAuMDUgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gcC0zIGJnLXNlY29uZGFyeS8zMCByb3VuZGVkLWxnXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW1cIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtyZXN1bHQuZmlsZS5uYW1lLnJlcGxhY2UoL1xcLihydGZ8bWQpJC8sICcnKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtyZXN1bHQucmVzdWx0Lm1ldGFkYXRhPy5jb252ZXJ0ZWRGb3JtYXQgPT09ICdtZCcgPyAnLm1kJyA6ICcucnRmJ31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvbiBcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiIFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cImdob3N0XCIgXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJnYXAtMlwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXthc3luYyAoKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHByb2Nlc3NlZEZpbGUgPSBmaWxlcy5maW5kKGYgPT4gZi5pZCA9PT0gcmVzdWx0LmZpbGUuaWQpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAocHJvY2Vzc2VkRmlsZSkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRyeSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhd2FpdCBkb3dubG9hZFNlcnZpY2UuZG93bmxvYWRGaWxlKHByb2Nlc3NlZEZpbGUpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsb2dnZXIuZXJyb3IoJ0Rvd25sb2FkJywgJ1Jlc3VsdCBkb3dubG9hZCBmYWlsZWQnLCBlcnJvciwgeyBcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZmlsZUlkOiByZXN1bHQuZmlsZS5pZCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZmlsZU5hbWU6IHJlc3VsdC5maWxlLm5hbWUgXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Rvd25sb2FkIGZhaWxlZDonLCBlcnJvcik7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxEb3dubG9hZCBjbGFzc05hbWU9XCJ3LTQgaC00XCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIERvd25sb2FkXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICkpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvVGFic0NvbnRlbnQ+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9UYWJzPlxyXG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIDwvQ2FyZD5cclxuICAgICAgICAgICAgICA8L21vdGlvbi5kaXY+XHJcbiAgICAgICAgICAgICl9XHJcbiAgICAgICAgICA8L0FuaW1hdGVQcmVzZW5jZT5cclxuXHJcbiAgICAgICAgICB7LyogRG93bmxvYWQgTWFuYWdlciBNb2RhbCAqL31cclxuICAgICAgICAgIDxBbmltYXRlUHJlc2VuY2U+XHJcbiAgICAgICAgICAgIHtzaG93RG93bmxvYWRNYW5hZ2VyICYmIChcclxuICAgICAgICAgICAgICA8bW90aW9uLmRpdlxyXG4gICAgICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwIH19XHJcbiAgICAgICAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEgfX1cclxuICAgICAgICAgICAgICAgIGV4aXQ9e3sgb3BhY2l0eTogMCB9fVxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZml4ZWQgaW5zZXQtMCBiZy1ibGFjay81MCB6LTUwIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHAtNFwiXHJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93RG93bmxvYWRNYW5hZ2VyKGZhbHNlKX1cclxuICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICA8bW90aW9uLmRpdlxyXG4gICAgICAgICAgICAgICAgICBpbml0aWFsPXt7IHNjYWxlOiAwLjksIG9wYWNpdHk6IDAgfX1cclxuICAgICAgICAgICAgICAgICAgYW5pbWF0ZT17eyBzY2FsZTogMSwgb3BhY2l0eTogMSB9fVxyXG4gICAgICAgICAgICAgICAgICBleGl0PXt7IHNjYWxlOiAwLjksIG9wYWNpdHk6IDAgfX1cclxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KGUpID0+IGUuc3RvcFByb3BhZ2F0aW9uKCl9XHJcbiAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgIDxEb3dubG9hZE1hbmFnZXIgXHJcbiAgICAgICAgICAgICAgICAgICAgZmlsZXM9e2ZpbGVzfVxyXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xvc2U9eygpID0+IHNldFNob3dEb3dubG9hZE1hbmFnZXIoZmFsc2UpfVxyXG4gICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibXQtNCB3LWZ1bGxcIlxyXG4gICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcclxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93RG93bmxvYWRNYW5hZ2VyKGZhbHNlKX1cclxuICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgIENsb3NlXHJcbiAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxyXG4gICAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cclxuICAgICAgICAgICAgKX1cclxuICAgICAgICAgIDwvQW5pbWF0ZVByZXNlbmNlPlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L2Rpdj5cclxuICAgIDwvZGl2PlxyXG4gICAgPC9Db252ZXJzaW9uRXJyb3JCb3VuZGFyeT5cclxuICApO1xyXG59XHJcbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUNhbGxiYWNrIiwibW90aW9uIiwiQW5pbWF0ZVByZXNlbmNlIiwiQXJyb3dSaWdodCIsIkZpbGVUZXh0IiwiRG93bmxvYWQiLCJTcGFya2xlcyIsIkNoZWNrQ2lyY2xlMiIsIkFyY2hpdmUiLCJEcmFnRHJvcFpvbmUiLCJDb252ZXJzaW9uUHJvZ3Jlc3MiLCJQcmV2aWV3UGFuZWwiLCJEb3dubG9hZE1hbmFnZXIiLCJCdXR0b24iLCJDYXJkIiwiQmFkZ2UiLCJUYWJzIiwiVGFic0NvbnRlbnQiLCJUYWJzTGlzdCIsIlRhYnNUcmlnZ2VyIiwiU3dpdGNoIiwiTGFiZWwiLCJ1c2VGaWxlU3RvcmUiLCJ0YXVyaUFwaSIsImRvd25sb2FkU2VydmljZSIsInVzZUVmZmVjdCIsIkNvbnZlcnNpb25FcnJvckJvdW5kYXJ5IiwibG9nZ2VyIiwiSG9tZSIsImZpbGVzIiwidXBkYXRlRmlsZVN0YXR1cyIsInVwZGF0ZUZpbGVQcm9ncmVzcyIsImNsZWFyRmlsZXMiLCJpc0NvbnZlcnRpbmciLCJzZXRJc0NvbnZlcnRpbmciLCJjb252ZXJzaW9uUmVzdWx0cyIsInNldENvbnZlcnNpb25SZXN1bHRzIiwic2hvd1ByZXZpZXciLCJzZXRTaG93UHJldmlldyIsInNlbGVjdGVkRmlsZUNvbnRlbnQiLCJzZXRTZWxlY3RlZEZpbGVDb250ZW50Iiwic2VsZWN0ZWRGaWxlVHlwZSIsInNldFNlbGVjdGVkRmlsZVR5cGUiLCJzZWxlY3RlZEZpbGVOYW1lIiwic2V0U2VsZWN0ZWRGaWxlTmFtZSIsInNob3dEb3dubG9hZE1hbmFnZXIiLCJzZXRTaG93RG93bmxvYWRNYW5hZ2VyIiwiaGFuZGxlQ29udmVydFRvTWFya2Rvd24iLCJydGZGaWxlcyIsImZpbHRlciIsImZpbGUiLCJ0eXBlIiwicmVzdWx0cyIsImlkIiwicHJvZ3Jlc3NJbnRlcnZhbCIsInNldEludGVydmFsIiwiTWF0aCIsIm1pbiIsInJhbmRvbSIsInJlc3VsdCIsImNvbnZlcnRSdGZUb01hcmtkb3duIiwicGF0aCIsImNsZWFySW50ZXJ2YWwiLCJzdWNjZXNzIiwicHVzaCIsImVycm9yIiwiZmlsZUlkIiwiZmlsZU5hbWUiLCJuYW1lIiwiaGFuZGxlQ29udmVydFRvUnRmIiwibWRGaWxlcyIsImNvbnZlcnRNYXJrZG93blRvUnRmIiwicnRmQ291bnQiLCJmIiwibGVuZ3RoIiwibWRDb3VudCIsImNvbXBsZXRlZENvdW50Iiwic3RhdHVzIiwibG9hZEZpbGVDb250ZW50IiwicmVzcG9uc2UiLCJyZWFkRmlsZUNvbnRlbnQiLCJjb250ZW50IiwiZmlsZVBhdGgiLCJjb25zb2xlIiwiZmlyc3RGaWxlIiwiZGl2IiwiY2xhc3NOYW1lIiwiaW5pdGlhbCIsIm9wYWNpdHkiLCJ5IiwiYW5pbWF0ZSIsInJvdGF0ZSIsInRyYW5zaXRpb24iLCJkdXJhdGlvbiIsInJlcGVhdCIsIkluZmluaXR5IiwiZWFzZSIsImgxIiwicCIsInNjYWxlIiwiZXhpdCIsInNwYW4iLCJ2YXJpYW50Iiwic2VsZWN0IiwidmFsdWUiLCJvbkNoYW5nZSIsImUiLCJmaW5kIiwidGFyZ2V0IiwibWFwIiwib3B0aW9uIiwib25DbGljayIsImRpc2FibGVkIiwic29tZSIsIm9uRG93bmxvYWQiLCJkb3dubG9hZEZpbGUiLCJvblByZXZpZXciLCJtZXRhZGF0YSIsImNvbnZlcnRlZEZvcm1hdCIsInJlcGxhY2UiLCJvblJldHJ5IiwiZmlsZVR5cGUiLCJodG1sRm9yIiwiY2hlY2tlZCIsIm9uQ2hlY2tlZENoYW5nZSIsInNpemUiLCJoZWlnaHQiLCJzb3VyY2VDb250ZW50Iiwic291cmNlVHlwZSIsImgzIiwiZGVmYXVsdFZhbHVlIiwiaW5kZXgiLCJ4IiwiZGVsYXkiLCJwcmUiLCJzbGljZSIsInIiLCJwcm9jZXNzZWRGaWxlIiwic3RvcFByb3BhZ2F0aW9uIiwib25DbG9zZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});