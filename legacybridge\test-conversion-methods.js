#!/usr/bin/env node

/**
 * Test script to verify the lightweight conversion methods work correctly
 * This tests the Pandoc-free implementation
 */

const { MCPConversionService } = require('./dist/mcp-server/services/mcp-conversion-service');
const { MCPCache } = require('./dist/mcp-server/services/mcp-cache');
const { MCPLogger } = require('./dist/mcp-server/utils/mcp-logger');

async function testConversionMethods() {
  console.log('🧪 Testing Lightweight Conversion Methods...\n');

  // Initialize services
  const logger = new MCPLogger({ level: 'info', enableConsole: true });
  const cache = new MCPCache({ enabled: false, type: 'memory', ttl: 3600, maxSize: 100 });
  const conversionService = new MCPConversionService(cache, logger);

  try {
    // Test 1: RTF to Markdown conversion
    console.log('📝 Test 1: RTF to Markdown Conversion');
    const rtfContent = '{\\rtf1\\ansi\\deff0 {\\fonttbl {\\f0 Times New Roman;}} \\f0\\fs24 {\\b Bold text} and {\\i italic text} with \\par paragraph breaks.}';
    
    const rtfResult = await conversionService.convertRtfToMarkdown(rtfContent);
    console.log('✅ RTF Input:', rtfContent.substring(0, 50) + '...');
    console.log('✅ Markdown Output:', rtfResult.content);
    console.log('✅ Metadata:', JSON.stringify(rtfResult.metadata, null, 2));
    console.log('');

    // Test 2: Markdown to RTF conversion
    console.log('📝 Test 2: Markdown to RTF Conversion');
    const markdownContent = '# Heading 1\n\nThis is **bold text** and *italic text*.\n\n## Heading 2\n\nAnother paragraph.';
    
    const mdResult = await conversionService.convertMarkdownToRtf(markdownContent);
    console.log('✅ Markdown Input:', markdownContent);
    console.log('✅ RTF Output:', mdResult.content);
    console.log('✅ Metadata:', JSON.stringify(mdResult.metadata, null, 2));
    console.log('');

    // Test 3: Document validation
    console.log('📝 Test 3: Document Validation');
    
    // Valid RTF
    const validRtfResult = await conversionService.validateDocument(rtfContent, 'rtf');
    console.log('✅ Valid RTF validation:', JSON.stringify(validRtfResult, null, 2));
    
    // Valid Markdown
    const validMdResult = await conversionService.validateDocument(markdownContent, 'markdown');
    console.log('✅ Valid Markdown validation:', JSON.stringify(validMdResult, null, 2));
    
    // Invalid format
    try {
      await conversionService.validateDocument('test content', 'unsupported');
      console.log('❌ Should have thrown error for unsupported format');
    } catch (error) {
      console.log('✅ Correctly rejected unsupported format:', error.message);
    }
    console.log('');

    // Test 4: Round-trip conversion
    console.log('📝 Test 4: Round-trip Conversion (Markdown → RTF → Markdown)');
    const originalMd = '**Bold** and *italic* text with paragraphs.';
    
    const toRtf = await conversionService.convertMarkdownToRtf(originalMd);
    console.log('✅ Original Markdown:', originalMd);
    console.log('✅ Converted to RTF:', toRtf.content);
    
    const backToMd = await conversionService.convertRtfToMarkdown(toRtf.content);
    console.log('✅ Back to Markdown:', backToMd.content);
    console.log('');

    console.log('🎉 All lightweight conversion tests completed successfully!');
    console.log('✅ Pandoc-free implementation is working correctly');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  }
}

// Run the tests
testConversionMethods().catch(console.error);
