// Mock MCPLogger for testing
export class MockMCPLogger {
  debug = jest.fn();
  info = jest.fn();
  warn = jest.fn();
  error = jest.fn();
  
  constructor() {
    this.debug.mockImplementation(() => {});
    this.info.mockImplementation(() => {});
    this.warn.mockImplementation(() => {});
    this.error.mockImplementation(() => {});
  }
  
  reset() {
    this.debug.mockClear();
    this.info.mockClear();
    this.warn.mockClear();
    this.error.mockClear();
  }
}