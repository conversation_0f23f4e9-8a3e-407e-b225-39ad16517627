#!/bin/bash
# Start MCP Server for Linux/macOS

# Set default environment variables
export NODE_ENV=${NODE_ENV:-development}
export MCP_PORT=${MCP_PORT:-3030}
export LOG_LEVEL=${LOG_LEVEL:-info}

# Check for required dependencies
echo "Checking dependencies..."

# Check for Node.js
if ! command -v node &> /dev/null; then
    echo "Node.js is not installed. Please install Node.js 16 or later."
    exit 1
fi

# Check for npm
if ! command -v npm &> /dev/null; then
    echo "npm is not installed. Please install npm."
    exit 1
fi

# Check for LibreOffice (optional, for legacy format support)
if command -v libreoffice &> /dev/null; then
    echo "LibreOffice is installed. Legacy format conversion will be available."
    export ENABLE_DOC=true
    export ENABLE_WORDPERFECT=true
else
    echo "LibreOffice is not installed. Legacy format conversion will be disabled."
    export ENABLE_DOC=false
    export ENABLE_WORDPERFECT=false
fi

# Check for Pandoc (optional, for legacy format support)
if ! command -v pandoc &> /dev/null && [[ "$ENABLE_DOC" == "true" || "$ENABLE_WORDPERFECT" == "true" ]]; then
    echo "Pandoc is not installed. Legacy format conversion will be disabled."
    export ENABLE_DOC=false
    export ENABLE_WORDPERFECT=false
fi

# Create required directories
mkdir -p logs uploads output temp

# Install dependencies if needed
if [ ! -d "node_modules" ]; then
    echo "Installing dependencies..."
    npm install
fi

# Build TypeScript if needed
if [ ! -d "dist" ]; then
    echo "Building TypeScript..."
    npm run build
fi

# Start the server
echo "Starting MCP Server on port $MCP_PORT..."
node dist/mcp-server/index.js