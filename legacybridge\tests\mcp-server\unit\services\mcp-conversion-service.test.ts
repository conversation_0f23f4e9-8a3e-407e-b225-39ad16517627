// Unit tests for MCPConversionService
import { MCPConversionService } from '../../../../src/mcp-server/services/mcp-conversion-service';
import { mockMCPConfig } from '../../mocks/config.mock';
import { MockMCPLogger } from '../../mocks/logger.mock';
import { MockMCPCache } from '../../mocks/cache.mock';
import fs from 'fs';
import path from 'path';

// Mock fs
jest.mock('fs', () => ({
  ...jest.requireActual('fs'),
  promises: {
    readFile: jest.fn(),
    writeFile: jest.fn(),
    unlink: jest.fn(),
    mkdir: jest.fn()
  },
  existsSync: jest.fn(),
  mkdirSync: jest.fn()
}));

// Mock path
jest.mock('path', () => ({
  ...jest.requireActual('path'),
  join: jest.fn().mockImplementation((...args) => args.join('/')),
  basename: jest.fn().mockImplementation((p, ext) => {
    const base = p.split('/').pop();
    if (ext && base.endsWith(ext)) {
      return base.slice(0, -ext.length);
    }
    return base;
  }),
  extname: jest.fn().mockImplementation(p => {
    const parts = p.split('.');
    return parts.length > 1 ? `.${parts.pop()}` : '';
  })
}));

describe('MCPConversionService', () => {
  let conversionService: MCPConversionService;
  let mockLogger: MockMCPLogger;
  let mockCache: MockMCPCache;
  
  beforeEach(() => {
    jest.clearAllMocks();
    
    mockLogger = new MockMCPLogger();
    mockCache = new MockMCPCache();
    
    // Mock fs.existsSync
    (fs.existsSync as jest.Mock).mockReturnValue(true);
    
    // Mock fs.promises.readFile
    (fs.promises.readFile as jest.Mock).mockImplementation((filePath, encoding) => {
      if (filePath.endsWith('.rtf')) {
        return Promise.resolve('{\\rtf1\\ansi Test RTF Content}');
      } else if (filePath.endsWith('.md')) {
        return Promise.resolve('# Test Markdown Content');
      } else {
        return Promise.resolve('Unknown content');
      }
    });
    
    conversionService = new MCPConversionService(mockMCPConfig, mockCache as any, mockLogger as any);
  });
  
  describe('convertRtfToMarkdown', () => {
    test('should convert RTF content to Markdown', async () => {
      // Mock the basic conversion method
      jest.spyOn(conversionService as any, 'convertRtfToMarkdownBasic').mockReturnValue('# Converted Markdown');

      const result = await conversionService.convertRtfToMarkdown('{\\rtf1\\ansi Test RTF Content}');

      expect((conversionService as any).convertRtfToMarkdownBasic).toHaveBeenCalledWith(
        '{\\rtf1\\ansi Test RTF Content}'
      );

      expect(result).toHaveProperty('content', '# Converted Markdown');
      expect(result).toHaveProperty('metadata');
      expect(result.metadata).toHaveProperty('convertedAt');
    });
    
    test('should use cache if available', async () => {
      // Mock cache hit with correct API
      mockCache.get.mockResolvedValue({
        content: '# Cached Markdown',
        metadata: {
          convertedAt: '2025-07-29T12:00:00.000Z'
        }
      });

      const result = await conversionService.convertRtfToMarkdown('{\\rtf1\\ansi Test RTF Content}');

      expect(mockCache.get).toHaveBeenCalledWith('rtf_to_md', '{\\rtf1\\ansi Test RTF Content}', undefined);

      expect(result).toHaveProperty('content', '# Cached Markdown');
      expect(result).toHaveProperty('metadata');
      expect(result.metadata).toHaveProperty('convertedAt', '2025-07-29T12:00:00.000Z');
    });
    
    test('should handle conversion errors', async () => {
      // Mock the basic conversion method to throw an error
      jest.spyOn(conversionService as any, 'convertRtfToMarkdownBasic').mockImplementation(() => {
        throw new Error('Conversion failed');
      });

      await expect(conversionService.convertRtfToMarkdown('{\\rtf1\\ansi Test RTF Content}')).rejects.toThrow(
        'Failed to convert RTF to Markdown'
      );

      expect(mockLogger.error).toHaveBeenCalled();
    });
  });
  
  describe('convertMarkdownToRtf', () => {
    test('should convert Markdown content to RTF', async () => {
      // Mock the basic conversion method
      jest.spyOn(conversionService as any, 'convertMarkdownToRtfBasic').mockReturnValue('{\\rtf1\\ansi Converted RTF}');

      const result = await conversionService.convertMarkdownToRtf('# Test Markdown Content');

      expect((conversionService as any).convertMarkdownToRtfBasic).toHaveBeenCalledWith(
        '# Test Markdown Content'
      );

      expect(result).toHaveProperty('content', '{\\rtf1\\ansi Converted RTF}');
      expect(result).toHaveProperty('metadata');
      expect(result.metadata).toHaveProperty('convertedAt');
    });
    
    test('should use cache if available', async () => {
      // Mock cache hit with correct API
      mockCache.get.mockResolvedValue({
        content: '{\\rtf1\\ansi Cached RTF}',
        metadata: {
          convertedAt: '2025-07-29T12:00:00.000Z',
          template: 'default'
        }
      });

      const result = await conversionService.convertMarkdownToRtf('# Test Markdown Content');

      expect(mockCache.get).toHaveBeenCalledWith('md_to_rtf', '# Test Markdown Content', undefined);

      expect(result).toHaveProperty('content', '{\\rtf1\\ansi Cached RTF}');
      expect(result).toHaveProperty('metadata');
      expect(result.metadata).toHaveProperty('convertedAt', '2025-07-29T12:00:00.000Z');
      expect(result.metadata).toHaveProperty('template', 'default');
    });
    
    test('should handle conversion errors', async () => {
      // Mock the basic conversion method to throw an error
      jest.spyOn(conversionService as any, 'convertMarkdownToRtfBasic').mockImplementation(() => {
        throw new Error('Conversion failed');
      });

      await expect(conversionService.convertMarkdownToRtf('# Test Markdown Content')).rejects.toThrow(
        'Failed to convert Markdown to RTF'
      );

      expect(mockLogger.error).toHaveBeenCalled();
    });
  });
  
  describe('convertRtfFileToMarkdown', () => {
    test('should convert RTF file to Markdown', async () => {
      // Mock the convertRtfToMarkdown method to return expected result
      jest.spyOn(conversionService, 'convertRtfToMarkdown').mockResolvedValue({
        content: '# Converted Markdown',
        metadata: {
          convertedAt: '2025-07-29T12:00:00.000Z'
        }
      });

      const result = await conversionService.convertRtfFileToMarkdown('/path/to/test.rtf');

      expect(fs.promises.readFile).toHaveBeenCalledWith('/path/to/test.rtf', 'utf8');
      expect(conversionService.convertRtfToMarkdown).toHaveBeenCalledWith(
        '{\\rtf1\\ansi Test RTF Content}',
        undefined
      );

      expect(result).toHaveProperty('content', '# Converted Markdown');
      expect(result).toHaveProperty('metadata');
      expect(result.metadata).toHaveProperty('convertedAt');
      expect(result).toHaveProperty('outputPath');
      expect(result).toHaveProperty('outputFileName', 'test.md');
    });
    
    test('should handle file read errors', async () => {
      // Mock file read error
      (fs.promises.readFile as jest.Mock).mockRejectedValue(new Error('File not found'));
      
      await expect(conversionService.convertRtfFileToMarkdown('/path/to/test.rtf')).rejects.toThrow(
        'Failed to read RTF file'
      );
      
      expect(mockLogger.error).toHaveBeenCalled();
    });
  });
  
  describe('convertMarkdownFileToRtf', () => {
    test('should convert Markdown file to RTF', async () => {
      // Mock the convertMarkdownToRtf method to return expected result
      jest.spyOn(conversionService, 'convertMarkdownToRtf').mockResolvedValue({
        content: '{\\rtf1\\ansi Converted RTF}',
        metadata: {
          convertedAt: '2025-07-29T12:00:00.000Z'
        }
      });

      const result = await conversionService.convertMarkdownFileToRtf('/path/to/test.md');

      expect(fs.promises.readFile).toHaveBeenCalledWith('/path/to/test.md', 'utf8');
      expect(conversionService.convertMarkdownToRtf).toHaveBeenCalledWith(
        '# Test Markdown Content',
        undefined
      );

      expect(result).toHaveProperty('content', '{\\rtf1\\ansi Converted RTF}');
      expect(result).toHaveProperty('metadata');
      expect(result.metadata).toHaveProperty('convertedAt');
      expect(result).toHaveProperty('outputPath');
      expect(result).toHaveProperty('outputFileName', 'test.rtf');
    });
    
    test('should handle file read errors', async () => {
      // Mock file read error
      (fs.promises.readFile as jest.Mock).mockRejectedValue(new Error('File not found'));
      
      await expect(conversionService.convertMarkdownFileToRtf('/path/to/test.md')).rejects.toThrow(
        'Failed to read Markdown file'
      );
      
      expect(mockLogger.error).toHaveBeenCalled();
    });
  });
  
  describe('validateDocument', () => {
    test('should validate RTF document', async () => {
      // Mock validation method
      jest.spyOn(conversionService as any, 'validateDocumentBasic').mockReturnValue({
        valid: true,
        errors: [],
        warnings: []
      });

      const result = await conversionService.validateDocument('{\\rtf1\\ansi Test RTF Content}', 'rtf');

      expect((conversionService as any).validateDocumentBasic).toHaveBeenCalledWith('{\\rtf1\\ansi Test RTF Content}', 'rtf');
      expect(result).toHaveProperty('valid', true);
      expect(result).toHaveProperty('errors');
      expect(result).toHaveProperty('warnings');
      expect(result.errors).toHaveLength(0);
    });
    
    test('should validate Markdown document', async () => {
      // Mock validation method
      jest.spyOn(conversionService as any, 'validateDocumentBasic').mockReturnValue({
        valid: true,
        errors: [],
        warnings: []
      });

      const result = await conversionService.validateDocument('# Test Markdown Content', 'markdown');

      expect((conversionService as any).validateDocumentBasic).toHaveBeenCalledWith('# Test Markdown Content', 'markdown');
      expect(result).toHaveProperty('valid', true);
      expect(result).toHaveProperty('errors');
      expect(result).toHaveProperty('warnings');
      expect(result.errors).toHaveLength(0);
    });
    
    test('should handle unsupported format', async () => {
      await expect(conversionService.validateDocument('Test content', 'unsupported')).rejects.toThrow(
        'Unsupported format: unsupported'
      );
    });
  });
});