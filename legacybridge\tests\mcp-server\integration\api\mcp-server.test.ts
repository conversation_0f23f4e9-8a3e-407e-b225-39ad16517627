// Integration tests for MCP Server API
import request from 'supertest';
import express from 'express';
import { MCPServer } from '../../../../src/mcp-server/core/mcp-server';
import { getConfig } from '../../../../src/mcp-server/utils/mcp-config';

describe('MCP Server API Integration', () => {
  let app: express.Express;
  let server: MCPServer;
  
  beforeAll(() => {
    // Set up test environment
    process.env.NODE_ENV = 'test';
    process.env.MCP_PORT = '3031';
    process.env.LOG_LEVEL = 'error';
    process.env.CACHE_ENABLED = 'false';
    process.env.API_KEYS = 'test-api-key';
    
    // Create server instance
    const config = getConfig();
    server = new MCPServer(config);
    
    // Get Express app instance
    app = (server as any).app;
  });
  
  afterAll(async () => {
    // Clean up
    await server.stop();
  });
  
  describe('Health Check Endpoint', () => {
    test('should return healthy status', async () => {
      const response = await request(app).get('/health');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('status', 'healthy');
      expect(response.body).toHaveProperty('version');
      expect(response.body).toHaveProperty('uptime');
    });
  });
  
  describe('Metrics Endpoint', () => {
    test('should return metrics data', async () => {
      const response = await request(app).get('/metrics');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('requests');
      expect(response.body).toHaveProperty('conversions');
      expect(response.body).toHaveProperty('errors');
    });
  });
  
  describe('MCP Tools Endpoint', () => {
    test('should return list of available tools', async () => {
      const response = await request(app)
        .get('/mcp/tools')
        .set('X-API-Key', 'test-api-key');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('tools');
      expect(response.body.tools).toBeInstanceOf(Array);
      expect(response.body.tools.length).toBeGreaterThan(0);
      
      // Verify tool structure
      const tool = response.body.tools[0];
      expect(tool).toHaveProperty('name');
      expect(tool).toHaveProperty('description');
      expect(tool).toHaveProperty('parameters');
    });
    
    test('should reject requests without API key', async () => {
      const response = await request(app).get('/mcp/tools');
      
      expect(response.status).toBe(401);
      expect(response.body).toHaveProperty('status', 'error');
      expect(response.body).toHaveProperty('message');
      expect(response.body.message).toContain('Unauthorized');
    });
  });
  
  describe('MCP Tool Execution Endpoint', () => {
    test('should validate tool name', async () => {
      const response = await request(app)
        .post('/mcp/tools/execute')
        .set('X-API-Key', 'test-api-key')
        .send({
          parameters: { content: 'test content' }
        });
      
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('status', 'error');
      expect(response.body).toHaveProperty('message');
      expect(response.body.message).toContain('Tool name is required');
    });
    
    test('should validate tool parameters', async () => {
      const response = await request(app)
        .post('/mcp/tools/execute')
        .set('X-API-Key', 'test-api-key')
        .send({
          tool: 'convert_markdown_to_rtf'
        });
      
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('status', 'error');
      expect(response.body).toHaveProperty('message');
      expect(response.body.message).toContain('Tool parameters are required');
    });
    
    test('should validate tool existence', async () => {
      const response = await request(app)
        .post('/mcp/tools/execute')
        .set('X-API-Key', 'test-api-key')
        .send({
          tool: 'non_existent_tool',
          parameters: { content: 'test content' }
        });
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('status', 'error');
      expect(response.body).toHaveProperty('message');
      expect(response.body.message).toContain('Tool not found');
    });
  });
  
  describe('Error Handling', () => {
    test('should handle 404 errors', async () => {
      const response = await request(app)
        .get('/non-existent-route')
        .set('X-API-Key', 'test-api-key');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('status', 'error');
      expect(response.body).toHaveProperty('message');
      expect(response.body.message).toContain('not found');
    });
    
    test('should handle rate limiting', async () => {
      // This test is a placeholder since we can't easily trigger rate limiting in tests
      // In a real scenario, we would need to make many requests in quick succession
      expect(true).toBe(true);
    });
  });
});