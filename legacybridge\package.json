{"name": "legacybridge", "version": "0.1.0", "private": true, "scripts": {"clean": "rm -rf node_modules dist target .next && npm cache clean --force", "install:all": "npm ci && cd src-tauri && cargo fetch", "build:frontend": "next build", "build:backend": "cd src-tauri && cargo build --release", "build:dll": "./build-unified.sh release all dll", "build": "./build-unified.sh release native all", "build:clean": "./build-unified.sh release native all clean", "dev": "concurrently \"npm run dev:frontend\" \"npm run dev:backend\"", "dev:frontend": "next dev", "dev:backend": "cd src-tauri && cargo tauri dev", "start": "next start", "lint": "next lint", "lint:all": "npm run lint && cd src-tauri && cargo clippy", "tauri": "tauri", "tauri:dev": "tauri dev", "tauri:build": "tauri build", "test": "jest --coverage --passWithNoTests", "test:watch": "jest --watch", "test:unit": "jest --testPathPattern=tests/unit", "test:integration": "jest --testPathPattern=tests/integration", "test:integration:api": "jest --testPathPattern=tests/integration/api", "test:e2e": "playwright test", "test:system": "jest --testPathPattern=tests/integration/system", "test:security": "jest --testPathPattern=tests/security", "test:performance": "cd src-tauri && cargo bench", "test:performance:regression": "node tests/performance/regression-check.js", "test:performance:memory": "node tests/performance/memory-check.js", "test:load": "k6 run tests/load/k6-load-test.js", "test:a11y": "playwright test tests/accessibility", "test:a11y:keyboard": "playwright test tests/accessibility/keyboard", "test:a11y:screen-reader": "playwright test tests/accessibility/screen-reader", "test:visual": "playwright test tests/visual-regression", "test:visual:components": "playwright test tests/visual-regression/components", "test:visual:responsive": "playwright test tests/visual-regression/responsive", "test:chaos": "playwright test tests/chaos", "test:chaos:recovery": "playwright test tests/chaos/recovery", "test:chaos:resilience": "playwright test tests/chaos/resilience", "test:all": "npm run test && cd src-tauri && cargo test", "check:dependencies": "npm audit --production && cd src-tauri && cargo audit", "validate:build": "npm list --depth=0 && cd src-tauri && cargo tree"}, "dependencies": {"@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/postcss": "^4.1.11", "@tauri-apps/api": "^2.7.0", "@types/cors": "^2.8.19", "@types/d3": "^7.4.3", "@types/express": "^5.0.3", "@types/file-saver": "^2.0.7", "@types/jsonwebtoken": "^9.0.10", "@types/multer": "^2.0.0", "@types/ws": "^8.18.1", "body-parser": "^2.2.0", "chart.js": "^4.5.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cors": "^2.8.5", "d3": "^7.9.0", "date-fns": "^4.1.0", "dompurify": "^3.2.6", "dotenv": "^17.2.1", "express": "^5.1.0", "express-rate-limit": "^8.0.1", "file-saver": "^2.0.5", "framer-motion": "^12.23.9", "helmet": "^8.1.0", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "jszip": "^3.10.1", "lru-cache": "^11.1.0", "lucide-react": "^0.525.0", "multer": "^2.0.2", "next": "15.4.3", "next-themes": "^0.4.6", "react": "^18.3.0", "react-chartjs-2": "^5.3.0", "react-dom": "^18.3.0", "recharts": "^3.1.0", "socket.io": "^4.8.1", "supertest": "^7.1.4", "tailwind-merge": "^3.3.1", "tailwind-variants": "^1.0.0", "uuid": "^11.1.0", "winston": "^3.17.0", "ws": "^8.18.3", "zustand": "^5.0.6"}, "devDependencies": {"@axe-core/playwright": "^4.10.0", "@eslint/eslintrc": "^3.0.0", "@jest/globals": "^29.7.0", "@playwright/test": "^1.49.0", "@tauri-apps/cli": "^2.0.0", "@testing-library/jest-dom": "^6.6.0", "@testing-library/react": "^15.0.0", "@testing-library/user-event": "^14.6.0", "@types/jest": "^29.5.14", "@types/jest-axe": "^3.5.9", "@types/node": "^20.19.9", "@types/pixelmatch": "^5.2.6", "@types/pngjs": "^6.0.5", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "axe-playwright": "^2.0.0", "axios": "^1.7.9", "concurrently": "^8.2.2", "eslint": "^8.57.0", "eslint-config-next": "15.4.3", "jest": "^29.7.0", "jest-axe": "^10.0.0", "jest-environment-jsdom": "^29.7.0", "pixelmatch": "^6.0.0", "playwright": "^1.49.0", "pngjs": "^7.0.0", "tailwindcss": "^3.4.0", "ts-jest": "^29.2.5", "typescript": "^5.6.0"}}