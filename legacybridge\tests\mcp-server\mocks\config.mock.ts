// Mock MCPConfig for testing
export const mockMCPConfig = {
  environment: 'test',
  version: '1.0.0-test',
  port: 3031,
  logLevel: 'error',
  corsOrigins: '*',
  rateLimit: 100,
  enableDashboard: false,
  enableMetrics: false,
  enableFileStorage: false,

  // Security configuration - moved from auth object to match actual interface
  apiKeys: ['test-api-key-1', 'test-api-key-2'],
  jwtSecret: 'test-jwt-secret',

  cache: {
    enabled: false,
    type: 'memory',
    ttl: 3600,
    maxSize: 100
  },

  fileStorage: {
    type: 'local',
    basePath: '/tmp/test-storage'
  },

  legacyFormats: {
    enableDOC: false,
    enableWordPerfect: false,
    enableOtherLegacyFormats: false
  },

  batchProcessing: {
    maxConcurrentJobs: 2,
    maxFilesPerBatch: 10,
    maxBatchSizeMB: 100
  }
};