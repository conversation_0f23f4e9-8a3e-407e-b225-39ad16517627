{"name": "legacybridge-mcp-server", "version": "1.0.0", "description": "Model Context Protocol server for LegacyBridge document conversion", "main": "dist/mcp-server/index.js", "scripts": {"build": "tsc", "start": "node dist/mcp-server/index.js", "dev": "ts-node-dev --respawn --transpile-only src/mcp-server/index.ts", "lint": "eslint src/mcp-server --ext .ts", "test": "jest --config jest.config.mcp.js", "test:unit": "jest --config jest.config.mcp.js --testPathPattern=tests/mcp-server/unit", "test:integration": "jest --config jest.config.mcp.js --testPathPattern=tests/mcp-server/integration", "test:coverage": "jest --config jest.config.mcp.js --coverage", "docker:build": "docker build -f Dockerfile.mcp -t legacybridge-mcp:latest .", "docker:run": "docker-compose -f docker-compose.mcp.yml up -d", "docker:stop": "docker-compose -f docker-compose.mcp.yml down"}, "keywords": ["legacybridge", "mcp", "document", "conversion", "rtf", "markdown", "doc", "wordperfect"], "author": "LegacyBridge Team", "license": "MIT", "dependencies": {"@tauri-apps/api": "^1.5.0", "axios": "^1.6.0", "body-parser": "^1.20.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.0", "file-saver": "^2.0.5", "helmet": "^7.0.0", "ioredis": "^5.3.2", "jszip": "^3.10.1", "jsonwebtoken": "^9.0.2", "lru-cache": "^10.0.1", "multer": "^1.4.5-lts.1", "socket.io": "^4.7.2", "uuid": "^9.0.1", "winston": "^3.11.0"}, "devDependencies": {"@types/cors": "^2.8.15", "@types/express": "^4.17.20", "@types/jest": "^29.5.6", "@types/jsonwebtoken": "^9.0.4", "@types/multer": "^1.4.9", "@types/node": "^20.8.9", "@types/uuid": "^9.0.6", "@typescript-eslint/eslint-plugin": "^6.9.0", "@typescript-eslint/parser": "^6.9.0", "eslint": "^8.52.0", "jest": "^29.7.0", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node-dev": "^2.0.0", "typescript": "^5.2.2"}, "engines": {"node": ">=16.0.0"}}