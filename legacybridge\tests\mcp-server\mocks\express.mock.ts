// Mock Express Request, Response, and NextFunction for testing
import { Request, Response, NextFunction } from 'express';

export const createMockRequest = (overrides = {}): Partial<Request> => ({
  headers: {},
  query: {},
  params: {},
  body: {},
  ...overrides
});

export const createMockResponse = (): Partial<Response> => {
  const res: Partial<Response> = {};
  
  res.status = jest.fn().mockReturnValue(res);
  res.json = jest.fn().mockReturnValue(res);
  res.send = jest.fn().mockReturnValue(res);
  res.end = jest.fn().mockReturnValue(res);
  res.setHeader = jest.fn().mockReturnValue(res);
  res.cookie = jest.fn().mockReturnValue(res);
  res.clearCookie = jest.fn().mockReturnValue(res);
  res.redirect = jest.fn().mockReturnValue(res);
  
  res.locals = {};
  
  // Add event emitter functionality
  const listeners: Record<string, Function[]> = {};
  
  res.on = jest.fn().mockImplementation((event, callback) => {
    if (!listeners[event]) {
      listeners[event] = [];
    }
    listeners[event].push(callback);
    return res;
  });
  
  res.emit = jest.fn().mockImplementation((event, ...args) => {
    if (listeners[event]) {
      listeners[event].forEach(callback => callback(...args));
    }
    return true;
  });
  
  return res;
};

export const createMockNextFunction = (): NextFunction => {
  return jest.fn();
};