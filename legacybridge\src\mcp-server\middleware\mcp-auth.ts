// MCP Server Authentication Middleware
// Handles API key and JWT authentication for the MCP server

import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { MCPConfig } from '../utils/mcp-config';

// Extended Request interface with user property
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        name: string;
        role: string;
        permissions: string[];
      };
    }
  }
}

// Authentication error
class AuthenticationError extends Error {
  statusCode: number;
  
  constructor(message: string, statusCode: number = 401) {
    super(message);
    this.name = 'AuthenticationError';
    this.statusCode = statusCode;
  }
}

// API key authentication
const authenticateApiKey = (apiKey: string, config: MCPConfig): boolean => {
  return config.apiKeys.includes(apiKey);
};

// JWT authentication
const authenticateJwt = (token: string, config: MCPConfig): any => {
  if (!config.jwtSecret) {
    throw new AuthenticationError('JWT authentication is not configured');
  }
  
  try {
    return jwt.verify(token, config.jwtSecret);
  } catch (error) {
    throw new AuthenticationError('Invalid or expired token');
  }
};

// Authentication middleware
export const MCPAuthMiddleware = (config: MCPConfig) => {
  return (req: Request, res: Response, next: NextFunction) => {
    // Skip authentication for health check and metrics endpoints
    if (req.path === '/health' || req.path === '/metrics') {
      return next();
    }

    // Skip authentication if no API keys or JWT secret configured
    if (config.apiKeys.length === 0 && !config.jwtSecret) {
      req.user = {
        id: 'anonymous',
        name: 'Anonymous',
        role: 'anonymous',
        permissions: ['convert', 'validate'],
      };
      return next();
    }

    // Get authorization header
    const authHeader = req.headers.authorization;
    
    // Check for API key in header or query parameter
    const apiKey = req.headers['x-api-key'] as string || req.query.api_key as string;
    
    if (apiKey) {
      // Authenticate with API key
      if (authenticateApiKey(apiKey, config)) {
        // Set user with basic permissions
        req.user = {
          id: 'api',
          name: 'API Client',
          role: 'api',
          permissions: ['convert', 'batch', 'validate'],
        };
        return next();
      }
      
      return res.status(401).json({
        status: 'error',
        message: 'Invalid API key',
      });
    }
    
    if (authHeader) {
      // Check for Bearer token
      const parts = authHeader.split(' ');
      
      if (parts.length === 2 && parts[0] === 'Bearer') {
        const token = parts[1];
        
        try {
          // Authenticate with JWT
          const decoded = authenticateJwt(token, config);
          
          // Set user from JWT payload
          req.user = {
            id: decoded.sub || decoded.id,
            name: decoded.name,
            role: decoded.role,
            permissions: decoded.permissions || [],
          };
          
          return next();
        } catch (error) {
          if (error instanceof AuthenticationError) {
            return res.status(error.statusCode).json({
              status: 'error',
              message: error.message,
            });
          }
          
          return res.status(401).json({
            status: 'error',
            message: 'Authentication failed',
          });
        }
      }
    }
    
    // No authentication provided
    if (config.environment === 'production' || config.environment === 'test') {
      // Require authentication in production and test environments
      return res.status(401).json({
        status: 'error',
        message: 'Unauthorized',
      });
    } else {
      // Allow anonymous access in development
      req.user = {
        id: 'anonymous',
        name: 'Anonymous',
        role: 'anonymous',
        permissions: ['convert', 'validate'],
      };
      return next();
    }
  };
};