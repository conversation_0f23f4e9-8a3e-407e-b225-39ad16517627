[package]
name = "legacybridge"
version = "1.0.0"
edition = "2021"
authors = ["LegacyBridge Team"]
description = "High-performance RTF ↔ Markdown converter DLL"

[lib]
name = "legacybridge"
crate-type = ["cdylib", "rlib"]

[dependencies]
lazy_static = "1.4"
regex = "1.10"
pulldown-cmark = "0.9"
thiserror = "1.0"
chrono = "0.4"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
prometheus = "0.13"
sysinfo = "0.31"
once_cell = "1.20"
tokio = { version = "1.42", features = ["full"] }

[profile.release]
lto = true
codegen-units = 1
opt-level = 3
strip = true

[target.'cfg(windows)'.build]
rustflags = ["-C", "target-feature=+crt-static"]

# 32-bit Windows specific optimizations
[target.i686-pc-windows-msvc]
rustflags = [
  "-C", "target-feature=+crt-static",
  "-C", "target-cpu=pentium4",  # Maximum compatibility
  "-C", "opt-level=s"           # Size optimization for legacy systems
]

# 32-bit Linux specific optimizations
[target.i686-unknown-linux-gnu]
rustflags = [
  "-C", "target-feature=+crt-static",
  "-C", "target-cpu=pentium4"
]