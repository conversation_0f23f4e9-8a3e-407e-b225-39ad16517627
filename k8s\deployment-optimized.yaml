apiVersion: v1
kind: Namespace
metadata:
  name: legacybridge-prod
  labels:
    app: legacybridge
    monitoring: "true"
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: legacybridge
  namespace: legacybridge-prod
  labels:
    app: legacybridge
    version: v1
    component: backend
spec:
  replicas: 3
  revisionHistoryLimit: 10
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 2
      maxUnavailable: 0
  selector:
    matchLabels:
      app: legacybridge
      component: backend
  template:
    metadata:
      labels:
        app: legacybridge
        version: v1
        component: backend
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9090"
        prometheus.io/path: "/metrics"
        # Force pod restart on config change
        checksum/config: "{{ include (print $.Template.BasePath \"/configmap.yaml\") . | sha256sum }}"
    spec:
      serviceAccountName: legacybridge
      securityContext:
        runAsNonRoot: true
        runAsUser: 1001
        fsGroup: 1001
        seccompProfile:
          type: RuntimeDefault
      topologySpreadConstraints:
        - maxSkew: 1
          topologyKey: kubernetes.io/hostname
          whenUnsatisfiable: DoNotSchedule
          labelSelector:
            matchLabels:
              app: legacybridge
      initContainers:
        - name: migration
          image: ghcr.io/legacybridge/legacybridge:latest
          imagePullPolicy: Always
          command: ["/bin/sh", "-c"]
          args:
            - |
              echo "Running database migrations..."
              # Add migration commands here
              echo "Migrations complete"
          securityContext:
            allowPrivilegeEscalation: false
            readOnlyRootFilesystem: true
            capabilities:
              drop:
                - ALL
      containers:
        - name: legacybridge
          image: ghcr.io/legacybridge/legacybridge:latest
          imagePullPolicy: Always
          ports:
            - containerPort: 3000
              name: http
              protocol: TCP
            - containerPort: 9090
              name: metrics
              protocol: TCP
          env:
            - name: NODE_ENV
              value: "production"
            - name: PORT
              value: "3000"
            - name: LOG_LEVEL
              value: "info"
            - name: METRICS_PORT
              value: "9090"
            - name: NODE_OPTIONS
              value: "--max-old-space-size=1536"
            - name: DATABASE_URL
              valueFrom:
                secretKeyRef:
                  name: legacybridge-secrets
                  key: database-url
            - name: REDIS_URL
              valueFrom:
                secretKeyRef:
                  name: legacybridge-secrets
                  key: redis-url
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: POD_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
            - name: POD_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.podIP
          resources:
            requests:
              memory: "512Mi"
              cpu: "500m"
              ephemeral-storage: "1Gi"
            limits:
              memory: "2Gi"
              cpu: "2000m"
              ephemeral-storage: "2Gi"
          livenessProbe:
            httpGet:
              path: /api/health
              port: http
              httpHeaders:
                - name: User-Agent
                  value: "Kubernetes-Health-Check"
            initialDelaySeconds: 45
            periodSeconds: 20
            timeoutSeconds: 5
            successThreshold: 1
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /api/ready
              port: http
              httpHeaders:
                - name: User-Agent
                  value: "Kubernetes-Ready-Check"
            initialDelaySeconds: 10
            periodSeconds: 10
            timeoutSeconds: 3
            successThreshold: 1
            failureThreshold: 3
          startupProbe:
            httpGet:
              path: /api/startup
              port: http
            initialDelaySeconds: 0
            periodSeconds: 10
            timeoutSeconds: 3
            successThreshold: 1
            failureThreshold: 30
          lifecycle:
            preStop:
              exec:
                command: ["/bin/sh", "-c", "sleep 15"]
          securityContext:
            allowPrivilegeEscalation: false
            readOnlyRootFilesystem: true
            runAsNonRoot: true
            runAsUser: 1001
            capabilities:
              drop:
                - ALL
              add:
                - NET_BIND_SERVICE
          volumeMounts:
            - name: tmp
              mountPath: /tmp
            - name: cache
              mountPath: /app/.next/cache
            - name: logs
              mountPath: /app/logs
            - name: config
              mountPath: /app/config
              readOnly: true
        # Sidecar for log shipping
        - name: fluentbit
          image: fluent/fluent-bit:2.1.10
          resources:
            requests:
              memory: "64Mi"
              cpu: "100m"
            limits:
              memory: "128Mi"
              cpu: "200m"
          volumeMounts:
            - name: logs
              mountPath: /app/logs
              readOnly: true
            - name: fluentbit-config
              mountPath: /fluent-bit/etc
              readOnly: true
      volumes:
        - name: tmp
          emptyDir:
            sizeLimit: 1Gi
        - name: cache
          emptyDir:
            sizeLimit: 2Gi
        - name: logs
          emptyDir:
            sizeLimit: 500Mi
        - name: config
          configMap:
            name: legacybridge-config
        - name: fluentbit-config
          configMap:
            name: fluentbit-config
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            - labelSelector:
                matchExpressions:
                  - key: app
                    operator: In
                    values:
                      - legacybridge
              topologyKey: kubernetes.io/hostname
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 100
              preference:
                matchExpressions:
                  - key: node-type
                    operator: In
                    values:
                      - compute-optimized
---
apiVersion: v1
kind: Service
metadata:
  name: legacybridge
  namespace: legacybridge-prod
  labels:
    app: legacybridge
    component: backend
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-type: "nlb"
    service.beta.kubernetes.io/aws-load-balancer-cross-zone-load-balancing-enabled: "true"
spec:
  type: ClusterIP
  sessionAffinity: ClientIP
  sessionAffinityConfig:
    clientIP:
      timeoutSeconds: 10800
  ports:
    - port: 80
      targetPort: 3000
      protocol: TCP
      name: http
    - port: 9090
      targetPort: 9090
      protocol: TCP
      name: metrics
  selector:
    app: legacybridge
    component: backend
---
apiVersion: v1
kind: Service
metadata:
  name: legacybridge-headless
  namespace: legacybridge-prod
  labels:
    app: legacybridge
spec:
  type: ClusterIP
  clusterIP: None
  ports:
    - port: 3000
      targetPort: 3000
      protocol: TCP
      name: http
  selector:
    app: legacybridge
    component: backend
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: legacybridge
  namespace: legacybridge-prod
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: legacybridge
      component: backend
  unhealthyPodEvictionPolicy: AlwaysAllow
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: legacybridge
  namespace: legacybridge-prod
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: legacybridge
  minReplicas: 2
  maxReplicas: 20
  metrics:
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 70
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: 80
    - type: Pods
      pods:
        metric:
          name: http_requests_per_second
        target:
          type: AverageValue
          averageValue: "1000"
    - type: Object
      object:
        metric:
          name: queue_depth
        describedObject:
          apiVersion: v1
          kind: Service
          name: legacybridge
        target:
          type: Value
          value: "30"
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
        - type: Percent
          value: 10
          periodSeconds: 60
        - type: Pods
          value: 1
          periodSeconds: 120
      selectPolicy: Min
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
        - type: Percent
          value: 100
          periodSeconds: 30
        - type: Pods
          value: 4
          periodSeconds: 60
      selectPolicy: Max
---
apiVersion: autoscaling/v2
kind: VerticalPodAutoscaler
metadata:
  name: legacybridge
  namespace: legacybridge-prod
spec:
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: legacybridge
  updatePolicy:
    updateMode: "Auto"
  resourcePolicy:
    containerPolicies:
      - containerName: legacybridge
        minAllowed:
          cpu: 250m
          memory: 256Mi
        maxAllowed:
          cpu: 4000m
          memory: 4Gi
        controlledResources: ["cpu", "memory"]
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: legacybridge
  namespace: legacybridge-prod
  labels:
    app: legacybridge
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: legacybridge
  namespace: legacybridge-prod
rules:
  - apiGroups: [""]
    resources: ["configmaps"]
    verbs: ["get", "list", "watch"]
  - apiGroups: [""]
    resources: ["secrets"]
    verbs: ["get"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: legacybridge
  namespace: legacybridge-prod
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: legacybridge
subjects:
  - kind: ServiceAccount
    name: legacybridge
    namespace: legacybridge-prod