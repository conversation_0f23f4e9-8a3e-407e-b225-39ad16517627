// MCP Client for interacting with the LegacyBridge MCP server

const axios = require('axios');

class MCPClient {
  constructor(baseUrl, apiKey) {
    this.baseUrl = baseUrl || 'http://localhost:3030';
    this.apiKey = apiKey;
    
    // Create axios instance with default config
    this.client = axios.create({
      baseURL: this.baseUrl,
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': this.apiKey
      }
    });
  }
  
  // Execute a function on the MCP server
  async executeFunction(tool, parameters) {
    try {
      const response = await this.client.post('/mcp/tools/execute', {
        tool,
        parameters
      });
      
      if (response.data.status === 'error') {
        throw new Error(response.data.message || 'Unknown error');
      }
      
      return response.data.result;
    } catch (error) {
      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        throw new Error(`MCP server error: ${error.response.data.message || error.response.status}`);
      } else if (error.request) {
        // The request was made but no response was received
        throw new Error('No response from MCP server');
      } else {
        // Something happened in setting up the request that triggered an Error
        throw new Error(`Error: ${error.message}`);
      }
    }
  }
  
  // Upload a file to the MCP server
  async uploadFile(file, conversionType, options = {}) {
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('conversionType', conversionType);
      formData.append('options', JSON.stringify(options));
      
      const response = await this.client.post('/mcp/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      
      if (response.data.status === 'error') {
        throw new Error(response.data.message || 'Unknown error');
      }
      
      return response.data.result;
    } catch (error) {
      if (error.response) {
        throw new Error(`MCP server error: ${error.response.data.message || error.response.status}`);
      } else if (error.request) {
        throw new Error('No response from MCP server');
      } else {
        throw new Error(`Error: ${error.message}`);
      }
    }
  }
  
  // Submit a batch job
  async submitBatch(files, batchOptions = {}) {
    try {
      const response = await this.client.post('/mcp/tools/execute', {
        tool: 'batch_convert',
        parameters: {
          files,
          batchOptions
        }
      });
      
      if (response.data.status === 'error') {
        throw new Error(response.data.message || 'Unknown error');
      }
      
      return response.data.result;
    } catch (error) {
      if (error.response) {
        throw new Error(`MCP server error: ${error.response.data.message || error.response.status}`);
      } else if (error.request) {
        throw new Error('No response from MCP server');
      } else {
        throw new Error(`Error: ${error.message}`);
      }
    }
  }
  
  // Get batch status
  async getBatchStatus(batchId) {
    try {
      const response = await this.client.get(`/mcp/batch/${batchId}`);
      
      if (response.data.status === 'error') {
        throw new Error(response.data.message || 'Unknown error');
      }
      
      return response.data.batchStatus;
    } catch (error) {
      if (error.response) {
        throw new Error(`MCP server error: ${error.response.data.message || error.response.status}`);
      } else if (error.request) {
        throw new Error('No response from MCP server');
      } else {
        throw new Error(`Error: ${error.message}`);
      }
    }
  }
  
  // Cancel a batch job
  async cancelBatch(batchId) {
    try {
      const response = await this.client.post(`/mcp/batch/${batchId}/cancel`);
      
      if (response.data.status === 'error') {
        throw new Error(response.data.message || 'Unknown error');
      }
      
      return response.data;
    } catch (error) {
      if (error.response) {
        throw new Error(`MCP server error: ${error.response.data.message || error.response.status}`);
      } else if (error.request) {
        throw new Error('No response from MCP server');
      } else {
        throw new Error(`Error: ${error.message}`);
      }
    }
  }
  
  // Get available tools
  async getTools() {
    try {
      const response = await this.client.get('/mcp/tools');
      
      if (response.data.status === 'error') {
        throw new Error(response.data.message || 'Unknown error');
      }
      
      return response.data.tools;
    } catch (error) {
      if (error.response) {
        throw new Error(`MCP server error: ${error.response.data.message || error.response.status}`);
      } else if (error.request) {
        throw new Error('No response from MCP server');
      } else {
        throw new Error(`Error: ${error.message}`);
      }
    }
  }
}

module.exports = { MCPClient };