# LegacyBridge MCP Server Integration 

## DO NOT USE PANDOC- ITS TOO HEAVY. WE WANT TO KEEP THIS APP LIGH

This guide provides comprehensive instructions for setting up, configuring, and using the LegacyBridge MCP (Model Context Protocol) server for AI assistant integration.

## Table of Contents

1. [Introduction](#introduction)
2. [Features](#features)
3. [Installation](#installation)
   - [Prerequisites](#prerequisites)
   - [Docker Installation](#docker-installation)
   - [Manual Installation](#manual-installation)
4. [Configuration](#configuration)
   - [Environment Variables](#environment-variables)
   - [Authentication](#authentication)
   - [Legacy Format Support](#legacy-format-support)
5. [API Reference](#api-reference)
   - [MCP Protocol Endpoints](#mcp-protocol-endpoints)
   - [File Upload API](#file-upload-api)
   - [Batch Processing](#batch-processing)
6. [Integration Examples](#integration-examples)
   - [AI Assistant Integration](#ai-assistant-integration)
   - [Web Application Integration](#web-application-integration)
   - [Legacy System Integration](#legacy-system-integration)
7. [Performance Optimization](#performance-optimization)
8. [Monitoring and Logging](#monitoring-and-logging)
9. [Troubleshooting](#troubleshooting)
10. [Security Considerations](#security-considerations)

## Introduction

The LegacyBridge MCP Server provides a standardized API for AI assistants and other applications to convert between legacy document formats (RTF, DOC, WordPerfect) and modern formats like Markdown. It implements the Model Context Protocol (MCP) for seamless integration with AI platforms.

## Features

- **MCP Protocol Support**: Implements the Model Context Protocol for AI assistant integration
- **Multiple Format Support**: Converts between RTF, Markdown, DOC, and WordPerfect formats
- **Batch Processing**: Efficiently process multiple documents in a single request
- **Progress Tracking**: Real-time progress updates via WebSockets
- **Caching**: Intelligent caching for improved performance
- **Authentication**: Secure API key and JWT authentication
- **Scalability**: Docker support for easy deployment and scaling
- **Monitoring**: Comprehensive metrics and logging

## Installation

### Prerequisites

- Node.js 16 or later
- npm or yarn
- Docker and Docker Compose (for containerized deployment)
- LibreOffice and Pandoc (for legacy format support)

### Docker Installation

The easiest way to deploy the MCP server is using Docker Compose:

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/legacy-bridge.git
   cd legacy-bridge
   ```

2. Create a `.env` file with your configuration:
   ```
   NODE_ENV=production
   MCP_PORT=3030
   LOG_LEVEL=info
   API_KEYS=your-api-key-1,your-api-key-2
   ENABLE_DOC=true
   ENABLE_WORDPERFECT=true
   ```

3. Start the server using Docker Compose:
   ```bash
   docker-compose -f docker-compose.mcp.yml up -d
   ```

4. Verify the server is running:
   ```bash
   curl http://localhost:3030/health
   ```

### Manual Installation

For manual installation:

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/legacy-bridge.git
   cd legacy-bridge
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Build the TypeScript code:
   ```bash
   npm run build
   ```

4. Start the server:
   ```bash
   # Linux/macOS
   ./scripts/start-mcp-server.sh
   
   # Windows
   scripts\start-mcp-server.bat
   ```

## Configuration

### Environment Variables

The MCP server can be configured using the following environment variables:

| Variable | Description | Default |
|----------|-------------|---------|
| `NODE_ENV` | Environment (development, production) | development |
| `MCP_PORT` | Port to run the server on | 3030 |
| `LOG_LEVEL` | Logging level (debug, info, warn, error) | info |
| `API_KEYS` | Comma-separated list of API keys | |
| `JWT_SECRET` | Secret for JWT authentication | |
| `CACHE_ENABLED` | Enable result caching | true |
| `CACHE_TYPE` | Cache type (memory, redis) | memory |
| `REDIS_URL` | Redis connection URL (if using Redis cache) | |
| `ENABLE_DOC` | Enable DOC format support | false |
| `ENABLE_WORDPERFECT` | Enable WordPerfect format support | false |

### Authentication

The MCP server supports two authentication methods:

1. **API Key Authentication**:
   - Include the API key in the `X-API-Key` header or as a query parameter `api_key`
   - Example: `curl -H "X-API-Key: your-api-key" http://localhost:3030/mcp/tools`

2. **JWT Authentication**:
   - Include a JWT token in the `Authorization` header as a Bearer token
   - Example: `curl -H "Authorization: Bearer your-jwt-token" http://localhost:3030/mcp/tools`

### Legacy Format Support

To enable support for legacy formats like DOC and WordPerfect, you need to install LibreOffice and Pandoc:

**Ubuntu/Debian**:
```bash
apt-get update && apt-get install -y libreoffice pandoc
```

**macOS**:
```bash
brew install libreoffice pandoc
```

**Windows**:
1. Download and install LibreOffice from https://www.libreoffice.org/
2. Download and install Pandoc from https://pandoc.org/installing.html

Then enable the formats in your configuration:
```
ENABLE_DOC=true
ENABLE_WORDPERFECT=true
```

## API Reference

### MCP Protocol Endpoints

#### List Available Tools

```
GET /mcp/tools
```

Returns a list of available conversion tools and their parameters.

#### Execute a Tool

```
POST /mcp/tools/execute
```

Request body:
```json
{
  "tool": "convert_rtf_to_markdown",
  "parameters": {
    "content": "your RTF content here",
    "options": {
      "preserveFormatting": true
    }
  }
}
```

### File Upload API

#### Upload and Convert a File

```
POST /mcp/upload
```

Form data:
- `file`: The file to convert
- `conversionType`: Type of conversion (rtf_to_md, md_to_rtf, doc_to_md, wp_to_md)
- `options`: JSON string of conversion options

### Batch Processing

#### Submit a Batch Job

```
POST /mcp/tools/execute
```

Request body:
```json
{
  "tool": "batch_convert",
  "parameters": {
    "files": [
      {
        "content": "file content 1",
        "fileName": "document1.rtf",
        "conversionType": "rtf_to_md"
      },
      {
        "content": "file content 2",
        "fileName": "document2.md",
        "conversionType": "md_to_rtf"
      }
    ],
    "batchOptions": {
      "priority": "normal",
      "callbackUrl": "https://your-callback-url.com/webhook"
    }
  }
}
```

#### Get Batch Status

```
GET /mcp/batch/:batchId
```

#### Cancel a Batch Job

```
POST /mcp/batch/:batchId/cancel
```

## Integration Examples

### AI Assistant Integration

Example of integrating with an AI assistant using the MCP protocol:

```javascript
// Example AI assistant integration
async function convertDocument(content, format) {
  const response = await fetch('http://localhost:3030/mcp/tools/execute', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-API-Key': 'your-api-key'
    },
    body: JSON.stringify({
      tool: format === 'rtf' ? 'convert_rtf_to_markdown' : 'convert_markdown_to_rtf',
      parameters: {
        content,
        options: {
          preserveFormatting: true
        }
      }
    })
  });
  
  const result = await response.json();
  return result.result.content;
}
```

### Web Application Integration

Example of integrating with a web application:

```javascript
// File upload form
const form = new FormData();
form.append('file', fileInput.files[0]);
form.append('conversionType', 'rtf_to_md');
form.append('options', JSON.stringify({ preserveFormatting: true }));

const response = await fetch('http://localhost:3030/mcp/upload', {
  method: 'POST',
  headers: {
    'X-API-Key': 'your-api-key'
  },
  body: form
});

const result = await response.json();
console.log(result.result.content);
```

### Legacy System Integration

Example of integrating with a legacy system:

```vb
' VB6 example using MSXML
Dim http As Object
Set http = CreateObject("MSXML2.XMLHTTP")

http.Open "POST", "http://localhost:3030/mcp/upload", False
http.setRequestHeader "X-API-Key", "your-api-key"

' Create form data
Dim boundary As String
boundary = "----WebKitFormBoundary" & Format(Now, "yyyymmddhhnnss")

http.setRequestHeader "Content-Type", "multipart/form-data; boundary=" & boundary

' Build request body
Dim requestBody As String
requestBody = "--" & boundary & vbCrLf
requestBody = requestBody & "Content-Disposition: form-data; name=""file""; filename=""document.rtf""" & vbCrLf
requestBody = requestBody & "Content-Type: application/rtf" & vbCrLf & vbCrLf
requestBody = requestBody & rtfContent & vbCrLf
requestBody = requestBody & "--" & boundary & vbCrLf
requestBody = requestBody & "Content-Disposition: form-data; name=""conversionType""" & vbCrLf & vbCrLf
requestBody = requestBody & "rtf_to_md" & vbCrLf
requestBody = requestBody & "--" & boundary & vbCrLf
requestBody = requestBody & "Content-Disposition: form-data; name=""options""" & vbCrLf & vbCrLf
requestBody = requestBody & "{""preserveFormatting"":true}" & vbCrLf
requestBody = requestBody & "--" & boundary & "--" & vbCrLf

http.send requestBody

' Process response
If http.Status = 200 Then
    Dim response As String
    response = http.responseText
    ' Parse JSON response
    ' ...
End If
```

## Performance Optimization

To optimize performance:

1. **Enable Caching**: Set `CACHE_ENABLED=true` and use Redis for production environments
2. **Batch Processing**: Use batch operations for multiple files
3. **Tune Concurrency**: Adjust `MAX_CONCURRENT_JOBS` based on your server capacity
4. **Use Docker Compose**: Scale horizontally by increasing the number of MCP server instances

## Monitoring and Logging

The MCP server provides several monitoring endpoints:

- **Health Check**: `GET /health`
- **Metrics**: `GET /metrics`
- **Logs**: Stored in the `logs` directory

For production deployments, consider integrating with monitoring systems like Prometheus and Grafana.

## Troubleshooting

Common issues and solutions:

1. **Connection Refused**: Ensure the server is running and the port is accessible
2. **Authentication Failed**: Verify your API key or JWT token
3. **Legacy Format Conversion Failed**: Check that LibreOffice and Pandoc are installed
4. **Out of Memory**: Increase container memory limits or reduce batch size

## Security Considerations

To secure your MCP server:

1. **Use HTTPS**: Deploy behind a reverse proxy with SSL/TLS
2. **Implement Rate Limiting**: Protect against abuse
3. **Validate Input**: Sanitize and validate all input
4. **Restrict Access**: Use API keys and limit access to trusted clients
5. **Regular Updates**: Keep dependencies up to date

---

For more information, please refer to the [LegacyBridge documentation](https://github.com/yourusername/legacy-bridge/docs) or contact support.