// Unit tests for MCPServer
import { MCPServer } from '../../../../src/mcp-server/core/mcp-server';
import { mockMCPConfig } from '../../mocks/config.mock';
import express from 'express';
import http from 'http';
import { Server as SocketIOServer } from 'socket.io';

// Mock express
jest.mock('express', () => {
  const mockExpress = jest.fn(() => ({
    use: jest.fn(),
    get: jest.fn(),
    listen: jest.fn()
  }));
  mockExpress.json = jest.fn().mockReturnValue('json-middleware');
  mockExpress.urlencoded = jest.fn().mockReturnValue('urlencoded-middleware');
  mockExpress.static = jest.fn().mockReturnValue('static-middleware');
  return mockExpress;
});

// Mock external middleware
jest.mock('helmet', () => jest.fn().mockReturnValue('helmet-middleware'));
jest.mock('cors', () => jest.fn().mockReturnValue('cors-middleware'));
jest.mock('express-rate-limit', () => jest.fn().mockReturnValue('rate-limit-middleware'));
jest.mock('body-parser', () => ({
  json: jest.fn().mockReturnValue('json-middleware'),
  urlencoded: jest.fn().mockReturnValue('urlencoded-middleware')
}));

// Mock http
jest.mock('http', () => ({
  createServer: jest.fn().mockReturnValue({
    listen: jest.fn(),
    close: jest.fn()
  })
}));

// Mock socket.io
jest.mock('socket.io', () => ({
  Server: jest.fn().mockImplementation(() => {
    const mockTo = jest.fn().mockReturnValue({
      emit: jest.fn()
    });
    return {
      on: jest.fn(),
      to: mockTo,
      emit: jest.fn()
    };
  })
}));

// Mock middleware
jest.mock('../../../../src/mcp-server/middleware/mcp-auth', () => ({
  MCPAuthMiddleware: jest.fn().mockReturnValue('auth-middleware')
}));

jest.mock('../../../../src/mcp-server/middleware/mcp-error-handler', () => ({
  MCPErrorHandler: jest.fn().mockReturnValue('error-handler-middleware')
}));

// Mock routes
jest.mock('../../../../src/mcp-server/routes/mcp-router', () => ({
  MCPRouter: jest.fn().mockReturnValue('mcp-router')
}));

// Mock services and utilities
jest.mock('../../../../src/mcp-server/utils/mcp-logger', () => ({
  MCPLogger: jest.fn().mockImplementation(() => ({
    debug: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn()
  }))
}));

jest.mock('../../../../src/mcp-server/services/mcp-cache', () => ({
  MCPCache: jest.fn().mockImplementation(() => ({
    get: jest.fn(),
    set: jest.fn(),
    delete: jest.fn()
  }))
}));

jest.mock('../../../../src/mcp-server/utils/mcp-metrics', () => ({
  MCPMetricsCollector: jest.fn().mockImplementation(() => ({
    recordRequest: jest.fn(),
    recordConversion: jest.fn(),
    getMetrics: jest.fn()
  }))
}));

// Mock utils
jest.mock('../../../../src/mcp-server/utils/mcp-logger', () => ({
  MCPLogger: jest.fn().mockImplementation(() => ({
    info: jest.fn(),
    error: jest.fn()
  }))
}));

jest.mock('../../../../src/mcp-server/utils/mcp-metrics', () => ({
  MCPMetricsCollector: jest.fn().mockImplementation(() => ({
    recordRequest: jest.fn(),
    getMetrics: jest.fn()
  }))
}));

// Mock services
jest.mock('../../../../src/mcp-server/services/mcp-cache', () => ({
  MCPCache: jest.fn().mockImplementation(() => ({
    close: jest.fn()
  }))
}));

// Mock helmet
jest.mock('helmet', () => jest.fn().mockReturnValue('helmet-middleware'));

// Mock cors
jest.mock('cors', () => jest.fn().mockReturnValue('cors-middleware'));

// Mock rate-limit
jest.mock('express-rate-limit', () => jest.fn().mockReturnValue('rate-limit-middleware'));

describe('MCPServer', () => {
  let server: MCPServer;
  let mockApp: any;
  let mockHttpServer: any;
  let mockIo: any;
  
  beforeEach(() => {
    jest.clearAllMocks();

    // Set up mocked instances first
    mockApp = {
      use: jest.fn(),
      get: jest.fn(),
      listen: jest.fn()
    };
    mockHttpServer = {
      listen: jest.fn(),
      close: jest.fn()
    };
    mockIo = {
      on: jest.fn(),
      to: jest.fn().mockReturnValue({
        emit: jest.fn()
      }),
      emit: jest.fn()
    };

    // Mock the constructors to return our instances
    (express as jest.Mock).mockReturnValue(mockApp);
    (http.createServer as jest.Mock).mockReturnValue(mockHttpServer);
    (SocketIOServer as jest.Mock).mockReturnValue(mockIo);

    server = new MCPServer(mockMCPConfig);
  });
  
  describe('constructor', () => {
    test('should initialize server components', () => {
      expect(express).toHaveBeenCalled();
      expect(http.createServer).toHaveBeenCalled();
      expect(SocketIOServer).toHaveBeenCalled();

      // Verify basic setup occurred
      expect(mockApp.use).toHaveBeenCalled();
      expect(mockApp.get).toHaveBeenCalled();
      expect(mockIo.on).toHaveBeenCalled();
    });
  });
  
  describe('start', () => {
    test('should start the server', () => {
      server.start();
      
      expect(mockHttpServer.listen).toHaveBeenCalledWith(
        mockMCPConfig.port,
        expect.any(Function)
      );
    });
  });
  
  describe('stop', () => {
    test('should stop the server', async () => {
      // Mock server.close to call callback
      mockHttpServer.close.mockImplementation((callback) => {
        callback();
      });
      
      await server.stop();
      
      expect(mockHttpServer.close).toHaveBeenCalled();
    });
    
    test('should handle server close errors', async () => {
      // Mock server.close to call callback with error
      mockHttpServer.close.mockImplementation((callback) => {
        callback(new Error('Server close error'));
      });
      
      await expect(server.stop()).rejects.toThrow('Server close error');
    });
  });
  
  describe('broadcastProgress', () => {
    test('should broadcast progress to clients', () => {
      const jobId = 'test-job-id';
      const progress = { completed: 50, total: 100 };

      // Just verify the method can be called without errors
      expect(() => {
        server.broadcastProgress(jobId, progress);
      }).not.toThrow();

      expect(mockIo.to).toHaveBeenCalledWith(`job-${jobId}`);
    });
  });
});