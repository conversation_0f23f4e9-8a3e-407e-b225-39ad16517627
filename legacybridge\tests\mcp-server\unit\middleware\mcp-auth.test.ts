// Unit tests for MCPAuthMiddleware
import { MCPAuthMiddleware } from '../../../../src/mcp-server/middleware/mcp-auth';
import { mockMCPConfig } from '../../mocks/config.mock';
import { createMockRequest, createMockResponse, createMockNextFunction } from '../../mocks/express.mock';

// Mock jsonwebtoken
jest.mock('jsonwebtoken', () => ({
  verify: jest.fn()
}));

describe('MCPAuthMiddleware', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });
  
  test('should allow requests with valid API key in header', () => {
    const config = {
      ...mockMCPConfig,
      apiKeys: ['valid-api-key']
    };

    const middleware = MCPAuthMiddleware(config);

    const req = createMockRequest({
      headers: {
        'x-api-key': 'valid-api-key'
      }
    });

    const res = createMockResponse();
    const next = createMockNextFunction();

    middleware(req as any, res as any, next);

    expect(next).toHaveBeenCalled();
    expect(next).not.toHaveBeenCalledWith(expect.any(Error));
  });
  
  test('should allow requests with valid API key in query parameter', () => {
    const config = {
      ...mockMCPConfig,
      apiKeys: ['valid-api-key']
    };
    
    const middleware = MCPAuthMiddleware(config);
    
    const req = createMockRequest({
      query: {
        api_key: 'valid-api-key'
      }
    });
    
    const res = createMockResponse();
    const next = createMockNextFunction();
    
    middleware(req as any, res as any, next);
    
    expect(next).toHaveBeenCalled();
    expect(next).not.toHaveBeenCalledWith(expect.any(Error));
  });
  
  test('should allow requests with valid JWT token', () => {
    const config = {
      ...mockMCPConfig,
      jwtSecret: 'test-secret'
    };
    
    const middleware = MCPAuthMiddleware(config);
    
    const req = createMockRequest({
      headers: {
        authorization: 'Bearer valid-jwt-token'
      }
    });
    
    const res = createMockResponse();
    const next = createMockNextFunction();
    
    // Mock JWT verification
    const jwt = require('jsonwebtoken');
    jwt.verify.mockImplementation(() => ({ userId: '123' }));
    
    middleware(req as any, res as any, next);
    
    expect(jwt.verify).toHaveBeenCalledWith('valid-jwt-token', 'test-secret');
    expect(next).toHaveBeenCalled();
    expect(next).not.toHaveBeenCalledWith(expect.any(Error));
  });
  
  test('should reject requests with invalid API key', () => {
    const config = {
      ...mockMCPConfig,
      apiKeys: ['valid-api-key']
    };
    
    const middleware = MCPAuthMiddleware(config);
    
    const req = createMockRequest({
      headers: {
        'x-api-key': 'invalid-api-key'
      }
    });
    
    const res = createMockResponse();
    const next = createMockNextFunction();
    
    middleware(req as any, res as any, next);

    expect(res.status).toHaveBeenCalledWith(401);
    expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
      status: 'error',
      message: expect.stringContaining('Invalid API key')
    }));
  });
  
  test('should reject requests with invalid JWT token', () => {
    const config = {
      ...mockMCPConfig,
      jwtSecret: 'test-secret'
    };
    
    const middleware = MCPAuthMiddleware(config);
    
    const req = createMockRequest({
      headers: {
        authorization: 'Bearer invalid-jwt-token'
      }
    });
    
    const res = createMockResponse();
    const next = createMockNextFunction();
    
    // Mock JWT verification failure
    const jwt = require('jsonwebtoken');
    jwt.verify.mockImplementation(() => {
      throw new Error('Invalid token');
    });
    
    middleware(req as any, res as any, next);

    expect(jwt.verify).toHaveBeenCalledWith('invalid-jwt-token', 'test-secret');
    expect(res.status).toHaveBeenCalledWith(401);
    expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
      status: 'error',
      message: 'Invalid or expired token'
    }));
  });
  
  test('should reject requests with no authentication', () => {
    const config = {
      ...mockMCPConfig,
      apiKeys: ['valid-api-key'],
      jwtSecret: 'test-secret'
    };

    const middleware = MCPAuthMiddleware(config);

    const req = createMockRequest({});
    const res = createMockResponse();
    const next = createMockNextFunction();

    middleware(req as any, res as any, next);

    // In test environment, should require authentication
    expect(res.status).toHaveBeenCalledWith(401);
    expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
      status: 'error',
      message: 'Unauthorized'
    }));
    expect(next).not.toHaveBeenCalled();
  });
  
  test('should allow requests to health endpoint without authentication', () => {
    const config = {
      ...mockMCPConfig,
      apiKeys: ['valid-api-key']
    };
    
    const middleware = MCPAuthMiddleware(config);
    
    const req = createMockRequest({
      path: '/health'
    });
    
    const res = createMockResponse();
    const next = createMockNextFunction();
    
    middleware(req as any, res as any, next);
    
    expect(next).toHaveBeenCalled();
    expect(next).not.toHaveBeenCalledWith(expect.any(Error));
  });
  
  test('should allow requests to metrics endpoint without authentication', () => {
    const config = {
      ...mockMCPConfig,
      apiKeys: ['valid-api-key']
    };
    
    const middleware = MCPAuthMiddleware(config);
    
    const req = createMockRequest({
      path: '/metrics'
    });
    
    const res = createMockResponse();
    const next = createMockNextFunction();
    
    middleware(req as any, res as any, next);
    
    expect(next).toHaveBeenCalled();
    expect(next).not.toHaveBeenCalledWith(expect.any(Error));
  });
  
  test('should skip authentication if no API keys or JWT secret configured', () => {
    const config = {
      ...mockMCPConfig,
      apiKeys: [],
      jwtSecret: ''
    };
    
    const middleware = MCPAuthMiddleware(config);
    
    const req = createMockRequest({});
    const res = createMockResponse();
    const next = createMockNextFunction();
    
    middleware(req as any, res as any, next);
    
    expect(next).toHaveBeenCalled();
    expect(next).not.toHaveBeenCalledWith(expect.any(Error));
  });
});