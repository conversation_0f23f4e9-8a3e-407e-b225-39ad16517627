{"compilerOptions": {"target": "ES2020", "module": "NodeNext", "moduleResolution": "NodeNext", "esModuleInterop": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "outDir": "dist", "declaration": true, "sourceMap": true, "resolveJsonModule": true, "baseUrl": ".", "paths": {"@/*": ["src/*"]}}, "include": ["src/mcp-server/**/*.ts"], "exclude": ["node_modules", "dist", "**/*.test.ts"]}