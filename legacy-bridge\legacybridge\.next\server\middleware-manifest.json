{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "s9YmmoTUGURKioqe/lal3cKOI51GRMNwHRoEPGegloI=", "__NEXT_PREVIEW_MODE_ID": "e929b3cb16d8cd2fce5bdeb41e5dc4a6", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "a32027c75e11347f5b4e6d50ab7b1532d204497bda43cd54511536f8b77653f5", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "29f47664f03bec5794bd214b23d0a81d19e0066c4b1bea09da1780f7a10664fd"}}}, "functions": {}, "sortedMiddleware": ["/"]}