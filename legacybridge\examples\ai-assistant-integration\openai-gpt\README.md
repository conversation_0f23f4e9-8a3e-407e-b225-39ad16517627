# OpenAI GPT Integration Example

This example demonstrates how to integrate the LegacyBridge MCP server with OpenAI's GPT models using the function calling feature.

## Prerequisites

- Node.js 16 or later
- LegacyBridge MCP server running
- OpenAI API key

## Setup

1. Install dependencies:

   ```bash
   npm install
   ```

2. Create a `.env` file with your API keys:

   ```
   OPENAI_API_KEY=your-openai-api-key
   MCP_API_KEY=your-mcp-api-key
   MCP_SERVER_URL=http://localhost:3030
   ```

3. Run the example:

   ```bash
   node index.js
   ```

## How It Works

This example uses OpenAI's function calling feature to allow the GPT model to convert between RTF and Markdown formats using the LegacyBridge MCP server.

The main components are:

1. **Function Definitions**: We define functions that the GPT model can call to convert documents.
2. **OpenAI API Integration**: We use the OpenAI API to create a conversation with function calling.
3. **MCP Server Integration**: We implement the functions to call the MCP server API.

## Example Conversation

Here's an example conversation with the GPT model:

```
User: I have an RTF document that I need to convert to Markdown. Here's the content:
{\\rtf1\\ansi\\ansicpg1252\\cocoartf2580
\\cocoatextscaling0\\cocoaplatform0{\\fonttbl\\f0\\fswiss\\fcharset0 Helvetica;}
{\\colortbl;\\red255\\green255\\blue255;}
{\\*\\expandedcolortbl;;}
\\paperw11900\\paperh16840\\margl1440\\margr1440\\vieww11520\\viewh8400\\viewkind0
\\pard\\tx566\\tx1133\\tx1700\\tx2267\\tx2834\\tx3401\\tx3968\\tx4535\\tx5102\\tx5669\\tx6236\\tx6803\\pardirnatural\\partightenfactor0

\\f0\\fs24 \\cf0 # Sample Document\\
\\
This is a **bold** statement.\\
\\
* Item 1\\
* Item 2\\
* Item 3}

GPT: I'll help you convert this RTF document to Markdown. Let me do that for you.

[GPT calls convert_rtf_to_markdown function]

Here's your document converted to Markdown:

```markdown
# Sample Document

This is a **bold** statement.

* Item 1
* Item 2
* Item 3
```

The conversion has preserved the formatting including the heading, bold text, and bullet points.

User: Thanks! Now can you convert this Markdown back to RTF?

GPT: I'll convert that Markdown back to RTF for you.

[GPT calls convert_markdown_to_rtf function]

I've converted the Markdown back to RTF. Here's the RTF content:

```rtf
{\rtf1\ansi\deff0{\fonttbl{\f0 \fswiss Helvetica;}}
{\colortbl;\red255\green0\blue0;\red0\green0\blue255;}
\viewkind4\uc1\pard\f0\fs24
\b\fs32 Sample Document\b0\fs24 \par
\par
This is a \b bold\b0  statement.\par
\par
\pard{\pntext\f0 \li360\fi-360 \bullet\tab}{\*\pn\pnlvlblt\pnf0\pnindent360{\pntxtb\bullet}}\li360\fi-360 Item 1\par
{\pntext\f0 \li360\fi-360 \bullet\tab}{\*\pn\pnlvlblt\pnf0\pnindent360{\pntxtb\bullet}}\li360\fi-360 Item 2\par
{\pntext\f0 \li360\fi-360 \bullet\tab}{\*\pn\pnlvlblt\pnf0\pnindent360{\pntxtb\bullet}}\li360\fi-360 Item 3\par
\pard\par
}
```

You can now use this RTF content in applications that support the RTF format, such as Microsoft Word, TextEdit, or other word processors.

## Code Structure

- `index.js`: Main example script
- `mcp-client.js`: Client for the MCP server API
- `openai-client.js`: Client for the OpenAI API
- `functions.js`: Function definitions for OpenAI function calling

## Extending the Example

You can extend this example to:

1. Add support for more document formats (DOC, WordPerfect)
2. Implement batch processing for multiple documents
3. Add a web interface for user interaction
4. Implement streaming responses for real-time conversion