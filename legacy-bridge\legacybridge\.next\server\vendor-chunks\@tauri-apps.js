"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tauri-apps";
exports.ids = ["vendor-chunks/@tauri-apps"];
exports.modules = {

/***/ "(ssr)/./node_modules/@tauri-apps/api/core.js":
/*!**********************************************!*\
  !*** ./node_modules/@tauri-apps/api/core.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Channel: () => (/* binding */ Channel),\n/* harmony export */   PluginListener: () => (/* binding */ PluginListener),\n/* harmony export */   Resource: () => (/* binding */ Resource),\n/* harmony export */   SERIALIZE_TO_IPC_FN: () => (/* binding */ SERIALIZE_TO_IPC_FN),\n/* harmony export */   addPluginListener: () => (/* binding */ addPluginListener),\n/* harmony export */   checkPermissions: () => (/* binding */ checkPermissions),\n/* harmony export */   convertFileSrc: () => (/* binding */ convertFileSrc),\n/* harmony export */   invoke: () => (/* binding */ invoke),\n/* harmony export */   isTauri: () => (/* binding */ isTauri),\n/* harmony export */   requestPermissions: () => (/* binding */ requestPermissions),\n/* harmony export */   transformCallback: () => (/* binding */ transformCallback)\n/* harmony export */ });\n/* harmony import */ var _external_tslib_tslib_es6_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./external/tslib/tslib.es6.js */ \"(ssr)/./node_modules/@tauri-apps/api/external/tslib/tslib.es6.js\");\n\n\n// Copyright 2019-2024 Tauri Programme within The Commons Conservancy\n// SPDX-License-Identifier: Apache-2.0\n// SPDX-License-Identifier: MIT\nvar _Channel_onmessage, _Channel_nextMessageIndex, _Channel_pendingMessages, _Channel_messageEndIndex, _Resource_rid;\n/**\n * Invoke your custom commands.\n *\n * This package is also accessible with `window.__TAURI__.core` when [`app.withGlobalTauri`](https://v2.tauri.app/reference/config/#withglobaltauri) in `tauri.conf.json` is set to `true`.\n * @module\n */\n/**\n * A key to be used to implement a special function\n * on your types that define how your type should be serialized\n * when passing across the IPC.\n * @example\n * Given a type in Rust that looks like this\n * ```rs\n * #[derive(serde::Serialize, serde::Deserialize)\n * enum UserId {\n *   String(String),\n *   Number(u32),\n * }\n * ```\n * `UserId::String(\"id\")` would be serialized into `{ String: \"id\" }`\n * and so we need to pass the same structure back to Rust\n * ```ts\n * import { SERIALIZE_TO_IPC_FN } from \"@tauri-apps/api/core\"\n *\n * class UserIdString {\n *   id\n *   constructor(id) {\n *     this.id = id\n *   }\n *\n *   [SERIALIZE_TO_IPC_FN]() {\n *     return { String: this.id }\n *   }\n * }\n *\n * class UserIdNumber {\n *   id\n *   constructor(id) {\n *     this.id = id\n *   }\n *\n *   [SERIALIZE_TO_IPC_FN]() {\n *     return { Number: this.id }\n *   }\n * }\n *\n * type UserId = UserIdString | UserIdNumber\n * ```\n *\n */\n// if this value changes, make sure to update it in:\n// 1. ipc.js\n// 2. process-ipc-message-fn.js\nconst SERIALIZE_TO_IPC_FN = '__TAURI_TO_IPC_KEY__';\n/**\n * Stores the callback in a known location, and returns an identifier that can be passed to the backend.\n * The backend uses the identifier to `eval()` the callback.\n *\n * @return An unique identifier associated with the callback function.\n *\n * @since 1.0.0\n */\nfunction transformCallback(\n// TODO: Make this not optional in v3\ncallback, once = false) {\n    return window.__TAURI_INTERNALS__.transformCallback(callback, once);\n}\nclass Channel {\n    constructor(onmessage) {\n        _Channel_onmessage.set(this, void 0);\n        // the index is used as a mechanism to preserve message order\n        _Channel_nextMessageIndex.set(this, 0);\n        _Channel_pendingMessages.set(this, []);\n        _Channel_messageEndIndex.set(this, void 0);\n        (0,_external_tslib_tslib_es6_js__WEBPACK_IMPORTED_MODULE_0__.__classPrivateFieldSet)(this, _Channel_onmessage, onmessage || (() => { }), \"f\");\n        this.id = transformCallback((rawMessage) => {\n            const index = rawMessage.index;\n            if ('end' in rawMessage) {\n                if (index == (0,_external_tslib_tslib_es6_js__WEBPACK_IMPORTED_MODULE_0__.__classPrivateFieldGet)(this, _Channel_nextMessageIndex, \"f\")) {\n                    this.cleanupCallback();\n                }\n                else {\n                    (0,_external_tslib_tslib_es6_js__WEBPACK_IMPORTED_MODULE_0__.__classPrivateFieldSet)(this, _Channel_messageEndIndex, index, \"f\");\n                }\n                return;\n            }\n            const message = rawMessage.message;\n            // Process the message if we're at the right order\n            if (index == (0,_external_tslib_tslib_es6_js__WEBPACK_IMPORTED_MODULE_0__.__classPrivateFieldGet)(this, _Channel_nextMessageIndex, \"f\")) {\n                (0,_external_tslib_tslib_es6_js__WEBPACK_IMPORTED_MODULE_0__.__classPrivateFieldGet)(this, _Channel_onmessage, \"f\").call(this, message);\n                (0,_external_tslib_tslib_es6_js__WEBPACK_IMPORTED_MODULE_0__.__classPrivateFieldSet)(this, _Channel_nextMessageIndex, (0,_external_tslib_tslib_es6_js__WEBPACK_IMPORTED_MODULE_0__.__classPrivateFieldGet)(this, _Channel_nextMessageIndex, \"f\") + 1, \"f\");\n                // process pending messages\n                while ((0,_external_tslib_tslib_es6_js__WEBPACK_IMPORTED_MODULE_0__.__classPrivateFieldGet)(this, _Channel_nextMessageIndex, \"f\") in (0,_external_tslib_tslib_es6_js__WEBPACK_IMPORTED_MODULE_0__.__classPrivateFieldGet)(this, _Channel_pendingMessages, \"f\")) {\n                    const message = (0,_external_tslib_tslib_es6_js__WEBPACK_IMPORTED_MODULE_0__.__classPrivateFieldGet)(this, _Channel_pendingMessages, \"f\")[(0,_external_tslib_tslib_es6_js__WEBPACK_IMPORTED_MODULE_0__.__classPrivateFieldGet)(this, _Channel_nextMessageIndex, \"f\")];\n                    (0,_external_tslib_tslib_es6_js__WEBPACK_IMPORTED_MODULE_0__.__classPrivateFieldGet)(this, _Channel_onmessage, \"f\").call(this, message);\n                    // eslint-disable-next-line @typescript-eslint/no-array-delete\n                    delete (0,_external_tslib_tslib_es6_js__WEBPACK_IMPORTED_MODULE_0__.__classPrivateFieldGet)(this, _Channel_pendingMessages, \"f\")[(0,_external_tslib_tslib_es6_js__WEBPACK_IMPORTED_MODULE_0__.__classPrivateFieldGet)(this, _Channel_nextMessageIndex, \"f\")];\n                    (0,_external_tslib_tslib_es6_js__WEBPACK_IMPORTED_MODULE_0__.__classPrivateFieldSet)(this, _Channel_nextMessageIndex, (0,_external_tslib_tslib_es6_js__WEBPACK_IMPORTED_MODULE_0__.__classPrivateFieldGet)(this, _Channel_nextMessageIndex, \"f\") + 1, \"f\");\n                }\n                if ((0,_external_tslib_tslib_es6_js__WEBPACK_IMPORTED_MODULE_0__.__classPrivateFieldGet)(this, _Channel_nextMessageIndex, \"f\") === (0,_external_tslib_tslib_es6_js__WEBPACK_IMPORTED_MODULE_0__.__classPrivateFieldGet)(this, _Channel_messageEndIndex, \"f\")) {\n                    this.cleanupCallback();\n                }\n            }\n            // Queue the message if we're not\n            else {\n                // eslint-disable-next-line security/detect-object-injection\n                (0,_external_tslib_tslib_es6_js__WEBPACK_IMPORTED_MODULE_0__.__classPrivateFieldGet)(this, _Channel_pendingMessages, \"f\")[index] = message;\n            }\n        });\n    }\n    cleanupCallback() {\n        window.__TAURI_INTERNALS__.unregisterCallback(this.id);\n    }\n    set onmessage(handler) {\n        (0,_external_tslib_tslib_es6_js__WEBPACK_IMPORTED_MODULE_0__.__classPrivateFieldSet)(this, _Channel_onmessage, handler, \"f\");\n    }\n    get onmessage() {\n        return (0,_external_tslib_tslib_es6_js__WEBPACK_IMPORTED_MODULE_0__.__classPrivateFieldGet)(this, _Channel_onmessage, \"f\");\n    }\n    [(_Channel_onmessage = new WeakMap(), _Channel_nextMessageIndex = new WeakMap(), _Channel_pendingMessages = new WeakMap(), _Channel_messageEndIndex = new WeakMap(), SERIALIZE_TO_IPC_FN)]() {\n        return `__CHANNEL__:${this.id}`;\n    }\n    toJSON() {\n        // eslint-disable-next-line security/detect-object-injection\n        return this[SERIALIZE_TO_IPC_FN]();\n    }\n}\nclass PluginListener {\n    constructor(plugin, event, channelId) {\n        this.plugin = plugin;\n        this.event = event;\n        this.channelId = channelId;\n    }\n    async unregister() {\n        return invoke(`plugin:${this.plugin}|remove_listener`, {\n            event: this.event,\n            channelId: this.channelId\n        });\n    }\n}\n/**\n * Adds a listener to a plugin event.\n *\n * @returns The listener object to stop listening to the events.\n *\n * @since 2.0.0\n */\nasync function addPluginListener(plugin, event, cb) {\n    const handler = new Channel(cb);\n    return invoke(`plugin:${plugin}|registerListener`, { event, handler }).then(() => new PluginListener(plugin, event, handler.id));\n}\n/**\n * Get permission state for a plugin.\n *\n * This should be used by plugin authors to wrap their actual implementation.\n */\nasync function checkPermissions(plugin) {\n    return invoke(`plugin:${plugin}|check_permissions`);\n}\n/**\n * Request permissions.\n *\n * This should be used by plugin authors to wrap their actual implementation.\n */\nasync function requestPermissions(plugin) {\n    return invoke(`plugin:${plugin}|request_permissions`);\n}\n/**\n * Sends a message to the backend.\n * @example\n * ```typescript\n * import { invoke } from '@tauri-apps/api/core';\n * await invoke('login', { user: 'tauri', password: 'poiwe3h4r5ip3yrhtew9ty' });\n * ```\n *\n * @param cmd The command name.\n * @param args The optional arguments to pass to the command.\n * @param options The request options.\n * @return A promise resolving or rejecting to the backend response.\n *\n * @since 1.0.0\n */\nasync function invoke(cmd, args = {}, options) {\n    return window.__TAURI_INTERNALS__.invoke(cmd, args, options);\n}\n/**\n * Convert a device file path to an URL that can be loaded by the webview.\n * Note that `asset:` and `http://asset.localhost` must be added to [`app.security.csp`](https://v2.tauri.app/reference/config/#csp-1) in `tauri.conf.json`.\n * Example CSP value: `\"csp\": \"default-src 'self' ipc: http://ipc.localhost; img-src 'self' asset: http://asset.localhost\"` to use the asset protocol on image sources.\n *\n * Additionally, `\"enable\" : \"true\"` must be added to [`app.security.assetProtocol`](https://v2.tauri.app/reference/config/#assetprotocolconfig)\n * in `tauri.conf.json` and its access scope must be defined on the `scope` array on the same `assetProtocol` object.\n *\n * @param  filePath The file path.\n * @param  protocol The protocol to use. Defaults to `asset`. You only need to set this when using a custom protocol.\n * @example\n * ```typescript\n * import { appDataDir, join } from '@tauri-apps/api/path';\n * import { convertFileSrc } from '@tauri-apps/api/core';\n * const appDataDirPath = await appDataDir();\n * const filePath = await join(appDataDirPath, 'assets/video.mp4');\n * const assetUrl = convertFileSrc(filePath);\n *\n * const video = document.getElementById('my-video');\n * const source = document.createElement('source');\n * source.type = 'video/mp4';\n * source.src = assetUrl;\n * video.appendChild(source);\n * video.load();\n * ```\n *\n * @return the URL that can be used as source on the webview.\n *\n * @since 1.0.0\n */\nfunction convertFileSrc(filePath, protocol = 'asset') {\n    return window.__TAURI_INTERNALS__.convertFileSrc(filePath, protocol);\n}\n/**\n * A rust-backed resource stored through `tauri::Manager::resources_table` API.\n *\n * The resource lives in the main process and does not exist\n * in the Javascript world, and thus will not be cleaned up automatiacally\n * except on application exit. If you want to clean it up early, call {@linkcode Resource.close}\n *\n * @example\n * ```typescript\n * import { Resource, invoke } from '@tauri-apps/api/core';\n * export class DatabaseHandle extends Resource {\n *   static async open(path: string): Promise<DatabaseHandle> {\n *     const rid: number = await invoke('open_db', { path });\n *     return new DatabaseHandle(rid);\n *   }\n *\n *   async execute(sql: string): Promise<void> {\n *     await invoke('execute_sql', { rid: this.rid, sql });\n *   }\n * }\n * ```\n */\nclass Resource {\n    get rid() {\n        return (0,_external_tslib_tslib_es6_js__WEBPACK_IMPORTED_MODULE_0__.__classPrivateFieldGet)(this, _Resource_rid, \"f\");\n    }\n    constructor(rid) {\n        _Resource_rid.set(this, void 0);\n        (0,_external_tslib_tslib_es6_js__WEBPACK_IMPORTED_MODULE_0__.__classPrivateFieldSet)(this, _Resource_rid, rid, \"f\");\n    }\n    /**\n     * Destroys and cleans up this resource from memory.\n     * **You should not call any method on this object anymore and should drop any reference to it.**\n     */\n    async close() {\n        return invoke('plugin:resources|close', {\n            rid: this.rid\n        });\n    }\n}\n_Resource_rid = new WeakMap();\nfunction isTauri() {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/no-unsafe-member-access\n    return !!(globalThis || window).isTauri;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tauri-apps/api/core.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tauri-apps/api/event.js":
/*!***********************************************!*\
  !*** ./node_modules/@tauri-apps/api/event.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TauriEvent: () => (/* binding */ TauriEvent),\n/* harmony export */   emit: () => (/* binding */ emit),\n/* harmony export */   emitTo: () => (/* binding */ emitTo),\n/* harmony export */   listen: () => (/* binding */ listen),\n/* harmony export */   once: () => (/* binding */ once)\n/* harmony export */ });\n/* harmony import */ var _core_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./core.js */ \"(ssr)/./node_modules/@tauri-apps/api/core.js\");\n\n\n// Copyright 2019-2024 Tauri Programme within The Commons Conservancy\n// SPDX-License-Identifier: Apache-2.0\n// SPDX-License-Identifier: MIT\n/**\n * The event system allows you to emit events to the backend and listen to events from it.\n *\n * This package is also accessible with `window.__TAURI__.event` when [`app.withGlobalTauri`](https://v2.tauri.app/reference/config/#withglobaltauri) in `tauri.conf.json` is set to `true`.\n * @module\n */\n/**\n * @since 1.1.0\n */\nvar TauriEvent;\n(function (TauriEvent) {\n    TauriEvent[\"WINDOW_RESIZED\"] = \"tauri://resize\";\n    TauriEvent[\"WINDOW_MOVED\"] = \"tauri://move\";\n    TauriEvent[\"WINDOW_CLOSE_REQUESTED\"] = \"tauri://close-requested\";\n    TauriEvent[\"WINDOW_DESTROYED\"] = \"tauri://destroyed\";\n    TauriEvent[\"WINDOW_FOCUS\"] = \"tauri://focus\";\n    TauriEvent[\"WINDOW_BLUR\"] = \"tauri://blur\";\n    TauriEvent[\"WINDOW_SCALE_FACTOR_CHANGED\"] = \"tauri://scale-change\";\n    TauriEvent[\"WINDOW_THEME_CHANGED\"] = \"tauri://theme-changed\";\n    TauriEvent[\"WINDOW_CREATED\"] = \"tauri://window-created\";\n    TauriEvent[\"WEBVIEW_CREATED\"] = \"tauri://webview-created\";\n    TauriEvent[\"DRAG_ENTER\"] = \"tauri://drag-enter\";\n    TauriEvent[\"DRAG_OVER\"] = \"tauri://drag-over\";\n    TauriEvent[\"DRAG_DROP\"] = \"tauri://drag-drop\";\n    TauriEvent[\"DRAG_LEAVE\"] = \"tauri://drag-leave\";\n})(TauriEvent || (TauriEvent = {}));\n/**\n * Unregister the event listener associated with the given name and id.\n *\n * @ignore\n * @param event The event name\n * @param eventId Event identifier\n * @returns\n */\nasync function _unlisten(event, eventId) {\n    window.__TAURI_EVENT_PLUGIN_INTERNALS__.unregisterListener(event, eventId);\n    await (0,_core_js__WEBPACK_IMPORTED_MODULE_0__.invoke)('plugin:event|unlisten', {\n        event,\n        eventId\n    });\n}\n/**\n * Listen to an emitted event to any {@link EventTarget|target}.\n *\n * @example\n * ```typescript\n * import { listen } from '@tauri-apps/api/event';\n * const unlisten = await listen<string>('error', (event) => {\n *   console.log(`Got error, payload: ${event.payload}`);\n * });\n *\n * // you need to call unlisten if your handler goes out of scope e.g. the component is unmounted\n * unlisten();\n * ```\n *\n * @param event Event name. Must include only alphanumeric characters, `-`, `/`, `:` and `_`.\n * @param handler Event handler callback.\n * @param options Event listening options.\n * @returns A promise resolving to a function to unlisten to the event.\n * Note that removing the listener is required if your listener goes out of scope e.g. the component is unmounted.\n *\n * @since 1.0.0\n */\nasync function listen(event, handler, options) {\n    var _a;\n    const target = typeof (options === null || options === void 0 ? void 0 : options.target) === 'string'\n        ? { kind: 'AnyLabel', label: options.target }\n        : ((_a = options === null || options === void 0 ? void 0 : options.target) !== null && _a !== void 0 ? _a : { kind: 'Any' });\n    return (0,_core_js__WEBPACK_IMPORTED_MODULE_0__.invoke)('plugin:event|listen', {\n        event,\n        target,\n        handler: (0,_core_js__WEBPACK_IMPORTED_MODULE_0__.transformCallback)(handler)\n    }).then((eventId) => {\n        return async () => _unlisten(event, eventId);\n    });\n}\n/**\n * Listens once to an emitted event to any {@link EventTarget|target}.\n *\n * @example\n * ```typescript\n * import { once } from '@tauri-apps/api/event';\n * interface LoadedPayload {\n *   loggedIn: boolean,\n *   token: string\n * }\n * const unlisten = await once<LoadedPayload>('loaded', (event) => {\n *   console.log(`App is loaded, loggedIn: ${event.payload.loggedIn}, token: ${event.payload.token}`);\n * });\n *\n * // you need to call unlisten if your handler goes out of scope e.g. the component is unmounted\n * unlisten();\n * ```\n *\n * @param event Event name. Must include only alphanumeric characters, `-`, `/`, `:` and `_`.\n * @param handler Event handler callback.\n * @param options Event listening options.\n * @returns A promise resolving to a function to unlisten to the event.\n * Note that removing the listener is required if your listener goes out of scope e.g. the component is unmounted.\n *\n * @since 1.0.0\n */\nasync function once(event, handler, options) {\n    return listen(event, (eventData) => {\n        void _unlisten(event, eventData.id);\n        handler(eventData);\n    }, options);\n}\n/**\n * Emits an event to all {@link EventTarget|targets}.\n *\n * @example\n * ```typescript\n * import { emit } from '@tauri-apps/api/event';\n * await emit('frontend-loaded', { loggedIn: true, token: 'authToken' });\n * ```\n *\n * @param event Event name. Must include only alphanumeric characters, `-`, `/`, `:` and `_`.\n * @param payload Event payload.\n *\n * @since 1.0.0\n */\nasync function emit(event, payload) {\n    await (0,_core_js__WEBPACK_IMPORTED_MODULE_0__.invoke)('plugin:event|emit', {\n        event,\n        payload\n    });\n}\n/**\n * Emits an event to all {@link EventTarget|targets} matching the given target.\n *\n * @example\n * ```typescript\n * import { emitTo } from '@tauri-apps/api/event';\n * await emitTo('main', 'frontend-loaded', { loggedIn: true, token: 'authToken' });\n * ```\n *\n * @param target Label of the target Window/Webview/WebviewWindow or raw {@link EventTarget} object.\n * @param event Event name. Must include only alphanumeric characters, `-`, `/`, `:` and `_`.\n * @param payload Event payload.\n *\n * @since 2.0.0\n */\nasync function emitTo(target, event, payload) {\n    const eventTarget = typeof target === 'string' ? { kind: 'AnyLabel', label: target } : target;\n    await (0,_core_js__WEBPACK_IMPORTED_MODULE_0__.invoke)('plugin:event|emit_to', {\n        target: eventTarget,\n        event,\n        payload\n    });\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tauri-apps/api/event.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tauri-apps/api/external/tslib/tslib.es6.js":
/*!******************************************************************!*\
  !*** ./node_modules/@tauri-apps/api/external/tslib/tslib.es6.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __classPrivateFieldGet: () => (/* binding */ __classPrivateFieldGet),\n/* harmony export */   __classPrivateFieldSet: () => (/* binding */ __classPrivateFieldSet)\n/* harmony export */ });\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise, SuppressedError, Symbol, Iterator */\r\n\r\n\r\nfunction __classPrivateFieldGet(receiver, state, kind, f) {\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\r\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\r\n}\r\n\r\nfunction __classPrivateFieldSet(receiver, state, value, kind, f) {\r\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\r\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\r\n}\r\n\r\ntypeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\r\n    var e = new Error(message);\r\n    return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\r\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tauri-apps/api/external/tslib/tslib.es6.js\n");

/***/ })

};
;