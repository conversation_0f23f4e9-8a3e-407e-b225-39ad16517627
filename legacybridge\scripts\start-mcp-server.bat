@echo off
REM Start MCP Server for Windows

REM Set default environment variables
if "%NODE_ENV%"=="" set NODE_ENV=development
if "%MCP_PORT%"=="" set MCP_PORT=3030
if "%LOG_LEVEL%"=="" set LOG_LEVEL=info

echo Checking dependencies...

REM Check for Node.js
where node >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Node.js is not installed. Please install Node.js 16 or later.
    exit /b 1
)

REM Check for npm
where npm >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo npm is not installed. Please install npm.
    exit /b 1
)

REM Check for LibreOffice (optional, for legacy format support)
where soffice >nul 2>nul
if %ERRORLEVEL% equ 0 (
    echo LibreOffice is installed. Legacy format conversion will be available.
    set ENABLE_DOC=true
    set ENABLE_WORDPERFECT=true
) else (
    echo LibreOffice is not installed. Legacy format conversion will be disabled.
    set ENABLE_DOC=false
    set ENABLE_WORDPERFECT=false
)

REM Check for Pandoc (optional, for legacy format support)
where pandoc >nul 2>nul
if %ERRORLEVEL% neq 0 (
    if "%ENABLE_DOC%"=="true" (
        echo Pandoc is not installed. Legacy format conversion will be disabled.
        set ENABLE_DOC=false
        set ENABLE_WORDPERFECT=false
    )
)

REM Create required directories
if not exist logs mkdir logs
if not exist uploads mkdir uploads
if not exist output mkdir output
if not exist temp mkdir temp

REM Install dependencies if needed
if not exist node_modules (
    echo Installing dependencies...
    call npm install
)

REM Build TypeScript if needed
if not exist dist (
    echo Building TypeScript...
    call npm run build
)

REM Start the server
echo Starting MCP Server on port %MCP_PORT%...
node dist/mcp-server/index.js