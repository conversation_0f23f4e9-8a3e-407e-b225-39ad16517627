// MCP Server Test Setup
// This file contains setup code for MCP server tests

// Set environment variables for testing
process.env.NODE_ENV = 'test';
process.env.MCP_PORT = '3031';
process.env.LOG_LEVEL = 'error';
process.env.CACHE_ENABLED = 'false';
process.env.API_KEYS = 'test-api-key-1,test-api-key-2';
process.env.JWT_SECRET = 'test-jwt-secret';
process.env.ENABLE_DOC = 'false';
process.env.ENABLE_WORDPERFECT = 'false';

// Global test setup
beforeAll(() => {
  // Setup code that runs before all tests
  console.log('Starting MCP server tests...');
});

// Global test teardown
afterAll(() => {
  // Cleanup code that runs after all tests
  console.log('MCP server tests completed.');
});