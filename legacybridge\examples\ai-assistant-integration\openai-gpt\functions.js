// Function definitions for OpenAI function calling

// Define functions that the model can call
const functionDefinitions = [
  {
    name: 'convert_rtf_to_markdown',
    description: 'Convert RTF content to Markdown format',
    parameters: {
      type: 'object',
      properties: {
        content: {
          type: 'string',
          description: 'RTF content to convert'
        },
        options: {
          type: 'object',
          properties: {
            preserveFormatting: {
              type: 'boolean',
              description: 'Whether to preserve formatting'
            },
            includeMetadata: {
              type: 'boolean',
              description: 'Whether to include metadata in the output'
            }
          }
        }
      },
      required: ['content']
    }
  },
  {
    name: 'convert_markdown_to_rtf',
    description: 'Convert Markdown content to RTF format',
    parameters: {
      type: 'object',
      properties: {
        content: {
          type: 'string',
          description: 'Markdown content to convert'
        },
        options: {
          type: 'object',
          properties: {
            template: {
              type: 'string',
              description: 'Template to use for conversion'
            },
            customStyles: {
              type: 'object',
              description: 'Custom styles to apply'
            }
          }
        }
      },
      required: ['content']
    }
  },
  {
    name: 'detect_format',
    description: 'Detect the format of the provided content (RTF or Markdown)',
    parameters: {
      type: 'object',
      properties: {
        content: {
          type: 'string',
          description: 'Content to analyze'
        }
      },
      required: ['content']
    }
  }
];

// Implement the functions
const implementedFunctions = {
  // Convert RTF to Markdown
  async convert_rtf_to_markdown(args, mcpClient) {
    try {
      const result = await mcpClient.executeFunction('convert_rtf_to_markdown', args);
      return {
        success: true,
        content: result.content,
        metadata: result.metadata
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  },
  
  // Convert Markdown to RTF
  async convert_markdown_to_rtf(args, mcpClient) {
    try {
      const result = await mcpClient.executeFunction('convert_markdown_to_rtf', args);
      return {
        success: true,
        content: result.content,
        metadata: result.metadata
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  },
  
  // Detect format
  async detect_format(args) {
    const content = args.content.trim();
    
    // Check if content looks like RTF
    if (content.startsWith('{\\rtf') || content.includes('\\rtf')) {
      return {
        success: true,
        format: 'rtf',
        confidence: 0.95
      };
    }
    
    // Check if content looks like Markdown
    const markdownIndicators = [
      content.includes('# '),                // Headers
      content.includes('- ') || content.includes('* '),  // Lists
      content.includes('```'),               // Code blocks
      content.includes('**') || content.includes('__'),  // Bold
      content.includes('*') || content.includes('_'),    // Italic
      content.includes('[') && content.includes('](')    // Links
    ];
    
    const markdownScore = markdownIndicators.filter(Boolean).length / markdownIndicators.length;
    
    if (markdownScore > 0.3) {
      return {
        success: true,
        format: 'markdown',
        confidence: markdownScore
      };
    }
    
    // If we can't determine the format
    return {
      success: true,
      format: 'unknown',
      confidence: 0
    };
  }
};

module.exports = {
  functionDefinitions,
  implementedFunctions
};