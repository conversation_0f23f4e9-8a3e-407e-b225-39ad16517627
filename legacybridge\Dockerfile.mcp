# LegacyBridge MCP Server Dockerfile
# Multi-stage build for optimized production image

# Build stage
FROM node:18-slim AS builder

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci

# Copy source code
COPY . .

# Build TypeScript
RUN npm run build

# Production stage
FROM node:18-slim AS production

# Set working directory
WORKDIR /app

# Set build arguments
ARG VERSION=dev
ARG BUILD_DATE=now

# Set environment variables
ENV NODE_ENV=production
ENV VERSION=$VERSION
ENV BUILD_DATE=$BUILD_DATE

# Install LibreOffice and Pandoc for legacy format support
RUN apt-get update && apt-get install -y \
    libreoffice \
    pandoc \
    curl \
    unzip \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Copy package files
COPY package*.json ./

# Install production dependencies only
RUN npm ci --only=production

# Copy built files from builder stage
COPY --from=builder /app/dist ./dist

# Create required directories
RUN mkdir -p logs uploads output temp

# Expose port
EXPOSE 3030

# Set healthcheck
HEALTHCHECK --interval=30s --timeout=10s --start-period=20s --retries=3 \
    CMD curl -f http://localhost:3030/health || exit 1

# Set metadata labels
LABEL org.opencontainers.image.title="LegacyBridge MCP Server" \
      org.opencontainers.image.description="MCP Server for LegacyBridge document conversion" \
      org.opencontainers.image.version=$VERSION \
      org.opencontainers.image.created=$BUILD_DATE \
      org.opencontainers.image.vendor="LegacyBridge" \
      org.opencontainers.image.licenses="MIT"

# Start the server
CMD ["node", "dist/mcp-server/index.js"]