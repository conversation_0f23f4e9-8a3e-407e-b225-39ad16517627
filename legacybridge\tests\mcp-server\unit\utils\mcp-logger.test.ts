// Unit tests for MCPLogger
import { MCPLogger } from '../../../../src/mcp-server/utils/mcp-logger';

// Mock winston
jest.mock('winston', () => {
  const mockFormat = {
    combine: jest.fn(),
    timestamp: jest.fn(),
    json: jest.fn(),
    colorize: jest.fn(),
    printf: jest.fn()
  };
  
  const mockTransports = {
    Console: jest.fn(),
    File: jest.fn()
  };
  
  return {
    format: mockFormat,
    createLogger: jest.fn().mockReturnValue({
      debug: jest.fn(),
      info: jest.fn(),
      warn: jest.fn(),
      error: jest.fn()
    }),
    transports: mockTransports
  };
});

describe('MCPLogger', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });
  
  test('should create logger with default log level', () => {
    const logger = new MCPLogger();
    
    // Verify default log level
    expect(logger).toBeDefined();
    expect(logger.logLevel).toBe('info');
  });
  
  test('should create logger with specified log level', () => {
    const logger = new MCPLogger('debug');
    
    // Verify specified log level
    expect(logger).toBeDefined();
    expect(logger.logLevel).toBe('debug');
  });
  
  test('should log messages at different levels', () => {
    const logger = new MCPLogger('debug');
    
    // Log messages
    logger.debug('Debug message');
    logger.info('Info message');
    logger.warn('Warning message');
    logger.error('Error message');
    
    // Verify logging methods were called
    expect(logger.logger.debug).toHaveBeenCalledWith('Debug message');
    expect(logger.logger.info).toHaveBeenCalledWith('Info message');
    expect(logger.logger.warn).toHaveBeenCalledWith('Warning message');
    expect(logger.logger.error).toHaveBeenCalledWith('Error message');
  });
  
  test('should log objects with context', () => {
    const logger = new MCPLogger('debug');
    const context = { userId: '123', action: 'login' };
    
    // Log message with context
    logger.info('User action', context);
    
    // Verify logging method was called with context
    expect(logger.logger.info).toHaveBeenCalledWith('User action', context);
  });
  
  test('should log errors with stack traces', () => {
    const logger = new MCPLogger('debug');
    const error = new Error('Test error');
    
    // Log error
    logger.error('An error occurred', error);
    
    // Verify logging method was called with error
    expect(logger.logger.error).toHaveBeenCalledWith('An error occurred', error);
  });
});