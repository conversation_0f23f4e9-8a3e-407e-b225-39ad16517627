/* Modern glassmorphism design for monitoring dashboard */

/* Glass panel effect */
.glass-panel {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.15);
  transition: all 0.3s ease;
}

.glass-panel:hover {
  background: rgba(255, 255, 255, 0.08);
  box-shadow: 0 8px 40px 0 rgba(31, 38, 135, 0.2);
}

.dark .glass-panel {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.08);
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.3);
}

.dark .glass-panel:hover {
  background: rgba(0, 0, 0, 0.4);
  box-shadow: 0 8px 40px 0 rgba(0, 0, 0, 0.4);
}

/* Animated gradient backgrounds */
.gradient-bg {
  background: linear-gradient(
    135deg,
    #3b82f6 0%,
    #8b5cf6 25%,
    #ec4899 50%,
    #10b981 75%,
    #3b82f6 100%
  );
  background-size: 400% 400%;
  animation: gradient-shift 15s ease infinite;
}

@keyframes gradient-shift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Mesh gradient background */
.mesh-gradient {
  background-color: #0f172a;
  background-image: 
    radial-gradient(at 20% 80%, #3b82f6 0px, transparent 50%),
    radial-gradient(at 80% 20%, #8b5cf6 0px, transparent 50%),
    radial-gradient(at 40% 40%, #10b981 0px, transparent 50%),
    radial-gradient(at 90% 90%, #ec4899 0px, transparent 50%);
}

/* Status indicators with pulse animation */
.status-indicator {
  position: relative;
  display: inline-block;
  width: 12px;
  height: 12px;
}

.status-indicator::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.status-success {
  background: #10b981;
  box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.4);
}

.status-success::before {
  background: #10b981;
}

.status-warning {
  background: #f59e0b;
  box-shadow: 0 0 0 0 rgba(245, 158, 11, 0.4);
}

.status-warning::before {
  background: #f59e0b;
}

.status-error {
  background: #ef4444;
  box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.4);
}

.status-error::before {
  background: #ef4444;
}

@keyframes pulse {
  0%, 100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.5);
    opacity: 0;
  }
}

/* Glow effects */
.glow-blue {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
}

.glow-green {
  box-shadow: 0 0 20px rgba(16, 185, 129, 0.5);
}

.glow-purple {
  box-shadow: 0 0 20px rgba(139, 92, 246, 0.5);
}

.glow-amber {
  box-shadow: 0 0 20px rgba(245, 158, 11, 0.5);
}

/* Scrollbar styling */
.scrollbar-thin {
  scrollbar-width: thin;
}

.scrollbar-thumb-slate-700::-webkit-scrollbar-thumb {
  background-color: #334155;
  border-radius: 4px;
}

.scrollbar-track-slate-800::-webkit-scrollbar-track {
  background-color: #1e293b;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

/* Neon text effect */
.neon-text {
  text-shadow: 
    0 0 5px currentColor,
    0 0 10px currentColor,
    0 0 20px currentColor,
    0 0 40px currentColor;
}

/* Card hover effects */
.hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

/* Skeleton loading animation */
@keyframes skeleton-loading {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.skeleton {
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0.05) 25%,
    rgba(255, 255, 255, 0.1) 50%,
    rgba(255, 255, 255, 0.05) 75%
  );
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s ease-in-out infinite;
}

/* Grid pattern overlay */
.grid-pattern {
  background-image: 
    linear-gradient(rgba(255, 255, 255, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.03) 1px, transparent 1px);
  background-size: 50px 50px;
}

/* Responsive breakpoints for monitoring dashboard */
@media (max-width: 640px) {
  .glass-panel {
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
  }
}

/* Performance optimizations */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}