apiVersion: v1
kind: Namespace
metadata:
  name: monitoring
  labels:
    app: monitoring
---
# Prometheus Configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
  namespace: monitoring
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
      evaluation_interval: 15s
      external_labels:
        cluster: 'production'
        region: 'us-east-1'
    
    alerting:
      alertmanagers:
        - static_configs:
            - targets:
                - alertmanager:9093
    
    rule_files:
      - '/etc/prometheus/rules/*.yml'
    
    scrape_configs:
      - job_name: 'kubernetes-apiservers'
        kubernetes_sd_configs:
          - role: endpoints
        scheme: https
        tls_config:
          ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
        bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
        relabel_configs:
          - source_labels: [__meta_kubernetes_namespace, __meta_kubernetes_service_name, __meta_kubernetes_endpoint_port_name]
            action: keep
            regex: default;kubernetes;https
      
      - job_name: 'kubernetes-nodes'
        kubernetes_sd_configs:
          - role: node
        scheme: https
        tls_config:
          ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
        bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
        relabel_configs:
          - action: labelmap
            regex: __meta_kubernetes_node_label_(.+)
      
      - job_name: 'kubernetes-pods'
        kubernetes_sd_configs:
          - role: pod
        relabel_configs:
          - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
            action: keep
            regex: true
          - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
            action: replace
            target_label: __metrics_path__
            regex: (.+)
          - source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
            action: replace
            regex: ([^:]+)(?::\d+)?;(\d+)
            replacement: $1:$2
            target_label: __address__
          - action: labelmap
            regex: __meta_kubernetes_pod_label_(.+)
          - source_labels: [__meta_kubernetes_namespace]
            action: replace
            target_label: kubernetes_namespace
          - source_labels: [__meta_kubernetes_pod_name]
            action: replace
            target_label: kubernetes_pod_name
      
      - job_name: 'legacybridge'
        kubernetes_sd_configs:
          - role: pod
            namespaces:
              names:
                - legacybridge-prod
        relabel_configs:
          - source_labels: [__meta_kubernetes_pod_label_app]
            action: keep
            regex: legacybridge
          - source_labels: [__meta_kubernetes_pod_container_port_name]
            action: keep
            regex: metrics
          - source_labels: [__meta_kubernetes_namespace]
            target_label: namespace
          - source_labels: [__meta_kubernetes_pod_name]
            target_label: pod
          - source_labels: [__meta_kubernetes_pod_label_version]
            target_label: version
---
# Alert Rules
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-rules
  namespace: monitoring
data:
  legacybridge-alerts.yml: |
    groups:
      - name: legacybridge
        interval: 30s
        rules:
          - alert: LegacyBridgeDown
            expr: up{job="legacybridge"} == 0
            for: 5m
            labels:
              severity: critical
              team: backend
            annotations:
              summary: "LegacyBridge instance {{ $labels.pod }} is down"
              description: "{{ $labels.pod }} in namespace {{ $labels.namespace }} has been down for more than 5 minutes."
          
          - alert: HighErrorRate
            expr: |
              sum(rate(http_requests_total{job="legacybridge",status=~"5.."}[5m])) by (pod)
              /
              sum(rate(http_requests_total{job="legacybridge"}[5m])) by (pod)
              > 0.05
            for: 5m
            labels:
              severity: warning
              team: backend
            annotations:
              summary: "High error rate on {{ $labels.pod }}"
              description: "Error rate is {{ $value | humanizePercentage }} on {{ $labels.pod }}"
          
          - alert: HighMemoryUsage
            expr: |
              container_memory_usage_bytes{pod=~"legacybridge-.*"}
              / container_spec_memory_limit_bytes{pod=~"legacybridge-.*"}
              > 0.9
            for: 5m
            labels:
              severity: warning
              team: backend
            annotations:
              summary: "High memory usage on {{ $labels.pod }}"
              description: "Memory usage is {{ $value | humanizePercentage }} on {{ $labels.pod }}"
          
          - alert: HighCPUUsage
            expr: |
              rate(container_cpu_usage_seconds_total{pod=~"legacybridge-.*"}[5m])
              > 0.9
            for: 5m
            labels:
              severity: warning
              team: backend
            annotations:
              summary: "High CPU usage on {{ $labels.pod }}"
              description: "CPU usage is {{ $value | humanizePercentage }} on {{ $labels.pod }}"
          
          - alert: LowPodCount
            expr: |
              count(up{job="legacybridge"} == 1) < 2
            for: 5m
            labels:
              severity: warning
              team: backend
            annotations:
              summary: "Low number of LegacyBridge pods"
              description: "Only {{ $value }} LegacyBridge pods are running"
          
          - alert: SlowResponseTime
            expr: |
              histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket{job="legacybridge"}[5m])) by (le, pod))
              > 2
            for: 5m
            labels:
              severity: warning
              team: backend
            annotations:
              summary: "Slow response time on {{ $labels.pod }}"
              description: "95th percentile response time is {{ $value }}s on {{ $labels.pod }}"
          
          - alert: HighQueueDepth
            expr: |
              legacybridge_queue_depth > 100
            for: 5m
            labels:
              severity: warning
              team: backend
            annotations:
              summary: "High queue depth"
              description: "Queue depth is {{ $value }} items"
          
          - alert: DiskSpaceRunningOut
            expr: |
              (node_filesystem_avail_bytes{mountpoint="/"} / node_filesystem_size_bytes{mountpoint="/"}) < 0.1
            for: 5m
            labels:
              severity: critical
              team: infrastructure
            annotations:
              summary: "Disk space running out on {{ $labels.instance }}"
              description: "Only {{ $value | humanizePercentage }} disk space remaining"
---
# Prometheus Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: prometheus
  namespace: monitoring
spec:
  replicas: 2
  selector:
    matchLabels:
      app: prometheus
  template:
    metadata:
      labels:
        app: prometheus
    spec:
      serviceAccountName: prometheus
      securityContext:
        runAsNonRoot: true
        runAsUser: 65534
        fsGroup: 65534
      containers:
        - name: prometheus
          image: prom/prometheus:v2.47.0
          args:
            - '--config.file=/etc/prometheus/prometheus.yml'
            - '--storage.tsdb.path=/prometheus'
            - '--storage.tsdb.retention.time=30d'
            - '--storage.tsdb.retention.size=50GB'
            - '--web.enable-lifecycle'
            - '--web.enable-admin-api'
          ports:
            - containerPort: 9090
              name: http
          resources:
            requests:
              cpu: 500m
              memory: 2Gi
            limits:
              cpu: 2000m
              memory: 4Gi
          volumeMounts:
            - name: config
              mountPath: /etc/prometheus
            - name: rules
              mountPath: /etc/prometheus/rules
            - name: storage
              mountPath: /prometheus
          livenessProbe:
            httpGet:
              path: /-/healthy
              port: http
            initialDelaySeconds: 30
            timeoutSeconds: 30
          readinessProbe:
            httpGet:
              path: /-/ready
              port: http
            initialDelaySeconds: 5
            timeoutSeconds: 30
      volumes:
        - name: config
          configMap:
            name: prometheus-config
        - name: rules
          configMap:
            name: prometheus-rules
        - name: storage
          persistentVolumeClaim:
            claimName: prometheus-storage
---
# Prometheus Service
apiVersion: v1
kind: Service
metadata:
  name: prometheus
  namespace: monitoring
spec:
  selector:
    app: prometheus
  ports:
    - port: 9090
      targetPort: 9090
      name: http
---
# Prometheus Storage
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: prometheus-storage
  namespace: monitoring
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 100Gi
  storageClassName: fast-ssd
---
# Grafana Configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-config
  namespace: monitoring
data:
  grafana.ini: |
    [analytics]
    reporting_enabled = false
    check_for_updates = false
    
    [auth]
    disable_login_form = false
    disable_signout_menu = false
    
    [auth.anonymous]
    enabled = true
    org_role = Viewer
    
    [dashboards]
    default_home_dashboard_path = /var/lib/grafana/dashboards/legacybridge-overview.json
    
    [security]
    admin_user = admin
    admin_password = admin
    
    [server]
    root_url = https://grafana.legacybridge.io
  
  datasources.yaml: |
    apiVersion: 1
    datasources:
      - name: Prometheus
        type: prometheus
        access: proxy
        url: http://prometheus:9090
        isDefault: true
        editable: false
---
# Grafana Dashboards
apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-dashboards
  namespace: monitoring
data:
  legacybridge-overview.json: |
    {
      "dashboard": {
        "title": "LegacyBridge Overview",
        "panels": [
          {
            "title": "Request Rate",
            "targets": [
              {
                "expr": "sum(rate(http_requests_total{job=\"legacybridge\"}[5m])) by (pod)"
              }
            ]
          },
          {
            "title": "Error Rate",
            "targets": [
              {
                "expr": "sum(rate(http_requests_total{job=\"legacybridge\",status=~\"5..\"}[5m])) by (pod)"
              }
            ]
          },
          {
            "title": "Response Time (p95)",
            "targets": [
              {
                "expr": "histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket{job=\"legacybridge\"}[5m])) by (le, pod))"
              }
            ]
          },
          {
            "title": "CPU Usage",
            "targets": [
              {
                "expr": "rate(container_cpu_usage_seconds_total{pod=~\"legacybridge-.*\"}[5m])"
              }
            ]
          },
          {
            "title": "Memory Usage",
            "targets": [
              {
                "expr": "container_memory_usage_bytes{pod=~\"legacybridge-.*\"}"
              }
            ]
          },
          {
            "title": "Active Pods",
            "targets": [
              {
                "expr": "count(up{job=\"legacybridge\"} == 1)"
              }
            ]
          }
        ]
      }
    }
---
# Grafana Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: grafana
  namespace: monitoring
spec:
  replicas: 2
  selector:
    matchLabels:
      app: grafana
  template:
    metadata:
      labels:
        app: grafana
    spec:
      securityContext:
        runAsNonRoot: true
        runAsUser: 472
        fsGroup: 472
      containers:
        - name: grafana
          image: grafana/grafana:10.1.0
          ports:
            - containerPort: 3000
              name: http
          env:
            - name: GF_INSTALL_PLUGINS
              value: "grafana-piechart-panel,grafana-worldmap-panel"
          resources:
            requests:
              cpu: 100m
              memory: 256Mi
            limits:
              cpu: 500m
              memory: 512Mi
          volumeMounts:
            - name: config
              mountPath: /etc/grafana
            - name: datasources
              mountPath: /etc/grafana/provisioning/datasources
            - name: dashboards
              mountPath: /var/lib/grafana/dashboards
            - name: storage
              mountPath: /var/lib/grafana
          livenessProbe:
            httpGet:
              path: /api/health
              port: http
            initialDelaySeconds: 30
          readinessProbe:
            httpGet:
              path: /api/health
              port: http
            initialDelaySeconds: 5
      volumes:
        - name: config
          configMap:
            name: grafana-config
        - name: datasources
          configMap:
            name: grafana-config
            items:
              - key: datasources.yaml
                path: datasources.yaml
        - name: dashboards
          configMap:
            name: grafana-dashboards
        - name: storage
          persistentVolumeClaim:
            claimName: grafana-storage
---
# Grafana Service
apiVersion: v1
kind: Service
metadata:
  name: grafana
  namespace: monitoring
spec:
  selector:
    app: grafana
  ports:
    - port: 3000
      targetPort: 3000
      name: http
---
# Grafana Storage
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: grafana-storage
  namespace: monitoring
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: standard
---
# AlertManager Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: alertmanager
  namespace: monitoring
spec:
  replicas: 1
  selector:
    matchLabels:
      app: alertmanager
  template:
    metadata:
      labels:
        app: alertmanager
    spec:
      securityContext:
        runAsNonRoot: true
        runAsUser: 65534
        fsGroup: 65534
      containers:
        - name: alertmanager
          image: prom/alertmanager:v0.26.0
          args:
            - '--config.file=/etc/alertmanager/alertmanager.yml'
            - '--storage.path=/alertmanager'
            - '--cluster.advertise-address=$(POD_IP):9094'
          env:
            - name: POD_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.podIP
          ports:
            - containerPort: 9093
              name: http
            - containerPort: 9094
              name: cluster
          resources:
            requests:
              cpu: 100m
              memory: 128Mi
            limits:
              cpu: 500m
              memory: 256Mi
          volumeMounts:
            - name: config
              mountPath: /etc/alertmanager
            - name: storage
              mountPath: /alertmanager
      volumes:
        - name: config
          configMap:
            name: alertmanager-config
        - name: storage
          emptyDir: {}
---
# AlertManager Config
apiVersion: v1
kind: ConfigMap
metadata:
  name: alertmanager-config
  namespace: monitoring
data:
  alertmanager.yml: |
    global:
      resolve_timeout: 5m
      slack_api_url: '$SLACK_WEBHOOK_URL'
    
    route:
      group_by: ['alertname', 'cluster', 'service']
      group_wait: 10s
      group_interval: 10s
      repeat_interval: 12h
      receiver: 'default'
      routes:
        - match:
            severity: critical
          receiver: pagerduty
        - match:
            severity: warning
          receiver: slack
    
    receivers:
      - name: 'default'
        slack_configs:
          - channel: '#alerts'
            title: 'LegacyBridge Alert'
            text: '{{ range .Alerts }}{{ .Annotations.summary }}\n{{ .Annotations.description }}{{ end }}'
      
      - name: 'slack'
        slack_configs:
          - channel: '#alerts-warning'
            title: 'Warning: {{ .GroupLabels.alertname }}'
            text: '{{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'
      
      - name: 'pagerduty'
        pagerduty_configs:
          - service_key: '$PAGERDUTY_SERVICE_KEY'
            description: '{{ .GroupLabels.alertname }}: {{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'
---
# AlertManager Service
apiVersion: v1
kind: Service
metadata:
  name: alertmanager
  namespace: monitoring
spec:
  selector:
    app: alertmanager
  ports:
    - port: 9093
      targetPort: 9093
      name: http
---
# ServiceAccount for Prometheus
apiVersion: v1
kind: ServiceAccount
metadata:
  name: prometheus
  namespace: monitoring
---
# ClusterRole for Prometheus
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: prometheus
rules:
  - apiGroups: [""]
    resources:
      - nodes
      - nodes/proxy
      - services
      - endpoints
      - pods
    verbs: ["get", "list", "watch"]
  - apiGroups:
      - extensions
    resources:
      - ingresses
    verbs: ["get", "list", "watch"]
  - nonResourceURLs: ["/metrics"]
    verbs: ["get"]
---
# ClusterRoleBinding for Prometheus
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: prometheus
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: prometheus
subjects:
  - kind: ServiceAccount
    name: prometheus
    namespace: monitoring