{"name": "legacybridge-openai-integration", "version": "1.0.0", "description": "Example of integrating LegacyBridge MCP server with OpenAI GPT", "main": "index.js", "scripts": {"start": "node index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["legacybridge", "openai", "gpt", "mcp", "document", "conversion"], "author": "LegacyBridge Team", "license": "MIT", "dependencies": {"axios": "^1.6.0", "dotenv": "^16.3.1", "form-data": "^4.0.0"}}