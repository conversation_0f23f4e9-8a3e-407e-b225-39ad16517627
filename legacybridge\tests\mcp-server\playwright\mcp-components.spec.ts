// Playwright Tests for MCP Server Components
// Tests individual MCP server components without requiring full server startup

import { test, expect } from '@playwright/test';
import { getConfig } from '../../../src/mcp-server/utils/mcp-config';
import { MCPLogger } from '../../../src/mcp-server/utils/mcp-logger';
import { MCPCache } from '../../../src/mcp-server/services/mcp-cache';
import { MCPMetricsCollector } from '../../../src/mcp-server/utils/mcp-metrics';

test.describe('MCP Server Components', () => {
  
  test('MCPConfig should load with default values', async () => {
    const config = getConfig();
    
    expect(config.port).toBe(3030);
    expect(config.environment).toBe('development');
    expect(config.logLevel).toBe('info');
    expect(config.cache.enabled).toBe(true);
    expect(config.cache.type).toBe('memory');
    expect(config.legacyFormats.enableDOC).toBe(false);
    expect(config.legacyFormats.enableWordPerfect).toBe(false);
  });

  test('MCPLogger should create log entries', async () => {
    const logger = new MCPLogger('error'); // Use error level to reduce noise
    
    // Test that logger methods exist and can be called
    expect(() => {
      logger.info('Test info message');
      logger.warn('Test warning message');
      logger.error('Test error message');
      logger.debug('Test debug message');
    }).not.toThrow();
  });

  test('MCPCache should handle memory caching', async () => {
    const cacheConfig = {
      enabled: true,
      type: 'memory' as const,
      ttl: 3600,
      maxSize: 100
    };

    const cache = new MCPCache(cacheConfig);

    // Test cache operations using correct API: set(type, content, result, options)
    await cache.set('rtf_to_md', 'test content', 'test-value');
    const value = await cache.get('rtf_to_md', 'test content');
    expect(value).toBe('test-value');

    // Test cache deletion
    await cache.delete('rtf_to_md', 'test content');
    const deletedValue = await cache.get('rtf_to_md', 'test content');
    expect(deletedValue).toBeNull();
  });

  test('MCPCache should handle disabled state', async () => {
    const cacheConfig = {
      enabled: false,
      type: 'memory' as const,
      ttl: 3600,
      maxSize: 100
    };
    
    const cache = new MCPCache(cacheConfig);
    
    // When disabled, cache should not store values
    await cache.set('test-key', 'test-value');
    const value = await cache.get('test-key');
    expect(value).toBeNull();
  });

  test('MCPMetricsCollector should track metrics', async () => {
    const metrics = new MCPMetricsCollector();
    
    // Record some test metrics
    metrics.recordRequest({
      method: 'POST',
      path: '/api/convert',
      statusCode: 200,
      duration: 150,
      timestamp: new Date()
    });
    
    metrics.recordConversion({
      format: 'rtf',
      success: true,
      duration: 100,
      fileSize: 1024,
      timestamp: new Date()
    });
    
    // Get metrics summary
    const summary = metrics.getMetrics();

    expect(summary.requests.total).toBe(1);
    expect(summary.requests.avgDuration).toBe(150);
    expect(summary.conversions.total).toBe(1);
    expect(summary.conversions.successRate).toBe(1); // 1.0 = 100%
  });

  test('MCPMetricsCollector should calculate success rates', async () => {
    const metrics = new MCPMetricsCollector();
    
    // Record successful and failed conversions
    metrics.recordConversion({
      format: 'rtf',
      success: true,
      duration: 100,
      fileSize: 1024,
      timestamp: new Date()
    });
    
    metrics.recordConversion({
      format: 'rtf',
      success: false,
      duration: 50,
      fileSize: 512,
      timestamp: new Date()
    });
    
    const summary = metrics.getMetrics();

    expect(summary.conversions.total).toBe(2);
    expect(summary.conversions.successRate).toBe(0.5); // 0.5 = 50%
  });

  test('MCPConfig should validate configuration', async () => {
    const config = getConfig();
    
    // Test that required properties exist
    expect(config.port).toBeGreaterThan(0);
    expect(config.port).toBeLessThan(65536);
    expect(['development', 'production', 'test']).toContain(config.environment);
    expect(['debug', 'info', 'warn', 'error']).toContain(config.logLevel);
  });

  test('MCPCache should handle cache key generation', async () => {
    const cacheConfig = {
      enabled: true,
      type: 'memory' as const,
      ttl: 3600,
      maxSize: 100
    };

    const cache = new MCPCache(cacheConfig);

    // Test with different content and options
    const content1 = 'test content 1';
    const content2 = 'test content 2';
    const options = { preserveFormatting: true };

    // Use correct API: set(type, content, result, options)
    await cache.set('rtf_to_md', content1, 'converted content 1', options);
    await cache.set('rtf_to_md', content2, 'converted content 2', options);

    // Should be able to retrieve the specific cached value
    const value = await cache.get('rtf_to_md', content2, options);
    expect(value).toBe('converted content 2');
  });

  test('MCPLogger should handle different log levels', async () => {
    // Test different log levels
    const debugLogger = new MCPLogger('debug');
    const infoLogger = new MCPLogger('info');
    const warnLogger = new MCPLogger('warn');
    const errorLogger = new MCPLogger('error');
    
    // All should be created without errors
    expect(debugLogger).toBeDefined();
    expect(infoLogger).toBeDefined();
    expect(warnLogger).toBeDefined();
    expect(errorLogger).toBeDefined();
  });

  test('MCPMetricsCollector should handle batch metrics', async () => {
    const metrics = new MCPMetricsCollector();
    
    // Record a batch job
    metrics.recordBatch({
      batchId: 'test-batch-1',
      fileCount: 5,
      totalSize: 5120,
      duration: 2000,
      successCount: 4,
      failureCount: 1,
      timestamp: new Date()
    });
    
    const summary = metrics.getMetrics();

    expect(summary.batches.total).toBe(1);
    expect(summary.batches.avgSize).toBe(5);
    expect(summary.batches.avgSuccessRate).toBe(0.8); // 0.8 = 80%
  });

  test('MCPConfig should handle environment variables', async () => {
    // Save original env
    const originalEnv = process.env.MCP_PORT;
    
    try {
      // Set test environment variable
      process.env.MCP_PORT = '4000';
      
      const config = getConfig();
      expect(config.port).toBe(4000);
      
    } finally {
      // Restore original env
      if (originalEnv) {
        process.env.MCP_PORT = originalEnv;
      } else {
        delete process.env.MCP_PORT;
      }
    }
  });

});
