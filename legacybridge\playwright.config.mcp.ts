// Playwright Configuration for MCP Server Testing
import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
  testDir: './tests/mcp-server/playwright',
  
  // Run tests in files in parallel
  fullyParallel: true,
  
  // Fail the build on CI if you accidentally left test.only in the source code
  forbidOnly: !!process.env.CI,
  
  // Retry on CI only
  retries: process.env.CI ? 2 : 0,
  
  // Opt out of parallel tests on CI
  workers: process.env.CI ? 1 : undefined,
  
  // Reporter to use
  reporter: [
    ['html', { outputFolder: 'playwright-report-mcp' }],
    ['json', { outputFile: 'test-results-mcp.json' }]
  ],
  
  // Shared settings for all the projects below
  use: {
    // Base URL for API testing
    baseURL: 'http://localhost:3032',
    
    // Collect trace when retrying the failed test
    trace: 'on-first-retry',
    
    // API testing specific settings
    extraHTTPHeaders: {
      'Accept': 'application/json',
      'Content-Type': 'application/json'
    }
  },

  // Configure projects for major browsers (for any UI testing if needed)
  projects: [
    {
      name: 'api-tests',
      testMatch: '**/*.spec.ts',
      use: {
        // API testing doesn't need a browser
        ...devices['Desktop Chrome']
      }
    }
  ],

  // Global setup and teardown (disabled for component testing)
  // globalSetup: require.resolve('./tests/mcp-server/playwright/global-setup.ts'),
  // globalTeardown: require.resolve('./tests/mcp-server/playwright/global-teardown.ts'),
  
  // Test timeout
  timeout: 30000,
  
  // Expect timeout
  expect: {
    timeout: 5000
  }
});
