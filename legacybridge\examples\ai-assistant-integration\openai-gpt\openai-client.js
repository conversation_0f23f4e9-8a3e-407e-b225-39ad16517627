// OpenAI Client for interacting with the OpenAI API

const axios = require('axios');

class OpenAIClient {
  constructor(apiKey) {
    this.apiKey = apiKey;
    
    // Create axios instance with default config
    this.client = axios.create({
      baseURL: 'https://api.openai.com/v1',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`
      }
    });
  }
  
  // Create a chat completion
  async createChatCompletion(options) {
    try {
      const response = await this.client.post('/chat/completions', {
        model: options.model || 'gpt-4',
        messages: options.messages,
        functions: options.functions,
        function_call: options.function_call,
        temperature: options.temperature || 0.7,
        max_tokens: options.max_tokens,
        top_p: options.top_p || 1,
        frequency_penalty: options.frequency_penalty || 0,
        presence_penalty: options.presence_penalty || 0
      });
      
      return response.data;
    } catch (error) {
      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        throw new Error(`OpenAI API error: ${error.response.data.error.message || error.response.status}`);
      } else if (error.request) {
        // The request was made but no response was received
        throw new Error('No response from OpenAI API');
      } else {
        // Something happened in setting up the request that triggered an Error
        throw new Error(`Error: ${error.message}`);
      }
    }
  }
  
  // Create a streaming chat completion
  async createStreamingChatCompletion(options, onChunk) {
    try {
      const response = await this.client.post('/chat/completions', {
        model: options.model || 'gpt-4',
        messages: options.messages,
        functions: options.functions,
        function_call: options.function_call,
        temperature: options.temperature || 0.7,
        max_tokens: options.max_tokens,
        top_p: options.top_p || 1,
        frequency_penalty: options.frequency_penalty || 0,
        presence_penalty: options.presence_penalty || 0,
        stream: true
      }, {
        responseType: 'stream'
      });
      
      response.data.on('data', (chunk) => {
        // Parse the chunk
        const lines = chunk.toString().split('\n').filter(line => line.trim() !== '');
        for (const line of lines) {
          if (line.includes('[DONE]')) {
            return;
          }
          
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.substring(6));
              onChunk(data);
            } catch (error) {
              console.error('Error parsing chunk:', error);
            }
          }
        }
      });
      
      return new Promise((resolve, reject) => {
        response.data.on('end', resolve);
        response.data.on('error', reject);
      });
    } catch (error) {
      if (error.response) {
        throw new Error(`OpenAI API error: ${error.response.data.error.message || error.response.status}`);
      } else if (error.request) {
        throw new Error('No response from OpenAI API');
      } else {
        throw new Error(`Error: ${error.message}`);
      }
    }
  }
}

module.exports = { OpenAIClient };