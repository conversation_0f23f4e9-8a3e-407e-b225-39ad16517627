# High Availability Kubernetes Deployment for LegacyBridge Enterprise
# Supports 1000+ concurrent users with auto-scaling and fault tolerance

---
# Namespace
apiVersion: v1
kind: Namespace
metadata:
  name: legacybridge
  labels:
    app: legacybridge
    environment: production

---
# ConfigMap for application configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: legacybridge-config
  namespace: legacybridge
data:
  NODE_ENV: "production"
  APP_NAME: "LegacyBridge Enterprise"
  LOG_LEVEL: "info"
  ENABLE_METRICS: "true"
  ENABLE_TRACING: "true"
  SESSION_TIMEOUT: "86400"
  MAX_FILE_SIZE_MB: "1000"
  CONCURRENT_CONVERSIONS: "50"

---
# Secret for sensitive data
apiVersion: v1
kind: Secret
metadata:
  name: legacybridge-secrets
  namespace: legacybridge
type: Opaque
stringData:
  DATABASE_URL: "************************************************/legacybridge"
  REDIS_URL: "redis://redis-cluster:6379"
  JWT_SECRET: "your-super-secret-jwt-key"
  ENCRYPTION_KEY: "your-encryption-key"
  AWS_ACCESS_KEY_ID: "your-aws-access-key"
  AWS_SECRET_ACCESS_KEY: "your-aws-secret-key"

---
# Application Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: legacybridge-app
  namespace: legacybridge
  labels:
    app: legacybridge
    component: backend
spec:
  replicas: 3
  selector:
    matchLabels:
      app: legacybridge
      component: backend
  template:
    metadata:
      labels:
        app: legacybridge
        component: backend
    spec:
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - legacybridge
              topologyKey: kubernetes.io/hostname
      containers:
      - name: legacybridge
        image: legacybridge/enterprise:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 3000
          name: http
        - containerPort: 9090
          name: metrics
        env:
        - name: POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        envFrom:
        - configMapRef:
            name: legacybridge-config
        - secretRef:
            name: legacybridge-secrets
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "2000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        volumeMounts:
        - name: temp-storage
          mountPath: /tmp
        - name: file-storage
          mountPath: /app/storage
      volumes:
      - name: temp-storage
        emptyDir: {}
      - name: file-storage
        persistentVolumeClaim:
          claimName: legacybridge-storage

---
# Service
apiVersion: v1
kind: Service
metadata:
  name: legacybridge-service
  namespace: legacybridge
  labels:
    app: legacybridge
spec:
  selector:
    app: legacybridge
    component: backend
  ports:
  - name: http
    port: 80
    targetPort: 3000
  - name: metrics
    port: 9090
    targetPort: 9090
  type: ClusterIP

---
# Horizontal Pod Autoscaler
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: legacybridge-hpa
  namespace: legacybridge
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: legacybridge-app
  minReplicas: 3
  maxReplicas: 50
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  - type: Pods
    pods:
      metric:
        name: pending_conversions
      target:
        type: AverageValue
        averageValue: "30"
  behavior:
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 100
        periodSeconds: 15
      - type: Pods
        value: 4
        periodSeconds: 15
      selectPolicy: Max
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60

---
# Pod Disruption Budget
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: legacybridge-pdb
  namespace: legacybridge
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: legacybridge
      component: backend

---
# Ingress with SSL
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: legacybridge-ingress
  namespace: legacybridge
  annotations:
    kubernetes.io/ingress.class: "nginx"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/proxy-body-size: "1000m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
spec:
  tls:
  - hosts:
    - legacybridge.com
    - "*.legacybridge.com"
    secretName: legacybridge-tls
  rules:
  - host: legacybridge.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: legacybridge-service
            port:
              number: 80
  - host: "*.legacybridge.com"
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: legacybridge-service
            port:
              number: 80

---
# Redis Deployment for Sessions
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis-cluster
  namespace: legacybridge
spec:
  replicas: 3
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
      - name: redis
        image: redis:7-alpine
        ports:
        - containerPort: 6379
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        volumeMounts:
        - name: redis-data
          mountPath: /data
      volumes:
      - name: redis-data
        emptyDir: {}

---
# Redis Service
apiVersion: v1
kind: Service
metadata:
  name: redis-cluster
  namespace: legacybridge
spec:
  selector:
    app: redis
  ports:
  - port: 6379
    targetPort: 6379
  type: ClusterIP

---
# PostgreSQL StatefulSet for Database
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: postgres-cluster
  namespace: legacybridge
spec:
  serviceName: postgres-service
  replicas: 3
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        app: postgres
    spec:
      containers:
      - name: postgres
        image: postgres:15-alpine
        ports:
        - containerPort: 5432
        env:
        - name: POSTGRES_DB
          value: legacybridge
        - name: POSTGRES_USER
          valueFrom:
            secretKeyRef:
              name: postgres-secrets
              key: username
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgres-secrets
              key: password
        - name: POSTGRES_REPLICATION_MODE
          value: master
        - name: POSTGRES_REPLICATION_USER
          value: replicator
        - name: POSTGRES_REPLICATION_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgres-secrets
              key: replication-password
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
  volumeClaimTemplates:
  - metadata:
      name: postgres-storage
    spec:
      accessModes: ["ReadWriteOnce"]
      storageClassName: fast-ssd
      resources:
        requests:
          storage: 100Gi

---
# PostgreSQL Service
apiVersion: v1
kind: Service
metadata:
  name: postgres-cluster
  namespace: legacybridge
spec:
  selector:
    app: postgres
  ports:
  - port: 5432
    targetPort: 5432
  type: ClusterIP

---
# Storage Class for Fast SSD
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: fast-ssd
provisioner: kubernetes.io/aws-ebs
parameters:
  type: gp3
  iops: "10000"
  throughput: "250"
allowVolumeExpansion: true
volumeBindingMode: WaitForFirstConsumer

---
# Persistent Volume Claim for File Storage
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: legacybridge-storage
  namespace: legacybridge
spec:
  accessModes:
    - ReadWriteMany
  storageClassName: efs-storage
  resources:
    requests:
      storage: 1Ti

---
# Storage Class for EFS (Elastic File System)
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: efs-storage
provisioner: efs.csi.aws.com
parameters:
  provisioningMode: efs-ap
  fileSystemId: fs-xxxxxxxx
  directoryPerms: "700"

---
# Network Policy for Security
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: legacybridge-network-policy
  namespace: legacybridge
spec:
  podSelector:
    matchLabels:
      app: legacybridge
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    - podSelector:
        matchLabels:
          app: legacybridge
    ports:
    - protocol: TCP
      port: 3000
  egress:
  - to:
    - podSelector:
        matchLabels:
          app: postgres
    - podSelector:
        matchLabels:
          app: redis
    ports:
    - protocol: TCP
      port: 5432
    - protocol: TCP
      port: 6379
  - to:
    - namespaceSelector: {}
    ports:
    - protocol: TCP
      port: 443
    - protocol: TCP
      port: 80

---
# Service Monitor for Prometheus
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: legacybridge-metrics
  namespace: legacybridge
spec:
  selector:
    matchLabels:
      app: legacybridge
  endpoints:
  - port: metrics
    interval: 30s
    path: /metrics

---
# Vertical Pod Autoscaler
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: legacybridge-vpa
  namespace: legacybridge
spec:
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: legacybridge-app
  updatePolicy:
    updateMode: "Auto"
  resourcePolicy:
    containerPolicies:
    - containerName: legacybridge
      minAllowed:
        cpu: 250m
        memory: 256Mi
      maxAllowed:
        cpu: 4000m
        memory: 8Gi