// MCP Conversion Service
// Handles document conversions with caching and error handling

import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { MCPConfig } from '../utils/mcp-config';
import { MCPLogger } from '../utils/mcp-logger';
import { MCPCache } from '../services/mcp-cache';
import { ConversionError, ValidationError } from '../middleware/mcp-error-handler';

export interface ConversionOptions {
  preserveFormatting?: boolean;
  includeMetadata?: boolean;
  customStyles?: Record<string, any>;
  template?: string;
}

export interface ValidationResult {
  valid: boolean;
  errors: any[];
  warnings: any[];
}

export class MCPConversionService {
  constructor(
    private config: MCPConfig,
    private cache: MCPCache,
    private logger: MCPLogger
  ) {}

  // Convert RTF to Markdown
  async convertRtfToMarkdown(content: string, options?: ConversionOptions): Promise<any> {
    this.logger.info('Converting RTF to Markdown', { contentLength: content.length });
    
    try {
      // Check cache first
      const cachedResult = await this.cache.get('rtf_to_md', content, options);
      if (cachedResult) {
        this.logger.info('Cache hit for RTF to Markdown conversion');
        return cachedResult;
      }
      
      // Validate RTF content
      if (!this.isValidRtf(content)) {
        throw new ValidationError('Invalid RTF content');
      }
      
      // Perform basic RTF to Markdown conversion
      const convertedContent = this.convertRtfToMarkdownBasic(content);

      // Create response
      const response = {
        content: convertedContent,
        metadata: {
          convertedAt: new Date().toISOString()
        }
      };

      // Cache the result
      await this.cache.set('rtf_to_md', content, response, options);

      return response;
    } catch (error) {
      this.logger.error('RTF to Markdown conversion failed', error);
      
      if (error instanceof ValidationError || error instanceof ConversionError) {
        throw error;
      }
      
      throw new ConversionError('Failed to convert RTF to Markdown', {
        originalError: error instanceof Error ? error.message : String(error)
      });
    }
  }

  // Convert Markdown to RTF
  async convertMarkdownToRtf(content: string, options?: ConversionOptions): Promise<any> {
    this.logger.info('Converting Markdown to RTF', { contentLength: content.length });
    
    try {
      // Check cache first
      const cachedResult = await this.cache.get('md_to_rtf', content, options);
      if (cachedResult) {
        this.logger.info('Cache hit for Markdown to RTF conversion');
        return cachedResult;
      }
      
      // Apply template if specified
      let processedContent = content;
      if (options?.template) {
        processedContent = await this.applyTemplate(content, options.template);
      }
      
      // Perform basic Markdown to RTF conversion
      const convertedContent = this.convertMarkdownToRtfBasic(processedContent);

      // Create response
      const response = {
        content: convertedContent,
        metadata: {
          convertedAt: new Date().toISOString(),
          template: options?.template
        }
      };

      // Cache the result
      await this.cache.set('md_to_rtf', content, response, options);

      return response;
    } catch (error) {
      this.logger.error('Markdown to RTF conversion failed', error);
      
      if (error instanceof ValidationError || error instanceof ConversionError) {
        throw error;
      }
      
      throw new ConversionError('Failed to convert Markdown to RTF', {
        originalError: error instanceof Error ? error.message : String(error)
      });
    }
  }

  // Convert RTF file to Markdown
  async convertRtfFileToMarkdown(filePath: string, options?: ConversionOptions): Promise<any> {
    this.logger.info('Converting RTF file to Markdown', { filePath });

    try {
      // Read file content
      let content: string;
      try {
        content = await fs.promises.readFile(filePath, 'utf8');
      } catch (readError) {
        throw new ConversionError('Failed to read RTF file', {
          filePath,
          originalError: readError instanceof Error ? readError.message : String(readError)
        });
      }

      // Convert content
      const result = await this.convertRtfToMarkdown(content, options);

      // Generate output file path
      const outputFileName = path.basename(filePath, '.rtf') + '.md';
      const outputDir = path.join(process.cwd(), 'output');

      // Create output directory if it doesn't exist
      if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
      }

      const outputPath = path.join(outputDir, outputFileName);

      // Write output file
      await fs.promises.writeFile(outputPath, result.content, 'utf8');

      return {
        ...result,
        outputPath,
        outputFileName
      };
    } catch (error) {
      this.logger.error('RTF file conversion failed', error);

      if (error instanceof ValidationError || error instanceof ConversionError) {
        throw error;
      }

      throw new ConversionError('Failed to convert RTF file', {
        filePath,
        originalError: error instanceof Error ? error.message : String(error)
      });
    }
  }

  // Convert Markdown file to RTF
  async convertMarkdownFileToRtf(filePath: string, options?: ConversionOptions): Promise<any> {
    this.logger.info('Converting Markdown file to RTF', { filePath });
    
    try {
      // Read file content
      let content: string;
      try {
        content = await fs.promises.readFile(filePath, 'utf8');
      } catch (readError) {
        throw new ConversionError('Failed to read Markdown file', {
          filePath,
          originalError: readError instanceof Error ? readError.message : String(readError)
        });
      }

      // Convert content
      const result = await this.convertMarkdownToRtf(content, options);
      
      // Generate output file path
      const outputFileName = path.basename(filePath, path.extname(filePath)) + '.rtf';
      const outputDir = path.join(process.cwd(), 'output');
      
      // Create output directory if it doesn't exist
      if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
      }
      
      const outputPath = path.join(outputDir, outputFileName);
      
      // Write output file
      await fs.promises.writeFile(outputPath, result.content, 'utf8');
      
      return {
        ...result,
        outputPath,
        outputFileName
      };
    } catch (error) {
      this.logger.error('Markdown file conversion failed', error);
      
      if (error instanceof ValidationError || error instanceof ConversionError) {
        throw error;
      }
      
      throw new ConversionError('Failed to convert Markdown file', {
        filePath,
        originalError: error instanceof Error ? error.message : String(error)
      });
    }
  }

  // Validate document
  async validateDocument(content: string, format: string): Promise<ValidationResult> {
    this.logger.info('Validating document', { format, contentLength: content.length });

    try {
      // Check for supported formats
      const supportedFormats = ['rtf', 'markdown'];
      if (!supportedFormats.includes(format.toLowerCase())) {
        throw new ValidationError(`Unsupported format: ${format}`, {
          supportedFormats,
          receivedFormat: format
        });
      }

      // Perform basic document validation
      const validationResult = this.validateDocumentBasic(content, format);

      return validationResult;
    } catch (error) {
      this.logger.error('Document validation failed', error);
      
      if (error instanceof ValidationError) {
        throw error;
      }
      
      throw new ValidationError('Failed to validate document', {
        originalError: error instanceof Error ? error.message : String(error)
      });
    }
  }

  // List available templates
  async listTemplates(): Promise<string[]> {
    try {
      // In a real implementation, this would call the Rust backend
      // For now, return a static list
      return [
        'default',
        'minimal',
        'professional',
        'academic',
        'letter',
        'report'
      ];
    } catch (error) {
      this.logger.error('Failed to list templates', error);
      throw new Error('Failed to list templates');
    }
  }

  // Apply template to Markdown content
  private async applyTemplate(content: string, templateName: string): Promise<string> {
    // In a real implementation, this would apply the template
    // For now, just return the original content
    return content;
  }

  // Basic document validation (simplified for MCP server)
  private validateDocumentBasic(content: string, format: string): ValidationResult {
    const errors: any[] = [];
    const warnings: any[] = [];

    if (!content || content.trim().length === 0) {
      errors.push({
        field: 'content',
        expected: 'non-empty string',
        received: 'empty or null',
        line: 1,
        column: 1
      });
    }

    if (format === 'rtf') {
      if (!this.isValidRtf(content)) {
        errors.push({
          field: 'format',
          expected: 'valid RTF document starting with {\\rtf',
          received: 'invalid RTF format',
          line: 1,
          column: 1
        });
      }
    } else if (format === 'markdown') {
      // Basic markdown validation
      if (content.includes('<script>')) {
        warnings.push({
          field: 'content',
          expected: 'safe markdown content',
          received: 'potentially unsafe script tags',
          line: 1,
          column: 1
        });
      }
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }

  // Basic RTF to Markdown conversion (simplified for MCP server)
  private convertRtfToMarkdownBasic(content: string): string {
    // This is a basic implementation for the MCP server
    // In production, this should integrate with the Rust conversion engine

    // Remove RTF control words and braces
    let markdown = content
      .replace(/\{\\rtf\d+[^}]*\}/g, '') // Remove RTF header
      .replace(/\{\\[^}]*\}/g, '') // Remove control groups
      .replace(/\\[a-z]+\d*/g, '') // Remove control words
      .replace(/[{}]/g, '') // Remove remaining braces
      .trim();

    // Basic formatting conversion
    markdown = markdown
      .replace(/\\b\s*/g, '**') // Bold
      .replace(/\\i\s*/g, '*') // Italic
      .replace(/\\par\s*/g, '\n\n') // Paragraphs
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim();

    return markdown;
  }

  // Basic Markdown to RTF conversion (simplified for MCP server)
  private convertMarkdownToRtfBasic(content: string): string {
    // This is a basic implementation for the MCP server
    // In production, this should integrate with the Rust conversion engine

    let rtf = content
      .replace(/\*\*(.*?)\*\*/g, '{\\b $1}') // Bold
      .replace(/\*(.*?)\*/g, '{\\i $1}') // Italic
      .replace(/\n\n/g, '\\par ') // Paragraphs
      .replace(/\n/g, ' '); // Line breaks

    return `{\\rtf1\\ansi\\deff0 {\\fonttbl {\\f0 Times New Roman;}} \\f0\\fs24 ${rtf}}`;
  }

  // Check if content is valid RTF
  private isValidRtf(content: string): boolean {
    // Basic RTF validation - should start with {\\rtf
    return content.trim().startsWith('{\\rtf');
  }
}