// VB6/VFP9 32-bit DLL Interface for Legacy Format Support
// Provides C-compatible exports for legacy system integration

use std::ffi::{CStr, CString};
use std::os::raw::{c_char, c_int, c_void};
use std::ptr;
use std::slice;
use std::sync::Mutex;
use std::collections::HashMap;

use crate::formats::{FormatManager, FormatType};
use crate::conversion::error::ConversionError;

/// Global format manager instance
static FORMAT_MANAGER: Mutex<Option<FormatManager>> = Mutex::new(None);

/// Error codes for legacy interface
pub const LEGACY_SUCCESS: c_int = 0;
pub const LEGACY_ERROR_INVALID_INPUT: c_int = -1;
pub const LEGACY_ERROR_UNSUPPORTED_FORMAT: c_int = -2;
pub const LEGACY_ERROR_CONVERSION_FAILED: c_int = -3;
pub const LEGACY_ERROR_BUFFER_TOO_SMALL: c_int = -4;
pub const LEGACY_ERROR_INITIALIZATION_FAILED: c_int = -5;

/// Result buffer for legacy interface
#[repr(C)]
pub struct LegacyResult {
    pub error_code: c_int,
    pub content_length: c_int,
    pub format_length: c_int,
    pub warnings_count: c_int,
}

/// Initialize the legacy format manager
/// Returns 0 on success, negative on error
#[no_mangle]
pub extern "stdcall" fn LegacyBridge_Initialize() -> c_int {
    match FORMAT_MANAGER.lock() {
        Ok(mut manager) => {
            *manager = Some(FormatManager::new());
            LEGACY_SUCCESS
        },
        Err(_) => LEGACY_ERROR_INITIALIZATION_FAILED,
    }
}

/// Cleanup the legacy format manager
#[no_mangle]
pub extern "stdcall" fn LegacyBridge_Cleanup() -> c_int {
    match FORMAT_MANAGER.lock() {
        Ok(mut manager) => {
            *manager = None;
            LEGACY_SUCCESS
        },
        Err(_) => LEGACY_ERROR_INITIALIZATION_FAILED,
    }
}

/// Detect format from file content
/// Returns format type ID or negative error code
#[no_mangle]
pub extern "stdcall" fn LegacyBridge_DetectFormat(
    content_ptr: *const u8,
    content_length: c_int,
    confidence_ptr: *mut f32,
) -> c_int {
    if content_ptr.is_null() || content_length <= 0 {
        return LEGACY_ERROR_INVALID_INPUT;
    }

    let content = unsafe {
        slice::from_raw_parts(content_ptr, content_length as usize)
    };

    match FORMAT_MANAGER.lock() {
        Ok(manager_guard) => {
            if let Some(ref manager) = *manager_guard {
                match manager.detect_format(content) {
                    Ok(detection) => {
                        if !confidence_ptr.is_null() {
                            unsafe {
                                *confidence_ptr = detection.confidence;
                            }
                        }
                        
                        match detection.format_type {
                            FormatType::Doc => 1,
                            FormatType::WordPerfect => 2,
                            FormatType::DBase => 3,
                            FormatType::WordStar => 4,
                            FormatType::Lotus123 => 5,
                            FormatType::Unknown => 0,
                        }
                    },
                    Err(_) => LEGACY_ERROR_CONVERSION_FAILED,
                }
            } else {
                LEGACY_ERROR_INITIALIZATION_FAILED
            }
        },
        Err(_) => LEGACY_ERROR_INITIALIZATION_FAILED,
    }
}

/// Convert DOC file to Markdown
/// VB6/VFP9 compatible function
#[no_mangle]
pub extern "stdcall" fn LegacyBridge_ConvertDocToMarkdown(
    content_ptr: *const u8,
    content_length: c_int,
    output_buffer: *mut c_char,
    buffer_size: c_int,
    result_ptr: *mut LegacyResult,
) -> c_int {
    convert_legacy_format(
        content_ptr,
        content_length,
        output_buffer,
        buffer_size,
        result_ptr,
        FormatType::Doc,
        "markdown",
    )
}

/// Convert DOC file to RTF
/// VB6/VFP9 compatible function
#[no_mangle]
pub extern "stdcall" fn LegacyBridge_ConvertDocToRtf(
    content_ptr: *const u8,
    content_length: c_int,
    output_buffer: *mut c_char,
    buffer_size: c_int,
    result_ptr: *mut LegacyResult,
) -> c_int {
    convert_legacy_format(
        content_ptr,
        content_length,
        output_buffer,
        buffer_size,
        result_ptr,
        FormatType::Doc,
        "rtf",
    )
}

/// Convert WordPerfect file to Markdown
#[no_mangle]
pub extern "stdcall" fn LegacyBridge_ConvertWordPerfectToMarkdown(
    content_ptr: *const u8,
    content_length: c_int,
    output_buffer: *mut c_char,
    buffer_size: c_int,
    result_ptr: *mut LegacyResult,
) -> c_int {
    convert_legacy_format(
        content_ptr,
        content_length,
        output_buffer,
        buffer_size,
        result_ptr,
        FormatType::WordPerfect,
        "markdown",
    )
}

/// Convert dBase file to Markdown
#[no_mangle]
pub extern "stdcall" fn LegacyBridge_ConvertDBaseToMarkdown(
    content_ptr: *const u8,
    content_length: c_int,
    output_buffer: *mut c_char,
    buffer_size: c_int,
    result_ptr: *mut LegacyResult,
) -> c_int {
    convert_legacy_format(
        content_ptr,
        content_length,
        output_buffer,
        buffer_size,
        result_ptr,
        FormatType::DBase,
        "markdown",
    )
}

/// Convert WordStar file to Markdown
#[no_mangle]
pub extern "stdcall" fn LegacyBridge_ConvertWordStarToMarkdown(
    content_ptr: *const u8,
    content_length: c_int,
    output_buffer: *mut c_char,
    buffer_size: c_int,
    result_ptr: *mut LegacyResult,
) -> c_int {
    convert_legacy_format(
        content_ptr,
        content_length,
        output_buffer,
        buffer_size,
        result_ptr,
        FormatType::WordStar,
        "markdown",
    )
}

/// Convert Lotus 1-2-3 file to Markdown
#[no_mangle]
pub extern "stdcall" fn LegacyBridge_ConvertLotusToMarkdown(
    content_ptr: *const u8,
    content_length: c_int,
    output_buffer: *mut c_char,
    buffer_size: c_int,
    result_ptr: *mut LegacyResult,
) -> c_int {
    convert_legacy_format(
        content_ptr,
        content_length,
        output_buffer,
        buffer_size,
        result_ptr,
        FormatType::Lotus123,
        "markdown",
    )
}

/// Generic conversion function for legacy formats
fn convert_legacy_format(
    content_ptr: *const u8,
    content_length: c_int,
    output_buffer: *mut c_char,
    buffer_size: c_int,
    result_ptr: *mut LegacyResult,
    format_type: FormatType,
    output_format: &str,
) -> c_int {
    // Initialize result structure
    if !result_ptr.is_null() {
        unsafe {
            (*result_ptr).error_code = LEGACY_SUCCESS;
            (*result_ptr).content_length = 0;
            (*result_ptr).format_length = 0;
            (*result_ptr).warnings_count = 0;
        }
    }

    // Validate input parameters
    if content_ptr.is_null() || content_length <= 0 || output_buffer.is_null() || buffer_size <= 0 {
        if !result_ptr.is_null() {
            unsafe {
                (*result_ptr).error_code = LEGACY_ERROR_INVALID_INPUT;
            }
        }
        return LEGACY_ERROR_INVALID_INPUT;
    }

    let content = unsafe {
        slice::from_raw_parts(content_ptr, content_length as usize)
    };

    match FORMAT_MANAGER.lock() {
        Ok(manager_guard) => {
            if let Some(ref manager) = *manager_guard {
                let conversion_result = match output_format {
                    "markdown" => manager.convert_to_markdown(content, &format_type),
                    "rtf" => manager.convert_to_rtf(content, &format_type),
                    _ => Err(ConversionError::UnsupportedFormat("Unknown output format".to_string())),
                };

                match conversion_result {
                    Ok(result) => {
                        let content_bytes = result.content.as_bytes();
                        let required_size = content_bytes.len() + 1; // +1 for null terminator

                        if required_size > buffer_size as usize {
                            if !result_ptr.is_null() {
                                unsafe {
                                    (*result_ptr).error_code = LEGACY_ERROR_BUFFER_TOO_SMALL;
                                    (*result_ptr).content_length = required_size as c_int;
                                }
                            }
                            return LEGACY_ERROR_BUFFER_TOO_SMALL;
                        }

                        // Copy content to output buffer
                        unsafe {
                            ptr::copy_nonoverlapping(
                                content_bytes.as_ptr(),
                                output_buffer as *mut u8,
                                content_bytes.len(),
                            );
                            *output_buffer.add(content_bytes.len()) = 0; // Null terminator
                        }

                        // Fill result structure
                        if !result_ptr.is_null() {
                            unsafe {
                                (*result_ptr).error_code = LEGACY_SUCCESS;
                                (*result_ptr).content_length = content_bytes.len() as c_int;
                                (*result_ptr).format_length = result.format.len() as c_int;
                                (*result_ptr).warnings_count = result.warnings.len() as c_int;
                            }
                        }

                        LEGACY_SUCCESS
                    },
                    Err(error) => {
                        let error_code = match error {
                            ConversionError::UnsupportedFormat(_) => LEGACY_ERROR_UNSUPPORTED_FORMAT,
                            ConversionError::InvalidInput(_) => LEGACY_ERROR_INVALID_INPUT,
                            _ => LEGACY_ERROR_CONVERSION_FAILED,
                        };

                        if !result_ptr.is_null() {
                            unsafe {
                                (*result_ptr).error_code = error_code;
                            }
                        }

                        error_code
                    }
                }
            } else {
                if !result_ptr.is_null() {
                    unsafe {
                        (*result_ptr).error_code = LEGACY_ERROR_INITIALIZATION_FAILED;
                    }
                }
                LEGACY_ERROR_INITIALIZATION_FAILED
            }
        },
        Err(_) => {
            if !result_ptr.is_null() {
                unsafe {
                    (*result_ptr).error_code = LEGACY_ERROR_INITIALIZATION_FAILED;
                }
            }
            LEGACY_ERROR_INITIALIZATION_FAILED
        }
    }
}

/// Get last error message (for debugging)
#[no_mangle]
pub extern "stdcall" fn LegacyBridge_GetLastError(
    error_buffer: *mut c_char,
    buffer_size: c_int,
) -> c_int {
    if error_buffer.is_null() || buffer_size <= 0 {
        return LEGACY_ERROR_INVALID_INPUT;
    }

    let error_msg = "Check error codes for specific error information";
    let error_bytes = error_msg.as_bytes();
    
    if error_bytes.len() + 1 > buffer_size as usize {
        return LEGACY_ERROR_BUFFER_TOO_SMALL;
    }

    unsafe {
        ptr::copy_nonoverlapping(
            error_bytes.as_ptr(),
            error_buffer as *mut u8,
            error_bytes.len(),
        );
        *error_buffer.add(error_bytes.len()) = 0;
    }

    LEGACY_SUCCESS
}

/// Get version information
#[no_mangle]
pub extern "stdcall" fn LegacyBridge_GetVersion(
    version_buffer: *mut c_char,
    buffer_size: c_int,
) -> c_int {
    if version_buffer.is_null() || buffer_size <= 0 {
        return LEGACY_ERROR_INVALID_INPUT;
    }

    let version = "LegacyBridge v1.0.0";
    let version_bytes = version.as_bytes();
    
    if version_bytes.len() + 1 > buffer_size as usize {
        return LEGACY_ERROR_BUFFER_TOO_SMALL;
    }

    unsafe {
        ptr::copy_nonoverlapping(
            version_bytes.as_ptr(),
            version_buffer as *mut u8,
            version_bytes.len(),
        );
        *version_buffer.add(version_bytes.len()) = 0;
    }

    LEGACY_SUCCESS
}

/// Get supported formats list
#[no_mangle]
pub extern "stdcall" fn LegacyBridge_GetSupportedFormats() -> c_int {
    // Return bitmask of supported formats
    // Bit 0: DOC, Bit 1: WordPerfect, Bit 2: dBase, Bit 3: WordStar, Bit 4: Lotus
    0b11111 // All formats supported
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_initialization() {
        assert_eq!(LegacyBridge_Initialize(), LEGACY_SUCCESS);
        assert_eq!(LegacyBridge_Cleanup(), LEGACY_SUCCESS);
    }

    #[test]
    fn test_version_info() {
        let mut buffer = [0u8; 256];
        let result = LegacyBridge_GetVersion(buffer.as_mut_ptr() as *mut c_char, 256);
        assert_eq!(result, LEGACY_SUCCESS);
        
        let version_str = unsafe { CStr::from_ptr(buffer.as_ptr() as *const c_char) };
        assert!(version_str.to_string_lossy().contains("LegacyBridge"));
    }

    #[test]
    fn test_supported_formats() {
        let formats = LegacyBridge_GetSupportedFormats();
        assert_eq!(formats, 0b11111); // All 5 formats supported
    }
}
