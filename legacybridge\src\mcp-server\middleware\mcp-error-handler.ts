// MCP Server Error Handler Middleware
// Provides centralized error handling for the MCP server

import { Request, Response, NextFunction } from 'express';
import { MCPLogger } from '../utils/mcp-logger';

// Base error class
export class MCPError extends Error {
  statusCode: number;
  errorCode: string;
  userMessage: string;
  details?: any;
  
  constructor(message: string, statusCode: number = 500, errorCode: string = 'INTERNAL_ERROR', userMessage?: string, details?: any) {
    super(message);
    this.name = this.constructor.name;
    this.statusCode = statusCode;
    this.errorCode = errorCode;
    this.userMessage = userMessage || message;
    this.details = details;
  }
}

// Specific error classes
export class ValidationError extends MCPError {
  constructor(message: string, details?: any) {
    super(message, 400, 'VALIDATION_ERROR', message, details);
  }
}

export class AuthenticationError extends MCPError {
  constructor(message: string, details?: any) {
    super(message, 401, 'AUTHENTICATION_ERROR', 'Authentication failed', details);
  }
}

export class AuthorizationError extends MCPError {
  constructor(message: string, details?: any) {
    super(message, 403, 'AUTHORIZATION_ERROR', 'You do not have permission to perform this action', details);
  }
}

export class NotFoundError extends MCPError {
  constructor(message: string, details?: any) {
    super(message, 404, 'NOT_FOUND', message, details);
  }
}

export class ConversionError extends MCPError {
  constructor(message: string, details?: any) {
    super(message, 422, 'CONVERSION_ERROR', 'Failed to convert the document', details);
  }
}

export class RateLimitError extends MCPError {
  constructor(message: string, details?: any) {
    super(message, 429, 'RATE_LIMIT_EXCEEDED', 'Rate limit exceeded. Please try again later', details);
  }
}

export class FileStorageError extends MCPError {
  constructor(message: string, details?: any) {
    super(message, 500, 'FILE_STORAGE_ERROR', 'Failed to store or retrieve file', details);
  }
}

// Error handler middleware
export const MCPErrorHandler = (logger: MCPLogger) => {
  return (err: Error, req: Request, res: Response, next: NextFunction) => {
    // Default error response
    let statusCode = 500;
    let errorCode = 'INTERNAL_ERROR';
    let userMessage = 'An unexpected error occurred';
    let details = undefined;
    
    // Handle known error types
    if (err instanceof MCPError) {
      statusCode = err.statusCode;
      errorCode = err.errorCode;
      userMessage = err.userMessage;
      details = err.details;
      
      // Log error with appropriate level based on status code
      if (statusCode >= 500) {
        logger.error(`${err.name}: ${err.message}`, err, { 
          path: req.path, 
          method: req.method,
          statusCode,
          errorCode,
        });
      } else if (statusCode >= 400) {
        logger.warn(`${err.name}: ${err.message}`, { 
          path: req.path, 
          method: req.method,
          statusCode,
          errorCode,
          details,
        });
      }
    } else {
      // Unknown error - log as error
      logger.error('Unhandled error', err, { 
        path: req.path, 
        method: req.method,
      });
    }
    
    // Send error response
    res.status(statusCode).json({
      status: 'error',
      errorCode,
      message: userMessage,
      details: process.env.NODE_ENV === 'production' ? undefined : details,
      requestId: req.headers['x-request-id'],
      timestamp: new Date().toISOString(),
    });
  };
};