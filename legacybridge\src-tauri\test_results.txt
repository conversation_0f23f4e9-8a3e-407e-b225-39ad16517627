    Updating crates.io index
     Locking 466 packages to latest compatible versions
      Adding base64 v0.21.7 (available: v0.22.1)
      Adding criterion v0.5.1 (available: v0.6.0)
      Adding pulldown-cmark v0.9.6 (available: v0.13.0)
      Adding tauri v1.8.3 (available: v2.7.0)
      Adding tauri-build v1.5.6 (available: v2.3.1)
      Adding thiserror v1.0.69 (available: v2.0.12)
 Downloading crates ...
  Downloaded adler2 v2.0.1
  Downloaded alloc-stdlib v0.2.2
  Downloaded bitflags v1.3.2
  Downloaded fastrand v2.3.0
  Downloaded generic-array v0.14.7
  Downloaded powerfmt v0.2.0
  Downloaded webkit2gtk-sys v0.18.0
  Downloaded utf8_iter v1.0.4
  Downloaded jsonptr v0.4.7
  Downloaded mac v0.1.1
  Downloaded nodrop v0.1.14
  Downloaded phf v0.8.0
  Downloaded icu_normalizer_data v2.0.0
  Downloaded x11-dl v2.21.0
  Downloaded icu_properties_data v2.0.1
  Downloaded itertools v0.10.5
  Downloaded wry v0.24.11
  Downloaded tokio v1.46.1
  Downloaded regex-automata v0.4.9
  Downloaded indexmap v2.10.0
  Downloaded idna v1.0.3
  Downloaded rustix v1.0.8
  Downloaded phf_macros v0.8.0
  Downloaded phf_generator v0.11.3
  Downloaded phf_generator v0.10.0
  Downloaded phf_generator v0.8.0
  Downloaded phf_codegen v0.10.0
  Downloaded phf_codegen v0.8.0
  Downloaded phf v0.11.3
  Downloaded phf v0.10.1
  Downloaded parking_lot v0.12.4
  Downloaded pango-sys v0.15.10
  Downloaded pango v0.15.10
  Downloaded open v3.2.0
  Downloaded memchr v2.7.5
  Downloaded oorandom v11.1.5
  Downloaded num_cpus v1.17.0
  Downloaded num-traits v0.2.19
  Downloaded tao v0.16.10
  Downloaded libc v0.2.174
  Downloaded once_cell v1.21.3
  Downloaded num-conv v0.1.0
  Downloaded zerotrie v0.2.2
  Downloaded zerocopy v0.8.26
  Downloaded regex-syntax v0.8.5
  Downloaded x11 v2.21.0
  Downloaded pulldown-cmark v0.9.6
  Downloaded proptest v1.7.0
  Downloaded unicode-width v0.2.1
  Downloaded tauri v1.8.3
  Downloaded syn v2.0.104
  Downloaded syn v1.0.109
  Downloaded regex v1.11.1
  Downloaded icu_locale_core v2.0.0
  Downloaded icu_collections v2.0.0
  Downloaded miniz_oxide v0.8.9
  Downloaded ico v0.4.0
  Downloaded http v0.2.12
  Downloaded hashbrown v0.15.4
  Downloaded gtk-sys v0.15.3
  Downloaded gtk v0.15.5
  Downloaded percent-encoding v2.3.1
  Downloaded pathdiff v0.2.3
  Downloaded parking_lot_core v0.9.11
  Downloaded html5ever v0.26.0
  Downloaded hashbrown v0.12.3
  Downloaded glib v0.15.12
  Downloaded zerovec v0.11.2
  Downloaded time v0.3.41
  Downloaded serde_with v3.14.0
  Downloaded serde_json v1.0.141
  Downloaded new_debug_unreachable v1.0.6
  Downloaded memoffset v0.9.1
  Downloaded matches v0.1.10
  Downloaded markup5ever v0.11.0
  Downloaded log v0.4.27
  Downloaded url v2.5.4
  Downloaded unicode-segmentation v1.12.0
  Downloaded toml_edit v0.19.15
  Downloaded rayon v1.10.0
  Downloaded lock_api v0.4.13
  Downloaded litemap v0.8.0
  Downloaded lazy_static v1.5.0
  Downloaded kuchikiki v0.8.2
  Downloaded json-patch v2.0.0
  Downloaded javascriptcore-rs-sys v0.4.0
  Downloaded javascriptcore-rs v0.16.0
  Downloaded itoa v1.0.15
  Downloaded itoa v0.4.8
  Downloaded is-terminal v0.4.16
  Downloaded instant v0.1.13
  Downloaded infer v0.13.0
  Downloaded indexmap v1.9.3
  Downloaded ignore v0.4.23
  Downloaded idna_adapter v1.2.1
  Downloaded linux-raw-sys v0.9.4
  Downloaded ident_case v1.0.1
  Downloaded icu_provider v2.0.0
  Downloaded icu_properties v2.0.1
  Downloaded zerofrom-derive v0.1.6
  Downloaded zerofrom v0.1.6
  Downloaded yoke-derive v0.8.0
  Downloaded yoke v0.8.0
  Downloaded xattr v1.5.1
  Downloaded writeable v0.6.1
  Downloaded wait-timeout v0.2.1
  Downloaded version_check v0.9.5
  Downloaded version-compare v0.2.0
  Downloaded version-compare v0.0.11
  Downloaded proc-macro2 v1.0.95
  Downloaded proc-macro-hack v0.5.20+deprecated
  Downloaded proc-macro-error-attr v1.0.4
  Downloaded proc-macro-error v1.0.4
  Downloaded proc-macro-crate v1.3.1
  Downloaded precomputed-hash v0.1.1
  Downloaded ppv-lite86 v0.2.21
  Downloaded pkg-config v0.3.32
  Downloaded pin-utils v0.1.0
  Downloaded pin-project-lite v0.2.16
  Downloaded phf_shared v0.11.3
  Downloaded phf_shared v0.10.0
  Downloaded webkit2gtk v0.18.2
  Downloaded unicode-ident v1.0.18
  Downloaded typenum v1.18.0
  Downloaded toml_edit v0.22.27
  Downloaded toml v0.7.8
  Downloaded tar v0.4.44
  Downloaded soup2 v0.2.1
  Downloaded serde_derive v1.0.219
  Downloaded serde v1.0.219
  Downloaded schemars v1.0.4
  Downloaded schemars v0.9.0
  Downloaded icu_normalizer v2.0.0
  Downloaded iana-time-zone v0.1.63
  Downloaded http-range v0.1.5
  Downloaded hex v0.4.3
  Downloaded heck v0.5.0
  Downloaded heck v0.4.1
  Downloaded heck v0.3.3
  Downloaded half v2.6.0
  Downloaded gtk3-macros v0.15.6
  Downloaded gobject-sys v0.15.10
  Downloaded globset v0.4.16
  Downloaded glob v0.3.2
  Downloaded glib-sys v0.15.10
  Downloaded glib-macros v0.15.13
  Downloaded zerovec-derive v0.11.1
  Downloaded winnow v0.7.12
  Downloaded winnow v0.5.40
  Downloaded png v0.17.16
  Downloaded plotters v0.3.7
  Downloaded uuid v1.17.0
  Downloaded toml v0.5.11
  Downloaded time-macros v0.2.22
  Downloaded tendril v0.4.3
  Downloaded tempfile v3.20.0
  Downloaded tauri-utils v1.6.2
  Downloaded tauri-runtime v0.14.6
  Downloaded tauri-macros v1.4.7
  Downloaded tauri-codegen v1.4.6
  Downloaded target-lexicon v0.12.16
  Downloaded system-deps v6.2.2
  Downloaded smallvec v1.15.1
  Downloaded serde_with_macros v3.14.0
  Downloaded semver v1.0.26
  Downloaded selectors v0.22.0
  Downloaded ryu v1.0.20
  Downloaded rusty-fork v0.3.0
  Downloaded rayon-core v1.12.1
  Downloaded rand v0.9.2
  Downloaded rand v0.8.5
  Downloaded rand v0.7.3
  Downloaded encoding_rs v0.8.35
  Downloaded brotli v7.0.0
  Downloaded utf-8 v0.7.6
  Downloaded unicase v2.8.1
  Downloaded unarray v0.1.4
  Downloaded toml_write v0.1.2
  Downloaded toml_datetime v0.6.11
  Downloaded toml v0.8.23
  Downloaded tinytemplate v1.2.1
  Downloaded tinystr v0.8.1
  Downloaded time-core v0.1.4
  Downloaded thiserror-impl v1.0.69
  Downloaded thiserror v1.0.69
  Downloaded tauri-winres v0.1.1
  Downloaded tauri-runtime-wry v0.14.11
  Downloaded tauri-build v1.5.6
  Downloaded system-deps v5.0.0
  Downloaded synstructure v0.13.2
  Downloaded strsim v0.11.1
  Downloaded string_cache_codegen v0.5.4
  Downloaded string_cache v0.8.9
  Downloaded state v0.5.3
  Downloaded soup2-sys v0.2.0
  Downloaded slab v0.4.10
  Downloaded siphasher v0.3.11
  Downloaded simd-adler32 v0.3.7
  Downloaded shlex v1.3.0
  Downloaded sha2 v0.10.9
  Downloaded servo_arc v0.1.1
  Downloaded serialize-to-javascript v0.1.2
  Downloaded serde_spanned v0.6.9
  Downloaded serde_repr v0.1.20
  Downloaded scopeguard v1.2.0
  Downloaded same-file v1.0.6
  Downloaded rustc_version v0.4.1
  Downloaded ref-cast-impl v1.0.24
  Downloaded ref-cast v1.0.24
  Downloaded rand_xorshift v0.4.0
  Downloaded rand_core v0.6.4
  Downloaded rand_core v0.5.1
  Downloaded quote v1.0.40
  Downloaded quick-error v1.2.3
  Downloaded gio-sys v0.15.10
  Downloaded gdk-sys v0.15.1
  Downloaded futures-util v0.3.31
  Downloaded flate2 v1.1.2
  Downloaded bumpalo v3.19.0
  Downloaded walkdir v2.5.0
  Downloaded potential_utf v0.1.2
  Downloaded plotters-backend v0.3.7
  Downloaded thin-slice v0.1.1
  Downloaded stable_deref_trait v1.2.0
  Downloaded siphasher v1.0.1
  Downloaded serialize-to-javascript-impl v0.1.2
  Downloaded raw-window-handle v0.5.2
  Downloaded rand_pcg v0.2.1
  Downloaded rand_core v0.9.3
  Downloaded rand_chacha v0.9.0
  Downloaded rand_chacha v0.3.1
  Downloaded gio v0.15.12
  Downloaded gdk v0.15.4
  Downloaded darling_core v0.20.11
  Downloaded crossbeam-channel v0.5.15
  Downloaded criterion v0.5.1
  Downloaded clap_builder v4.5.41
  Downloaded chrono v0.4.41
  Downloaded cc v1.2.30
  Downloaded bytes v1.10.1
  Downloaded bstr v1.12.0
  Downloaded brotli-decompressor v4.0.3
  Downloaded plotters-svg v0.3.7
  Downloaded phf_shared v0.8.0
  Downloaded phf_macros v0.11.3
  Downloaded rand_chacha v0.2.2
  Downloaded getrandom v0.3.3
  Downloaded getrandom v0.2.16
  Downloaded getrandom v0.1.16
  Downloaded getopts v0.2.23
  Downloaded gdk-pixbuf-sys v0.15.10
  Downloaded futures-macro v0.3.31
  Downloaded futures-io v0.3.31
  Downloaded futures-executor v0.3.31
  Downloaded futures-core v0.3.31
  Downloaded futures-channel v0.3.31
  Downloaded futf v0.1.5
  Downloaded form_urlencoded v1.2.1
  Downloaded fluent-uri v0.1.4
  Downloaded filetime v0.2.25
  Downloaded fdeflate v0.3.7
  Downloaded errno v0.3.13
  Downloaded embed-resource v2.5.2
  Downloaded either v1.15.0
  Downloaded dyn-clone v1.0.19
  Downloaded dunce v1.0.5
  Downloaded dtoa-short v0.3.5
  Downloaded dtoa v1.0.10
  Downloaded displaydoc v0.2.5
  Downloaded dirs-next v2.0.0
  Downloaded digest v0.10.7
  Downloaded derive_more v0.99.20
  Downloaded deranged v0.4.0
  Downloaded darling_macro v0.20.11
  Downloaded darling v0.20.11
  Downloaded cssparser-macros v0.6.1
  Downloaded cssparser v0.27.2
  Downloaded crypto-common v0.1.6
  Downloaded crossbeam-utils v0.8.21
  Downloaded crossbeam-epoch v0.9.18
  Downloaded crossbeam-deque v0.8.6
  Downloaded criterion-plot v0.5.0
  Downloaded crc32fast v1.5.0
  Downloaded clap_lex v0.7.5
  Downloaded ciborium-ll v0.2.2
  Downloaded cfg-expr v0.15.8
  Downloaded cfb v0.7.3
  Downloaded cargo_toml v0.15.3
  Downloaded byteorder v1.5.0
  Downloaded gdkx11-sys v0.15.1
  Downloaded gdkwayland-sys v0.15.3
  Downloaded gdk-pixbuf v0.15.11
  Downloaded fxhash v0.2.1
  Downloaded futures-task v0.3.31
  Downloaded fnv v1.0.7
  Downloaded field-offset v0.3.6
  Downloaded equivalent v1.0.2
  Downloaded dirs-sys-next v0.1.2
  Downloaded ctor v0.2.9
  Downloaded cpufeatures v0.2.17
  Downloaded clap v4.5.41
  Downloaded ciborium-io v0.2.2
  Downloaded ciborium v0.2.2
  Downloaded cfg-if v1.0.1
  Downloaded cfg-expr v0.9.1
  Downloaded cast v0.3.0
  Downloaded cairo-rs v0.15.12
  Downloaded block-buffer v0.10.4
  Downloaded bitflags v2.9.1
  Downloaded base64 v0.22.1
  Downloaded base64 v0.21.7
  Downloaded anyhow v1.0.98
  Downloaded anstyle v1.0.11
  Downloaded aho-corasick v1.1.3
  Downloaded ahash v0.8.12
  Downloaded convert_case v0.4.0
  Downloaded cairo-sys-rs v0.15.1
  Downloaded bit-vec v0.8.0
  Downloaded atk v0.15.1
  Downloaded bit-set v0.8.0
  Downloaded autocfg v1.5.0
  Downloaded atk-sys v0.15.1
  Downloaded anes v0.1.6
  Downloaded alloc-no-stdlib v2.0.4
   Compiling proc-macro2 v1.0.95
   Compiling unicode-ident v1.0.18
   Compiling serde v1.0.219
   Compiling libc v0.2.174
   Compiling quote v1.0.40
   Compiling syn v2.0.104
   Compiling smallvec v1.15.1
   Compiling hashbrown v0.15.4
   Compiling cfg-if v1.0.1
   Compiling equivalent v1.0.2
   Compiling indexmap v2.10.0
   Compiling pkg-config v0.3.32
   Compiling heck v0.5.0
   Compiling toml_write v0.1.2
   Compiling winnow v0.7.12
   Compiling target-lexicon v0.12.16
   Compiling cfg-expr v0.15.8
   Compiling serde_derive v1.0.219
   Compiling version-compare v0.2.0
   Compiling zerocopy v0.8.26
   Compiling syn v1.0.109
   Compiling ppv-lite86 v0.2.21
   Compiling autocfg v1.5.0
   Compiling toml_datetime v0.6.11
   Compiling serde_spanned v0.6.9
   Compiling toml_edit v0.22.27
   Compiling version_check v0.9.5
   Compiling toml v0.8.23
   Compiling system-deps v6.2.2
   Compiling siphasher v0.3.11
   Compiling memchr v2.7.5
   Compiling glib-sys v0.15.10
warning: glib-sys@0.15.10: Could not run `PKG_CONFIG_ALLOW_SYSTEM_CFLAGS=1 pkg-config --libs --cflags glib-2.0 'glib-2.0 >= 2.48'`
error: failed to run custom build command for `glib-sys v0.15.10`

Caused by:
  process didn't exit successfully: `/root/repo/legacybridge/src-tauri/target/debug/build/glib-sys-3818d4b068377bc2/build-script-build` (exit status: 1)
  --- stdout
  cargo:rerun-if-env-changed=GLIB_2.0_NO_PKG_CONFIG
  cargo:rerun-if-env-changed=PKG_CONFIG_x86_64-unknown-linux-gnu
  cargo:rerun-if-env-changed=PKG_CONFIG_x86_64_unknown_linux_gnu
  cargo:rerun-if-env-changed=HOST_PKG_CONFIG
  cargo:rerun-if-env-changed=PKG_CONFIG
  cargo:rerun-if-env-changed=PKG_CONFIG_PATH_x86_64-unknown-linux-gnu
  cargo:rerun-if-env-changed=PKG_CONFIG_PATH_x86_64_unknown_linux_gnu
  cargo:rerun-if-env-changed=HOST_PKG_CONFIG_PATH
  cargo:rerun-if-env-changed=PKG_CONFIG_PATH
  cargo:rerun-if-env-changed=PKG_CONFIG_LIBDIR_x86_64-unknown-linux-gnu
  cargo:rerun-if-env-changed=PKG_CONFIG_LIBDIR_x86_64_unknown_linux_gnu
  cargo:rerun-if-env-changed=HOST_PKG_CONFIG_LIBDIR
  cargo:rerun-if-env-changed=PKG_CONFIG_LIBDIR
  cargo:rerun-if-env-changed=PKG_CONFIG_SYSROOT_DIR_x86_64-unknown-linux-gnu
  cargo:rerun-if-env-changed=PKG_CONFIG_SYSROOT_DIR_x86_64_unknown_linux_gnu
  cargo:rerun-if-env-changed=HOST_PKG_CONFIG_SYSROOT_DIR
  cargo:rerun-if-env-changed=PKG_CONFIG_SYSROOT_DIR
  cargo:rerun-if-env-changed=PKG_CONFIG_PATH_x86_64-unknown-linux-gnu
  cargo:rerun-if-env-changed=PKG_CONFIG_PATH_x86_64_unknown_linux_gnu
  cargo:rerun-if-env-changed=HOST_PKG_CONFIG_PATH
  cargo:rerun-if-env-changed=PKG_CONFIG_PATH
  cargo:rerun-if-env-changed=PKG_CONFIG_LIBDIR_x86_64-unknown-linux-gnu
  cargo:rerun-if-env-changed=PKG_CONFIG_LIBDIR_x86_64_unknown_linux_gnu
  cargo:rerun-if-env-changed=HOST_PKG_CONFIG_LIBDIR
  cargo:rerun-if-env-changed=PKG_CONFIG_LIBDIR
  cargo:rerun-if-env-changed=PKG_CONFIG_SYSROOT_DIR_x86_64-unknown-linux-gnu
  cargo:rerun-if-env-changed=PKG_CONFIG_SYSROOT_DIR_x86_64_unknown_linux_gnu
  cargo:rerun-if-env-changed=HOST_PKG_CONFIG_SYSROOT_DIR
  cargo:rerun-if-env-changed=PKG_CONFIG_SYSROOT_DIR
  cargo:warning=Could not run `PKG_CONFIG_ALLOW_SYSTEM_CFLAGS=1 pkg-config --libs --cflags glib-2.0 'glib-2.0 >= 2.48'`
  The pkg-config command could not be found.

  Most likely, you need to install a pkg-config package for your OS.
  Try `apt install pkg-config`, or `yum install pkg-config`, or `brew install pkgconf`
  or `pkg install pkg-config`, or `apk add pkgconfig` depending on your distribution.

  If you've already installed it, ensure the pkg-config command is one of the
  directories in the PATH environment variable.

  If you did not expect this build to link to a pre-installed system library,
  then check documentation of the glib-sys crate for an option to
  build the library from source, or disable features or dependencies
  that require pkg-config.
warning: build failed, waiting for other jobs to finish...
