// MCP Server Implementation for LegacyBridge
// Implements the Model Context Protocol (MCP) for AI assistant integration

import express, { Express, Request, Response, NextFunction } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import { json, urlencoded } from 'body-parser';
import { v4 as uuidv4 } from 'uuid';
import { createServer } from 'http';
import { Server as SocketIOServer } from 'socket.io';
import { MCPRouter } from '../routes/mcp-router';
import { MCPAuthMiddleware } from '../middleware/mcp-auth';
import { MCPErrorHandler } from '../middleware/mcp-error-handler';
import { MCPLogger } from '../utils/mcp-logger';
import { MCPConfig } from '../utils/mcp-config';
import { MCPCache } from '../services/mcp-cache';
import { MCPMetricsCollector } from '../utils/mcp-metrics';

export class MCPServer {
  private app: Express;
  private server: any;
  private io: SocketIOServer;
  private logger: MCPLogger;
  private config: MCPConfig;
  private cache: MCPCache;
  private metrics: MCPMetricsCollector;

  constructor(config: MCPConfig) {
    this.app = express();
    this.server = createServer(this.app);
    this.io = new SocketIOServer(this.server, {
      cors: {
        origin: '*',
        methods: ['GET', 'POST']
      }
    });
    this.logger = new MCPLogger(config.logLevel);
    this.config = config;
    this.cache = new MCPCache(config.cache);
    this.metrics = new MCPMetricsCollector();
    
    this.setupMiddleware();
    this.setupRoutes();
    this.setupSocketIO();
    this.setupErrorHandling();
  }

  private setupMiddleware(): void {
    // Security middleware
    this.app.use(helmet());
    this.app.use(cors({
      origin: this.config.corsOrigins || '*',
      methods: ['GET', 'POST', 'PUT', 'DELETE'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-API-Key']
    }));

    // Rate limiting
    const limiter = rateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: this.config.rateLimit || 100, // limit each IP to 100 requests per windowMs
      standardHeaders: true,
      legacyHeaders: false,
      message: {
        status: 'error',
        message: 'Too many requests, please try again later.'
      }
    });
    this.app.use(limiter);

    // Body parsing
    this.app.use(json({ limit: '50mb' }));
    this.app.use(urlencoded({ extended: true, limit: '50mb' }));

    // Request logging
    this.app.use((req: Request, res: Response, next: NextFunction) => {
      const requestId = uuidv4();
      req.headers['x-request-id'] = requestId;
      this.logger.info(`[${requestId}] ${req.method} ${req.path}`);
      
      // Log response time
      const start = Date.now();
      res.on('finish', () => {
        const duration = Date.now() - start;
        this.logger.info(`[${requestId}] ${req.method} ${req.path} ${res.statusCode} ${duration}ms`);
        
        // Collect metrics
        this.metrics.recordRequest({
          method: req.method,
          path: req.path,
          statusCode: res.statusCode,
          duration
        });
      });
      
      next();
    });

    // Authentication middleware
    this.app.use(MCPAuthMiddleware(this.config));
  }

  private setupRoutes(): void {
    // Health check endpoint
    this.app.get('/health', (req: Request, res: Response) => {
      res.json({
        status: 'healthy',
        version: this.config.version,
        uptime: process.uptime()
      });
    });

    // Metrics endpoint
    this.app.get('/metrics', (req: Request, res: Response) => {
      res.json(this.metrics.getMetrics());
    });

    // MCP routes
    this.app.use('/mcp', MCPRouter(this.config, this.cache, this.logger));

    // Static dashboard (if enabled)
    if (this.config.enableDashboard) {
      this.app.use('/dashboard', express.static('public/dashboard'));
    }
  }

  private setupSocketIO(): void {
    this.io.on('connection', (socket) => {
      this.logger.info(`Client connected: ${socket.id}`);
      
      // Handle progress updates
      socket.on('subscribe', (jobId: string) => {
        socket.join(`job-${jobId}`);
        this.logger.info(`Client ${socket.id} subscribed to job ${jobId}`);
      });
      
      socket.on('disconnect', () => {
        this.logger.info(`Client disconnected: ${socket.id}`);
      });
    });
  }

  private setupErrorHandling(): void {
    // 404 handler
    this.app.use((req: Request, res: Response) => {
      res.status(404).json({
        status: 'error',
        message: `Route ${req.path} not found`
      });
    });

    // Global error handler
    this.app.use(MCPErrorHandler(this.logger));
  }

  public broadcastProgress(jobId: string, progress: any): void {
    this.io.to(`job-${jobId}`).emit('progress', progress);
  }

  public start(): void {
    const port = this.config.port || 3000;
    this.server.listen(port, () => {
      this.logger.info(`MCP Server started on port ${port}`);
      this.logger.info(`Environment: ${this.config.environment}`);
      this.logger.info(`Version: ${this.config.version}`);
    });
  }

  public stop(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.logger.info('Stopping MCP Server...');
      
      // Close cache connections
      this.cache.close();
      
      // Close HTTP server
      this.server.close((err: Error) => {
        if (err) {
          this.logger.error('Error stopping server:', err);
          reject(err);
        } else {
          this.logger.info('MCP Server stopped');
          resolve();
        }
      });
    });
  }
}