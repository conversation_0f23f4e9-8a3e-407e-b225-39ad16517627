# AI Assistant Integration Examples

This directory contains examples of integrating the LegacyBridge MCP server with various AI assistants.

## Overview

The LegacyBridge MCP (Model Context Protocol) server provides a standardized API for AI assistants to convert between legacy document formats (RTF, DOC, WordPerfect) and modern formats like Markdown. These examples demonstrate how to integrate the MCP server with popular AI assistant platforms.

## Examples

- [OpenAI GPT Integration](./openai-gpt/): Example of integrating with OpenAI's GPT models
- [Anthropic Claude Integration](./anthropic-claude/): Example of integrating with Anthropic's Claude models
- [Hugging Face Integration](./hugging-face/): Example of integrating with Hugging Face models
- [LangChain Integration](./langchain/): Example of integrating with LangChain

## Getting Started

1. Make sure the MCP server is running. You can start it using Docker:

   ```bash
   cd /path/to/legacybridge
   docker-compose -f docker-compose.mcp.yml up -d
   ```

2. Configure your API key in the example you want to run.

3. Run the example following the instructions in its README.

## Common Integration Patterns

### Direct API Integration

The simplest integration pattern is to call the MCP server API directly from your AI assistant application:

```javascript
async function convertDocument(content, format) {
  const response = await fetch('http://localhost:3030/mcp/tools/execute', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-API-Key': 'your-api-key'
    },
    body: JSON.stringify({
      tool: format === 'rtf' ? 'convert_rtf_to_markdown' : 'convert_markdown_to_rtf',
      parameters: {
        content,
        options: {
          preserveFormatting: true
        }
      }
    })
  });
  
  const result = await response.json();
  return result.result.content;
}
```

### Tool-based Integration

Many AI assistants support a "tools" or "functions" feature that allows the AI to call external APIs. You can define the MCP server endpoints as tools:

```javascript
const tools = [
  {
    type: "function",
    function: {
      name: "convert_rtf_to_markdown",
      description: "Convert RTF content to Markdown format",
      parameters: {
        type: "object",
        properties: {
          content: {
            type: "string",
            description: "RTF content to convert"
          },
          options: {
            type: "object",
            properties: {
              preserveFormatting: {
                type: "boolean",
                description: "Whether to preserve formatting"
              }
            }
          }
        },
        required: ["content"]
      }
    }
  },
  {
    type: "function",
    function: {
      name: "convert_markdown_to_rtf",
      description: "Convert Markdown content to RTF format",
      parameters: {
        type: "object",
        properties: {
          content: {
            type: "string",
            description: "Markdown content to convert"
          },
          options: {
            type: "object",
            properties: {
              template: {
                type: "string",
                description: "Template to use for conversion"
              }
            }
          }
        },
        required: ["content"]
      }
    }
  }
];
```

### Webhook Integration

For asynchronous processing, you can use the batch processing API with a webhook callback:

```javascript
async function batchConvert(files, callbackUrl) {
  const response = await fetch('http://localhost:3030/mcp/tools/execute', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-API-Key': 'your-api-key'
    },
    body: JSON.stringify({
      tool: 'batch_convert',
      parameters: {
        files,
        batchOptions: {
          callbackUrl
        }
      }
    })
  });
  
  return await response.json();
}
```

## Security Considerations

When integrating with AI assistants, consider these security best practices:

1. **API Key Management**: Store API keys securely and never expose them in client-side code.
2. **Input Validation**: Validate all input before sending it to the MCP server.
3. **Rate Limiting**: Implement rate limiting to prevent abuse.
4. **HTTPS**: Always use HTTPS for production deployments.
5. **Content Filtering**: Implement content filtering to prevent malicious content.

## Additional Resources

- [MCP Server Integration Guide](../../docs/MCP_SERVER_INTEGRATION.md): Comprehensive guide for MCP server integration
- [MCP Protocol Specification](https://example.com/mcp-protocol): Specification for the Model Context Protocol