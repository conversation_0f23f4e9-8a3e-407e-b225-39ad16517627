// Adaptive Thread Pool with Work-Stealing and Backpressure
//
// Key features:
// 1. Dynamic thread scaling based on system load
// 2. Work-stealing queues for optimal task distribution
// 3. Backpressure management with queue limits
// 4. NUMA-aware thread affinity (where available)
// 5. Automatic load balancing
// 6. Thread pool warm-up for consistent performance

use std::sync::{Arc, atomic::{AtomicUsize, AtomicBool, AtomicU64, Ordering}};
use std::collections::VecDeque;
use std::thread;
use std::time::{Duration, Instant};
use crossbeam_deque::{Worker, Stealer, Injector};
use parking_lot::{Mutex, RwLock};
use num_cpus;

/// Task trait for work items
pub trait Task: Send + 'static {
    type Output: Send + 'static;
    fn execute(self) -> Self::Output;
}

/// Backpressure error when system is overloaded
#[derive(Debug, Clone)]
pub enum BackpressureError {
    QueueFull { current: usize, max: usize },
    SystemOverloaded { load_factor: f64 },
    Timeout { duration: Duration },
}

/// Thread pool statistics
#[derive(Debug, <PERSON><PERSON>, Default)]
pub struct PoolStatistics {
    pub tasks_completed: u64,
    pub tasks_queued: u64,
    pub tasks_stolen: u64,
    pub active_threads: usize,
    pub total_threads: usize,
    pub queue_depth: usize,
    pub average_task_time_ms: f64,
    pub load_factor: f64,
}

/// Configuration for adaptive thread pool
#[derive(Debug, Clone)]
pub struct PoolConfig {
    /// Minimum number of threads
    pub min_threads: usize,
    /// Maximum number of threads
    pub max_threads: usize,
    /// Maximum queue size for backpressure
    pub max_queue_size: usize,
    /// Backpressure threshold (0.0 - 1.0)
    pub backpressure_threshold: f64,
    /// Thread idle timeout before scaling down
    pub idle_timeout: Duration,
    /// Load sampling interval
    pub sampling_interval: Duration,
    /// Enable NUMA awareness
    pub numa_aware: bool,
    /// Warm-up threads on creation
    pub warm_up: bool,
}

impl Default for PoolConfig {
    fn default() -> Self {
        let cpu_count = num_cpus::get();
        Self {
            min_threads: 1,
            max_threads: cpu_count * 2,
            max_queue_size: 10_000,
            backpressure_threshold: 0.8,
            idle_timeout: Duration::from_secs(60),
            sampling_interval: Duration::from_millis(100),
            numa_aware: cfg!(target_os = "linux"),
            warm_up: true,
        }
    }
}

/// Worker thread state
struct WorkerState {
    id: usize,
    thread: Mutex<Option<thread::JoinHandle<()>>>,
    stealer: Stealer<Box<dyn FnOnce() + Send>>,
    tasks_completed: AtomicU64,
    last_active: Mutex<Instant>,
}

/// Adaptive thread pool with work-stealing
pub struct AdaptiveThreadPool {
    config: PoolConfig,
    injector: Arc<Injector<Box<dyn FnOnce() + Send>>>,
    workers: Arc<RwLock<Vec<Arc<WorkerState>>>>,
    active_count: Arc<AtomicUsize>,
    queued_count: Arc<AtomicUsize>,
    completed_count: Arc<AtomicU64>,
    stolen_count: Arc<AtomicU64>,
    running: Arc<AtomicBool>,
    load_monitor: Mutex<Option<thread::JoinHandle<()>>>,
    stats: Arc<RwLock<PoolStatistics>>,
}

impl AdaptiveThreadPool {
    /// Create new adaptive thread pool
    pub fn new() -> Self {
        Self::with_config(PoolConfig::default())
    }

    /// Create thread pool with custom configuration
    pub fn with_config(config: PoolConfig) -> Self {
        let injector = Arc::new(Injector::new());
        let workers = Arc::new(RwLock::new(Vec::new()));
        let active_count = Arc::new(AtomicUsize::new(0));
        let queued_count = Arc::new(AtomicUsize::new(0));
        let completed_count = Arc::new(AtomicU64::new(0));
        let stolen_count = Arc::new(AtomicU64::new(0));
        let running = Arc::new(AtomicBool::new(true));
        let stats = Arc::new(RwLock::new(PoolStatistics::default()));

        let pool = Self {
            config: config.clone(),
            injector,
            workers,
            active_count,
            queued_count,
            completed_count,
            stolen_count,
            running,
            load_monitor: Mutex::new(None),
            stats,
        };

        // Initialize minimum workers
        for _ in 0..config.min_threads {
            pool.spawn_worker();
        }

        // Start load monitoring thread
        pool.start_load_monitor();

        // Warm up thread pool if configured
        if config.warm_up {
            pool.warm_up();
        }

        pool
    }

    /// Submit a single task
    pub fn submit<T>(&self, task: T) -> Result<(), BackpressureError>
    where
        T: Task,
    {
        // Check backpressure
        let queue_size = self.queued_count.load(Ordering::Relaxed);
        if queue_size >= self.config.max_queue_size {
            return Err(BackpressureError::QueueFull {
                current: queue_size,
                max: self.config.max_queue_size,
            });
        }

        let load_factor = self.calculate_load_factor();
        if load_factor > self.config.backpressure_threshold {
            return Err(BackpressureError::SystemOverloaded { load_factor });
        }

        // Increment queued count
        self.queued_count.fetch_add(1, Ordering::Relaxed);

        // Create task closure
        let queued_count = self.queued_count.clone();
        let completed_count = self.completed_count.clone();
        let active_count = self.active_count.clone();

        let task_fn = Box::new(move || {
            active_count.fetch_add(1, Ordering::Relaxed);
            queued_count.fetch_sub(1, Ordering::Relaxed);
            
            let _result = task.execute();
            
            active_count.fetch_sub(1, Ordering::Relaxed);
            completed_count.fetch_add(1, Ordering::Relaxed);
        });

        // Push to injector queue
        self.injector.push(task_fn);

        // Wake up workers
        self.notify_workers();

        Ok(())
    }

    /// Submit a batch of tasks
    pub fn submit_batch<T, I>(&self, tasks: I) -> Result<Vec<()>, BackpressureError>
    where
        T: Task,
        I: IntoIterator<Item = T>,
    {
        let tasks: Vec<T> = tasks.into_iter().collect();
        let batch_size = tasks.len();

        // Check backpressure for batch
        let current_queue = self.queued_count.load(Ordering::Relaxed);
        if current_queue + batch_size > self.config.max_queue_size {
            return Err(BackpressureError::QueueFull {
                current: current_queue,
                max: self.config.max_queue_size,
            });
        }

        let mut results = Vec::with_capacity(batch_size);
        for task in tasks {
            results.push(self.submit(task)?);
        }

        Ok(results)
    }

    /// Spawn a new worker thread
    fn spawn_worker(&self) {
        let worker_id = self.workers.read().len();
        let worker_queue = Worker::new_fifo();
        let stealer = worker_queue.stealer();

        let worker_state = Arc::new(WorkerState {
            id: worker_id,
            thread: Mutex::new(None),
            stealer: stealer.clone(),
            tasks_completed: AtomicU64::new(0),
            last_active: Mutex::new(Instant::now()),
        });

        // Clone for thread
        let injector = self.injector.clone();
        let running = self.running.clone();
        let worker_state_clone = worker_state.clone();
        let stolen_count = self.stolen_count.clone();
        let idle_timeout = self.config.idle_timeout;
        let numa_aware = self.config.numa_aware;

        // Get all stealers
        let all_stealers: Vec<Stealer<Box<dyn FnOnce() + Send>>> = self.workers.read()
            .iter()
            .map(|w| w.stealer.clone())
            .collect();

        // Spawn worker thread
        let thread = thread::Builder::new()
            .name(format!("adaptive-worker-{}", worker_id))
            .spawn(move || {
                // Set NUMA affinity if configured
                #[cfg(target_os = "linux")]
                if numa_aware {
                    Self::set_numa_affinity(worker_id);
                }

                while running.load(Ordering::Relaxed) {
                    // Try to get task from local queue first
                    let task = worker_queue.pop()
                        .or_else(|| {
                            // Try to steal from injector
                            loop {
                                match injector.steal_batch_and_pop(&worker_queue) {
                                    crossbeam_deque::Steal::Success(task) => return Some(task),
                                    crossbeam_deque::Steal::Empty => break,
                                    crossbeam_deque::Steal::Retry => continue,
                                }
                            }

                            // Try to steal from other workers
                            all_stealers.iter()
                                .filter(|s| !std::ptr::eq(s, &&stealer))
                                .find_map(|stealer| {
                                    loop {
                                        match stealer.steal() {
                                            crossbeam_deque::Steal::Success(task) => {
                                                stolen_count.fetch_add(1, Ordering::Relaxed);
                                                return Some(task);
                                            }
                                            crossbeam_deque::Steal::Empty => return None,
                                            crossbeam_deque::Steal::Retry => continue,
                                        }
                                    }
                                })
                        });

                    if let Some(task) = task {
                        *worker_state_clone.last_active.lock() = Instant::now();
                        task();
                        worker_state_clone.tasks_completed.fetch_add(1, Ordering::Relaxed);
                    } else {
                        // Check idle timeout
                        let last_active = *worker_state_clone.last_active.lock();
                        if last_active.elapsed() > idle_timeout {
                            // Worker has been idle too long
                            break;
                        }
                        
                        // Sleep briefly to avoid busy waiting
                        thread::park_timeout(Duration::from_millis(10));
                    }
                }
            })
            .expect("Failed to spawn worker thread");

        // Update worker state
        *worker_state.thread.lock() = Some(thread);
        self.workers.write().push(worker_state);
    }

    /// Calculate current load factor
    fn calculate_load_factor(&self) -> f64 {
        let active = self.active_count.load(Ordering::Relaxed) as f64;
        let queued = self.queued_count.load(Ordering::Relaxed) as f64;
        let workers = self.workers.read().len() as f64;

        if workers == 0.0 {
            return 1.0;
        }

        (active + queued * 0.5) / (workers * self.config.max_queue_size as f64)
    }

    /// Notify workers of new work
    fn notify_workers(&self) {
        let workers = self.workers.read();
        for worker in workers.iter() {
            if let Some(thread) = worker.thread.lock().as_ref() {
                thread.thread().unpark();
            }
        }
    }

    /// Start load monitoring thread
    fn start_load_monitor(&self) {
        let config = self.config.clone();
        let workers = self.workers.clone();
        let stats = self.stats.clone();
        let running = self.running.clone();
        let active_count = self.active_count.clone();
        let queued_count = self.queued_count.clone();
        let completed_count = self.completed_count.clone();
        let stolen_count = self.stolen_count.clone();

        let monitor = thread::Builder::new()
            .name("adaptive-pool-monitor".to_string())
            .spawn(move || {
                let mut last_completed = 0u64;
                let mut samples = VecDeque::with_capacity(10);

                while running.load(Ordering::Relaxed) {
                    thread::sleep(config.sampling_interval);

                    // Calculate statistics
                    let active = active_count.load(Ordering::Relaxed);
                    let queued = queued_count.load(Ordering::Relaxed);
                    let completed = completed_count.load(Ordering::Relaxed);
                    let stolen = stolen_count.load(Ordering::Relaxed);

                    let completed_delta = completed - last_completed;
                    last_completed = completed;

                    // Update running average
                    samples.push_back(completed_delta);
                    if samples.len() > 10 {
                        samples.pop_front();
                    }

                    let avg_throughput = samples.iter().sum::<u64>() as f64 / samples.len() as f64;
                    let load_factor = (active + queued) as f64 / config.max_queue_size as f64;

                    // Update statistics
                    let worker_count = workers.read().len();
                    let mut stats_guard = stats.write();
                    stats_guard.tasks_completed = completed;
                    stats_guard.tasks_queued = queued as u64;
                    stats_guard.tasks_stolen = stolen;
                    stats_guard.active_threads = active;
                    stats_guard.total_threads = worker_count;
                    stats_guard.queue_depth = queued;
                    stats_guard.load_factor = load_factor;
                    stats_guard.average_task_time_ms = if completed > 0 {
                        config.sampling_interval.as_millis() as f64 / avg_throughput
                    } else {
                        0.0
                    };
                    drop(stats_guard);

                    // Adaptive scaling logic handled elsewhere
                }
            })
            .expect("Failed to spawn monitor thread");

        *self.load_monitor.lock() = Some(monitor);
    }

    /// Warm up thread pool
    fn warm_up(&self) {
        let worker_count = self.workers.read().len();
        let warmup_tasks = worker_count * 10;

        for _ in 0..warmup_tasks {
            let _ = self.submit(WarmupTask);
        }

        // Wait for warmup to complete
        thread::sleep(Duration::from_millis(100));
    }

    /// Set NUMA affinity for thread (Linux only)
    #[cfg(target_os = "linux")]
    fn set_numa_affinity(worker_id: usize) {
        // This would use libnuma or hwloc for proper NUMA binding
        // For now, we'll use a simple CPU affinity approach
        use libc::{cpu_set_t, CPU_SET, CPU_ZERO, sched_setaffinity};
        
        unsafe {
            let mut cpu_set = std::mem::zeroed::<cpu_set_t>();
            CPU_ZERO(&mut cpu_set);
            CPU_SET(worker_id % num_cpus::get(), &mut cpu_set);
            sched_setaffinity(0, std::mem::size_of::<cpu_set_t>(), &cpu_set);
        }
    }

    #[cfg(not(target_os = "linux"))]
    fn set_numa_affinity(_worker_id: usize) {
        // NUMA affinity not supported on this platform
    }

    /// Get current pool statistics
    pub fn statistics(&self) -> PoolStatistics {
        self.stats.read().clone()
    }

    /// Shutdown the thread pool gracefully
    pub fn shutdown(&self) {
        self.running.store(false, Ordering::Relaxed);
        
        // Wake all workers
        self.notify_workers();

        // Wait for workers to finish
        let mut workers = self.workers.write();
        for worker in workers.drain(..) {
            if let Some(thread) = worker.thread.lock().take() {
                let _ = thread.join();
            }
        }

        // Stop monitor thread
        if let Some(monitor) = self.load_monitor.lock().take() {
            let _ = monitor.join();
        }
    }
}

/// Warmup task for pool initialization
struct WarmupTask;

impl Task for WarmupTask {
    type Output = ();
    
    fn execute(self) -> Self::Output {
        // Simple computation to warm up CPU caches
        let mut sum = 0u64;
        for i in 0..1000 {
            sum = sum.wrapping_add(i);
        }
        std::hint::black_box(sum);
    }
}

impl Drop for AdaptiveThreadPool {
    fn drop(&mut self) {
        self.shutdown();
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::sync::atomic::AtomicU32;

    struct TestTask {
        id: u32,
        counter: Arc<AtomicU32>,
    }

    impl Task for TestTask {
        type Output = u32;

        fn execute(self) -> Self::Output {
            self.counter.fetch_add(1, Ordering::Relaxed);
            thread::sleep(Duration::from_millis(10));
            self.id
        }
    }

    #[test]
    fn test_adaptive_pool_basic() {
        let pool = AdaptiveThreadPool::new();
        let counter = Arc::new(AtomicU32::new(0));

        // Submit tasks
        for i in 0..100 {
            pool.submit(TestTask {
                id: i,
                counter: counter.clone(),
            }).unwrap();
        }

        // Wait for completion
        thread::sleep(Duration::from_secs(2));

        assert_eq!(counter.load(Ordering::Relaxed), 100);
        
        let stats = pool.statistics();
        assert_eq!(stats.tasks_completed, 100);
    }

    #[test]
    fn test_backpressure() {
        let config = PoolConfig {
            max_queue_size: 10,
            ..Default::default()
        };
        let pool = AdaptiveThreadPool::with_config(config);

        // Fill queue
        for i in 0..10 {
            pool.submit(TestTask {
                id: i,
                counter: Arc::new(AtomicU32::new(0)),
            }).unwrap();
        }

        // This should fail due to backpressure
        let result = pool.submit(TestTask {
            id: 99,
            counter: Arc::new(AtomicU32::new(0)),
        });

        assert!(matches!(result, Err(BackpressureError::QueueFull { .. })));
    }

    #[test]
    fn test_work_stealing() {
        let pool = AdaptiveThreadPool::new();
        let counter = Arc::new(AtomicU32::new(0));

        // Submit many tasks to trigger work stealing
        for i in 0..1000 {
            pool.submit(TestTask {
                id: i,
                counter: counter.clone(),
            }).unwrap();
        }

        thread::sleep(Duration::from_secs(3));

        let stats = pool.statistics();
        assert!(stats.tasks_stolen > 0);
        assert_eq!(counter.load(Ordering::Relaxed), 1000);
    }
}