// WordStar format parser
// Supports WordStar document files (.WS, .WSD)

use std::collections::HashMap;
use crate::conversion::error::ConversionError;
use super::common::*;
use super::{FormatDetection, ConversionResult, FormatType};

/// WordStar control codes and formatting
const WS_SOFT_HYPHEN: u8 = 0x1E;
const WS_HARD_SPACE: u8 = 0x1F;
const WS_PRINT_CONTROLS: u8 = 0x1D;
const WS_DOT_COMMAND: u8 = 0x2E; // '.'

/// WordStar formatting control codes
#[derive(Debug, Clone)]
enum WordStarControl {
    Bold(bool),           // ^B (bold on/off)
    Underline(bool),      // ^S (underline on/off)
    Italic(bool),         // ^Y (italic on/off)
    Superscript(bool),    // ^T (superscript on/off)
    Subscript(bool),      // ^V (subscript on/off)
    PageBreak,            // ^L
    SoftReturn,           // ^M
    HardReturn,           // ^J
    Tab,                  // ^I
    DotCommand(String),   // .command
    PrintControl(u8),     // Other print controls
    Character(char),      // Regular character
}

/// WordStar document structure
#[derive(Debug)]
struct WordStarDocument {
    content: Vec<WordStarControl>,
    metadata: HashMap<String, String>,
}

impl WordStarDocument {
    fn new() -> Self {
        Self {
            content: Vec::new(),
            metadata: HashMap::new(),
        }
    }

    fn add_control(&mut self, control: WordStarControl) {
        self.content.push(control);
    }

    fn add_metadata(&mut self, key: String, value: String) {
        self.metadata.insert(key, value);
    }
}

/// Detect WordStar format
pub fn detect_wordstar_format(content: &[u8]) -> Result<FormatDetection, ConversionError> {
    if content.len() < 16 {
        return Ok(FormatDetection {
            format_type: FormatType::Unknown,
            confidence: 0.0,
            version: None,
            metadata: HashMap::new(),
        });
    }

    let mut confidence = 0.0;
    let mut metadata = extract_metadata(content, "WordStar");
    let mut version = None;

    // Check for WordStar characteristics
    let mut control_char_count = 0;
    let mut dot_command_count = 0;
    let mut text_char_count = 0;
    let mut line_count = 0;

    let sample_size = std::cmp::min(content.len(), 2048);
    let sample = &content[0..sample_size];

    let mut i = 0;
    while i < sample.len() {
        let byte = sample[i];
        
        match byte {
            // WordStar control characters
            0x01..=0x1F => {
                control_char_count += 1;
                match byte {
                    0x02 => confidence += 0.1, // ^B (Bold)
                    0x13 => confidence += 0.1, // ^S (Underline)
                    0x19 => confidence += 0.1, // ^Y (Italic)
                    0x0C => confidence += 0.1, // ^L (Page break)
                    0x0A => line_count += 1,   // Line feed
                    0x0D => {},                // Carriage return
                    0x09 => {},                // Tab
                    WS_SOFT_HYPHEN | WS_HARD_SPACE => confidence += 0.05,
                    _ => {}
                }
            },
            // Dot commands (WordStar formatting commands)
            WS_DOT_COMMAND if i == 0 || sample[i-1] == 0x0A => {
                dot_command_count += 1;
                confidence += 0.2;
                
                // Check for common WordStar dot commands
                if i + 3 < sample.len() {
                    let command = String::from_utf8_lossy(&sample[i..i+3]);
                    match command.as_ref() {
                        ".PL" | ".MT" | ".MB" | ".LM" | ".RM" | ".PA" => {
                            confidence += 0.3;
                        },
                        _ => {}
                    }
                }
            },
            // Regular text characters
            0x20..=0x7E => {
                text_char_count += 1;
            },
            // High-bit characters (might be WordStar extended)
            0x80..=0xFF => {
                // WordStar sometimes uses high-bit characters for formatting
                if byte & 0x7F >= 0x20 {
                    confidence += 0.02;
                }
            },
            _ => {}
        }
        
        i += 1;
    }

    // Calculate confidence based on characteristics
    let total_chars = control_char_count + text_char_count;
    if total_chars > 0 {
        let control_ratio = control_char_count as f32 / total_chars as f32;
        
        // WordStar typically has 5-15% control characters
        if control_ratio >= 0.05 && control_ratio <= 0.20 {
            confidence += 0.3;
        }
        
        // Presence of dot commands is a strong indicator
        if dot_command_count > 0 {
            confidence += 0.4;
        }
        
        // Line structure analysis
        if line_count > 0 {
            let avg_line_length = text_char_count / line_count.max(1);
            if avg_line_length >= 20 && avg_line_length <= 80 {
                confidence += 0.1; // Typical text line lengths
            }
        }
    }

    // Check for WordStar file patterns
    if is_text_content(sample) && control_char_count > 0 {
        confidence += 0.2;
    }

    // Metadata
    metadata.insert("control_chars".to_string(), control_char_count.to_string());
    metadata.insert("dot_commands".to_string(), dot_command_count.to_string());
    metadata.insert("text_chars".to_string(), text_char_count.to_string());
    metadata.insert("lines".to_string(), line_count.to_string());

    // Determine version (simplified detection)
    if confidence > 0.5 {
        version = Some("WordStar 3.x-7.x".to_string());
        if dot_command_count > 5 {
            version = Some("WordStar 4.x+".to_string());
        }
    }

    // Confidence thresholds
    if confidence > 1.0 {
        confidence = 0.95;
    } else if confidence < 0.3 {
        confidence = 0.0;
    }

    let format_type = if confidence > 0.5 {
        FormatType::WordStar
    } else {
        FormatType::Unknown
    };

    Ok(FormatDetection {
        format_type,
        confidence,
        version,
        metadata,
    })
}

/// Parse WordStar document content
fn parse_wordstar_content(content: &[u8]) -> Result<WordStarDocument, ConversionError> {
    let mut document = WordStarDocument::new();
    let mut i = 0;

    while i < content.len() {
        let byte = content[i];

        match byte {
            // Control characters
            0x02 => {
                // ^B - Bold toggle
                document.add_control(WordStarControl::Bold(true));
                i += 1;
            },
            0x13 => {
                // ^S - Underline toggle
                document.add_control(WordStarControl::Underline(true));
                i += 1;
            },
            0x19 => {
                // ^Y - Italic toggle
                document.add_control(WordStarControl::Italic(true));
                i += 1;
            },
            0x14 => {
                // ^T - Superscript toggle
                document.add_control(WordStarControl::Superscript(true));
                i += 1;
            },
            0x16 => {
                // ^V - Subscript toggle
                document.add_control(WordStarControl::Subscript(true));
                i += 1;
            },
            0x0C => {
                // ^L - Page break
                document.add_control(WordStarControl::PageBreak);
                i += 1;
            },
            0x0A => {
                // ^J - Hard return (line feed)
                document.add_control(WordStarControl::HardReturn);
                i += 1;
            },
            0x0D => {
                // ^M - Soft return (carriage return)
                document.add_control(WordStarControl::SoftReturn);
                i += 1;
            },
            0x09 => {
                // ^I - Tab
                document.add_control(WordStarControl::Tab);
                i += 1;
            },
            WS_DOT_COMMAND if i == 0 || (i > 0 && content[i-1] == 0x0A) => {
                // Dot command at beginning of line
                let (command, consumed) = parse_dot_command(content, i)?;
                document.add_control(WordStarControl::DotCommand(command));
                i += consumed;
            },
            0x01..=0x1F => {
                // Other control characters
                document.add_control(WordStarControl::PrintControl(byte));
                i += 1;
            },
            0x20..=0x7E => {
                // Regular ASCII character
                document.add_control(WordStarControl::Character(byte as char));
                i += 1;
            },
            0x80..=0xFF => {
                // Extended character (might be formatted)
                let char_code = byte & 0x7F;
                if char_code >= 0x20 {
                    document.add_control(WordStarControl::Character(char_code as char));
                } else {
                    document.add_control(WordStarControl::PrintControl(byte));
                }
                i += 1;
            },
            _ => {
                i += 1;
            }
        }
    }

    Ok(document)
}

/// Parse WordStar dot command
fn parse_dot_command(content: &[u8], start: usize) -> Result<(String, usize), ConversionError> {
    let mut end = start + 1;
    
    // Find end of line or command
    while end < content.len() && content[end] != 0x0A && content[end] != 0x0D {
        end += 1;
    }
    
    let command_bytes = &content[start..end];
    let command = String::from_utf8_lossy(command_bytes).trim().to_string();
    
    Ok((command, end - start))
}

/// Convert WordStar to Markdown
pub fn convert_wordstar_to_markdown(content: &[u8]) -> Result<ConversionResult, ConversionError> {
    let detection = detect_wordstar_format(content)?;
    if detection.format_type != FormatType::WordStar {
        return Err(ConversionError::UnsupportedFormat("Not a valid WordStar file".to_string()));
    }

    let document = parse_wordstar_content(content)?;
    let markdown_content = convert_to_markdown(&document)?;

    let mut metadata = detection.metadata;
    metadata.insert("controls_parsed".to_string(), document.content.len().to_string());

    let mut warnings = Vec::new();
    if markdown_content.len() < 50 {
        warnings.push("Very little content extracted - file may be mostly formatting codes".to_string());
    }

    Ok(ConversionResult {
        content: markdown_content,
        format: "markdown".to_string(),
        metadata,
        warnings,
    })
}

/// Convert WordStar to RTF
pub fn convert_wordstar_to_rtf(content: &[u8]) -> Result<ConversionResult, ConversionError> {
    let detection = detect_wordstar_format(content)?;
    if detection.format_type != FormatType::WordStar {
        return Err(ConversionError::UnsupportedFormat("Not a valid WordStar file".to_string()));
    }

    let document = parse_wordstar_content(content)?;
    let rtf_content = convert_to_rtf(&document)?;

    Ok(ConversionResult {
        content: rtf_content,
        format: "rtf".to_string(),
        metadata: detection.metadata,
        warnings: Vec::new(),
    })
}

/// Convert WordStar document to Markdown
fn convert_to_markdown(document: &WordStarDocument) -> Result<String, ConversionError> {
    let mut result = String::new();
    let mut bold_active = false;
    let mut italic_active = false;
    let mut underline_active = false;
    let mut in_paragraph = false;

    result.push_str("# WordStar Document\n\n");

    for control in &document.content {
        match control {
            WordStarControl::Bold(_) => {
                if bold_active {
                    result.push_str("**");
                    bold_active = false;
                } else {
                    result.push_str("**");
                    bold_active = true;
                }
            },
            WordStarControl::Italic(_) => {
                if italic_active {
                    result.push('*');
                    italic_active = false;
                } else {
                    result.push('*');
                    italic_active = true;
                }
            },
            WordStarControl::Underline(_) => {
                if underline_active {
                    result.push_str("</u>");
                    underline_active = false;
                } else {
                    result.push_str("<u>");
                    underline_active = true;
                }
            },
            WordStarControl::Superscript(_) => {
                result.push_str("^");
            },
            WordStarControl::Subscript(_) => {
                result.push_str("~");
            },
            WordStarControl::PageBreak => {
                result.push_str("\n\n---\n\n");
                in_paragraph = false;
            },
            WordStarControl::HardReturn => {
                result.push('\n');
                if in_paragraph {
                    result.push('\n');
                    in_paragraph = false;
                }
            },
            WordStarControl::SoftReturn => {
                result.push(' ');
            },
            WordStarControl::Tab => {
                result.push_str("    ");
            },
            WordStarControl::DotCommand(cmd) => {
                // Convert common dot commands to markdown
                if cmd.starts_with(".PA") {
                    result.push_str("\n\n---\n\n");
                } else if cmd.starts_with(".CE") {
                    result.push_str("\n<center>");
                } else {
                    result.push_str(&format!("\n<!-- {} -->\n", cmd));
                }
                in_paragraph = false;
            },
            WordStarControl::Character(ch) => {
                if !in_paragraph && !ch.is_whitespace() {
                    in_paragraph = true;
                }
                result.push(*ch);
            },
            WordStarControl::PrintControl(_) => {
                // Skip most print control codes
            },
        }
    }

    // Close any open formatting
    if bold_active {
        result.push_str("**");
    }
    if italic_active {
        result.push('*');
    }
    if underline_active {
        result.push_str("</u>");
    }

    Ok(clean_text_content(&result))
}

/// Convert WordStar document to RTF
fn convert_to_rtf(document: &WordStarDocument) -> Result<String, ConversionError> {
    let mut result = String::from("{\\rtf1\\ansi\\deff0 {\\fonttbl {\\f0 Times New Roman;}} \\f0\\fs24 ");
    
    for control in &document.content {
        match control {
            WordStarControl::Bold(_) => {
                result.push_str("\\b ");
            },
            WordStarControl::Italic(_) => {
                result.push_str("\\i ");
            },
            WordStarControl::Underline(_) => {
                result.push_str("\\ul ");
            },
            WordStarControl::Superscript(_) => {
                result.push_str("\\super ");
            },
            WordStarControl::Subscript(_) => {
                result.push_str("\\sub ");
            },
            WordStarControl::PageBreak => {
                result.push_str("\\page ");
            },
            WordStarControl::HardReturn => {
                result.push_str("\\par ");
            },
            WordStarControl::SoftReturn => {
                result.push_str("\\line ");
            },
            WordStarControl::Tab => {
                result.push_str("\\tab ");
            },
            WordStarControl::Character(ch) => {
                match ch {
                    '\\' => result.push_str("\\\\"),
                    '{' => result.push_str("\\{"),
                    '}' => result.push_str("\\}"),
                    _ => result.push(*ch),
                }
            },
            _ => {
                // Skip other controls in RTF
            }
        }
    }
    
    result.push('}');
    Ok(result)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_wordstar_detection() {
        let content = b"\x02Hello\x02 World\n.PL 66\nThis is a test.";
        let detection = detect_wordstar_format(content).unwrap();
        assert_eq!(detection.format_type, FormatType::WordStar);
        assert!(detection.confidence > 0.5);
    }

    #[test]
    fn test_dot_command_parsing() {
        let content = b".PL 66\n";
        let (command, consumed) = parse_dot_command(content, 0).unwrap();
        assert_eq!(command, ".PL 66");
        assert_eq!(consumed, 6);
    }

    #[test]
    fn test_control_character_detection() {
        let content = b"Normal text \x02bold\x02 and \x13underline\x13.";
        let document = parse_wordstar_content(content).unwrap();
        assert!(!document.content.is_empty());
    }
}
