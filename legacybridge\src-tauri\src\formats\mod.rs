// Legacy Format Management System
// Provides modular format detection and conversion for legacy file types

pub mod common;
pub mod doc;
pub mod wordperf;
pub mod dbase;
pub mod wordstar;
pub mod lotus;

use std::collections::HashMap;
use std::path::Path;
use crate::conversion::error::ConversionError;

/// Supported legacy format types
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub enum FormatType {
    Doc,
    WordPerfect,
    DBase,
    WordStar,
    Lotus123,
    Unknown,
}

/// Format detection result
#[derive(Debug, <PERSON>lone)]
pub struct FormatDetection {
    pub format_type: FormatType,
    pub confidence: f32,
    pub version: Option<String>,
    pub metadata: HashMap<String, String>,
}

/// Conversion result for legacy formats
#[derive(Debug, Clone)]
pub struct ConversionResult {
    pub content: String,
    pub format: String,
    pub metadata: HashMap<String, String>,
    pub warnings: Vec<String>,
}

/// Main format manager for legacy file types
pub struct FormatManager {
    enabled_formats: HashMap<FormatType, bool>,
}

impl FormatManager {
    /// Create a new format manager with default settings
    pub fn new() -> Self {
        let mut enabled_formats = HashMap::new();
        enabled_formats.insert(FormatType::Doc, true);
        enabled_formats.insert(FormatType::WordPerfect, true);
        enabled_formats.insert(FormatType::DBase, true);
        enabled_formats.insert(FormatType::WordStar, true);
        enabled_formats.insert(FormatType::Lotus123, true);
        
        Self { enabled_formats }
    }

    /// Create format manager with specific feature flags
    pub fn with_features(features: HashMap<FormatType, bool>) -> Self {
        Self { enabled_formats: features }
    }

    /// Detect format from file content
    pub fn detect_format(&self, content: &[u8]) -> Result<FormatDetection, ConversionError> {
        // Try each format detector in order of confidence
        if self.is_enabled(&FormatType::Doc) {
            if let Ok(detection) = doc::detect_doc_format(content) {
                if detection.confidence > 0.8 {
                    return Ok(detection);
                }
            }
        }

        if self.is_enabled(&FormatType::WordPerfect) {
            if let Ok(detection) = wordperf::detect_wordperfect_format(content) {
                if detection.confidence > 0.8 {
                    return Ok(detection);
                }
            }
        }

        if self.is_enabled(&FormatType::DBase) {
            if let Ok(detection) = dbase::detect_dbase_format(content) {
                if detection.confidence > 0.8 {
                    return Ok(detection);
                }
            }
        }

        if self.is_enabled(&FormatType::WordStar) {
            if let Ok(detection) = wordstar::detect_wordstar_format(content) {
                if detection.confidence > 0.8 {
                    return Ok(detection);
                }
            }
        }

        if self.is_enabled(&FormatType::Lotus123) {
            if let Ok(detection) = lotus::detect_lotus_format(content) {
                if detection.confidence > 0.8 {
                    return Ok(detection);
                }
            }
        }

        Ok(FormatDetection {
            format_type: FormatType::Unknown,
            confidence: 0.0,
            version: None,
            metadata: HashMap::new(),
        })
    }

    /// Detect format from file path
    pub fn detect_format_from_path(&self, path: &Path) -> Result<FormatDetection, ConversionError> {
        let extension = path.extension()
            .and_then(|ext| ext.to_str())
            .unwrap_or("")
            .to_lowercase();

        let (format_type, confidence) = match extension.as_str() {
            "doc" => (FormatType::Doc, 0.9),
            "wpd" | "wp" | "wp5" => (FormatType::WordPerfect, 0.9),
            "dbf" | "db3" | "db4" => (FormatType::DBase, 0.9),
            "ws" | "wsd" => (FormatType::WordStar, 0.9),
            "wk1" | "wks" | "123" => (FormatType::Lotus123, 0.9),
            _ => (FormatType::Unknown, 0.0),
        };

        let mut metadata = HashMap::new();
        metadata.insert("file_extension".to_string(), extension);
        
        Ok(FormatDetection {
            format_type,
            confidence,
            version: None,
            metadata,
        })
    }

    /// Convert legacy format to markdown
    pub fn convert_to_markdown(&self, content: &[u8], format_type: &FormatType) -> Result<ConversionResult, ConversionError> {
        if !self.is_enabled(format_type) {
            return Err(ConversionError::UnsupportedFormat(format!("{:?} format is disabled", format_type)));
        }

        match format_type {
            FormatType::Doc => doc::convert_doc_to_markdown(content),
            FormatType::WordPerfect => wordperf::convert_wordperfect_to_markdown(content),
            FormatType::DBase => dbase::convert_dbase_to_markdown(content),
            FormatType::WordStar => wordstar::convert_wordstar_to_markdown(content),
            FormatType::Lotus123 => lotus::convert_lotus_to_markdown(content),
            FormatType::Unknown => Err(ConversionError::UnsupportedFormat("Unknown format".to_string())),
        }
    }

    /// Convert legacy format to RTF
    pub fn convert_to_rtf(&self, content: &[u8], format_type: &FormatType) -> Result<ConversionResult, ConversionError> {
        if !self.is_enabled(format_type) {
            return Err(ConversionError::UnsupportedFormat(format!("{:?} format is disabled", format_type)));
        }

        match format_type {
            FormatType::Doc => doc::convert_doc_to_rtf(content),
            FormatType::WordPerfect => wordperf::convert_wordperfect_to_rtf(content),
            FormatType::DBase => dbase::convert_dbase_to_rtf(content),
            FormatType::WordStar => wordstar::convert_wordstar_to_rtf(content),
            FormatType::Lotus123 => lotus::convert_lotus_to_rtf(content),
            FormatType::Unknown => Err(ConversionError::UnsupportedFormat("Unknown format".to_string())),
        }
    }

    /// Check if a format is enabled
    fn is_enabled(&self, format_type: &FormatType) -> bool {
        self.enabled_formats.get(format_type).copied().unwrap_or(false)
    }

    /// Get list of supported formats
    pub fn supported_formats(&self) -> Vec<FormatType> {
        self.enabled_formats
            .iter()
            .filter_map(|(format_type, &enabled)| {
                if enabled {
                    Some(format_type.clone())
                } else {
                    None
                }
            })
            .collect()
    }
}

impl Default for FormatManager {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_format_manager_creation() {
        let manager = FormatManager::new();
        assert!(manager.is_enabled(&FormatType::Doc));
        assert!(manager.is_enabled(&FormatType::WordPerfect));
    }

    #[test]
    fn test_format_detection_from_path() {
        let manager = FormatManager::new();
        let path = Path::new("test.doc");
        let detection = manager.detect_format_from_path(path).unwrap();
        assert_eq!(detection.format_type, FormatType::Doc);
        assert!(detection.confidence > 0.8);
    }

    #[test]
    fn test_feature_flags() {
        let mut features = HashMap::new();
        features.insert(FormatType::Doc, true);
        features.insert(FormatType::WordPerfect, false);
        
        let manager = FormatManager::with_features(features);
        assert!(manager.is_enabled(&FormatType::Doc));
        assert!(!manager.is_enabled(&FormatType::WordPerfect));
    }
}
