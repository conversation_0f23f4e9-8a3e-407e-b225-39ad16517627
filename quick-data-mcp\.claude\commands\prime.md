## RUN
eza . --tree --level 5 --git-ignore

## READ
@README.md

## Remember
`specs/` - is where we plan new engineering work 
`ai_docs/` - is where useful reference material exists to guide our work
`arch-modular-mcp/` - production-ready modular MCP server architecture
`arch-modular-mcp/src/mcp_server/` - core server implementation with tools, resources, prompts
`arch-modular-mcp/tests/` - comprehensive test suite (19 tests)