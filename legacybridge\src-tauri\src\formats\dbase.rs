// dBase III/IV format parser
// Supports dBase III, dBase IV, and compatible database files

use std::collections::HashMap;
use crate::conversion::error::ConversionError;
use super::common::*;
use super::{FormatDetection, ConversionResult, FormatType};

/// dBase file type signatures
const DBASE3_SIGNATURE: u8 = 0x03;
const DBASE3_MEMO: u8 = 0x83;
const DBASE4_SIGNATURE: u8 = 0x04;
const DBASE4_MEMO: u8 = 0x8B;
const DBASE5_SIGNATURE: u8 = 0x05;
const FOXPRO_SIGNATURE: u8 = 0x30;

/// dBase field types
#[derive(Debug, Clone, PartialEq)]
enum FieldType {
    Character,
    Numeric,
    Logical,
    Date,
    Memo,
    Float,
    Binary,
    General,
    Picture,
    Unknown(char),
}

impl FieldType {
    fn from_char(c: char) -> Self {
        match c {
            'C' => FieldType::Character,
            'N' => FieldType::Numeric,
            'L' => FieldType::Logical,
            'D' => FieldType::Date,
            'M' => FieldType::Memo,
            'F' => FieldType::Float,
            'B' => FieldType::Binary,
            'G' => FieldType::General,
            'P' => FieldType::Picture,
            _ => FieldType::Unknown(c),
        }
    }

    fn to_string(&self) -> String {
        match self {
            FieldType::Character => "Character".to_string(),
            FieldType::Numeric => "Numeric".to_string(),
            FieldType::Logical => "Logical".to_string(),
            FieldType::Date => "Date".to_string(),
            FieldType::Memo => "Memo".to_string(),
            FieldType::Float => "Float".to_string(),
            FieldType::Binary => "Binary".to_string(),
            FieldType::General => "General".to_string(),
            FieldType::Picture => "Picture".to_string(),
            FieldType::Unknown(c) => format!("Unknown({})", c),
        }
    }
}

/// dBase file header
#[derive(Debug)]
struct DBaseHeader {
    version: u8,
    last_update: [u8; 3], // YY MM DD
    record_count: u32,
    header_length: u16,
    record_length: u16,
    reserved: [u8; 16],
    table_flags: u8,
    code_page: u8,
}

impl DBaseHeader {
    fn parse(content: &[u8]) -> Result<Self, ConversionError> {
        if content.len() < 32 {
            return Err(ConversionError::InvalidInput("dBase header too short".to_string()));
        }

        let mut last_update = [0u8; 3];
        last_update.copy_from_slice(&content[1..4]);

        let mut reserved = [0u8; 16];
        reserved.copy_from_slice(&content[12..28]);

        Ok(DBaseHeader {
            version: content[0],
            last_update,
            record_count: bytes_to_u32_le(&content[4..8]),
            header_length: bytes_to_u16_le(&content[8..10]),
            record_length: bytes_to_u16_le(&content[10..12]),
            reserved,
            table_flags: content[28],
            code_page: content[29],
        })
    }

    fn is_valid(&self) -> bool {
        matches!(self.version, 
            DBASE3_SIGNATURE | DBASE3_MEMO | DBASE4_SIGNATURE | 
            DBASE4_MEMO | DBASE5_SIGNATURE | FOXPRO_SIGNATURE
        ) && self.header_length >= 32 && self.record_length > 0
    }

    fn version_string(&self) -> String {
        match self.version {
            DBASE3_SIGNATURE => "dBase III".to_string(),
            DBASE3_MEMO => "dBase III with memo".to_string(),
            DBASE4_SIGNATURE => "dBase IV".to_string(),
            DBASE4_MEMO => "dBase IV with memo".to_string(),
            DBASE5_SIGNATURE => "dBase 5".to_string(),
            FOXPRO_SIGNATURE => "FoxPro".to_string(),
            _ => format!("Unknown (0x{:02X})", self.version),
        }
    }

    fn has_memo(&self) -> bool {
        matches!(self.version, DBASE3_MEMO | DBASE4_MEMO)
    }

    fn last_update_string(&self) -> String {
        let year = if self.last_update[0] < 80 { 2000 + self.last_update[0] as u16 } else { 1900 + self.last_update[0] as u16 };
        format!("{:04}-{:02}-{:02}", year, self.last_update[1], self.last_update[2])
    }
}

/// dBase field descriptor
#[derive(Debug, Clone)]
struct FieldDescriptor {
    name: String,
    field_type: FieldType,
    length: u8,
    decimal_places: u8,
    flags: u8,
}

impl FieldDescriptor {
    fn parse(data: &[u8]) -> Result<Self, ConversionError> {
        if data.len() < 32 {
            return Err(ConversionError::InvalidInput("Field descriptor too short".to_string()));
        }

        let name = extract_null_terminated_string(&data[0..11]);
        let field_type_char = data[11] as char;
        let field_type = FieldType::from_char(field_type_char);

        Ok(FieldDescriptor {
            name,
            field_type,
            length: data[16],
            decimal_places: data[17],
            flags: data[18],
        })
    }

    fn is_valid(&self) -> bool {
        !self.name.is_empty() && self.length > 0
    }
}

/// dBase record data
#[derive(Debug)]
struct DBaseRecord {
    deleted: bool,
    fields: Vec<String>,
}

impl DBaseRecord {
    fn parse(data: &[u8], field_descriptors: &[FieldDescriptor]) -> Result<Self, ConversionError> {
        if data.is_empty() {
            return Err(ConversionError::InvalidInput("Record data is empty".to_string()));
        }

        let deleted = data[0] == b'*';
        let mut fields = Vec::new();
        let mut offset = 1; // Skip deletion flag

        for field_desc in field_descriptors {
            if offset + field_desc.length as usize > data.len() {
                break;
            }

            let field_data = &data[offset..offset + field_desc.length as usize];
            let field_value = parse_field_value(field_data, &field_desc.field_type);
            fields.push(field_value);
            offset += field_desc.length as usize;
        }

        Ok(DBaseRecord { deleted, fields })
    }
}

/// Parse field value based on type
fn parse_field_value(data: &[u8], field_type: &FieldType) -> String {
    let raw_string = String::from_utf8_lossy(data).trim().to_string();

    match field_type {
        FieldType::Character => raw_string,
        FieldType::Numeric | FieldType::Float => {
            if raw_string.is_empty() {
                "0".to_string()
            } else {
                raw_string
            }
        },
        FieldType::Logical => {
            match raw_string.to_uppercase().as_str() {
                "T" | "Y" | "1" => "true".to_string(),
                "F" | "N" | "0" => "false".to_string(),
                _ => "null".to_string(),
            }
        },
        FieldType::Date => {
            if raw_string.len() == 8 && raw_string.chars().all(|c| c.is_ascii_digit()) {
                format!("{}-{}-{}", &raw_string[0..4], &raw_string[4..6], &raw_string[6..8])
            } else {
                raw_string
            }
        },
        FieldType::Memo => {
            format!("(Memo: {})", raw_string)
        },
        _ => raw_string,
    }
}

/// Detect dBase format
pub fn detect_dbase_format(content: &[u8]) -> Result<FormatDetection, ConversionError> {
    if content.len() < 32 {
        return Ok(FormatDetection {
            format_type: FormatType::Unknown,
            confidence: 0.0,
            version: None,
            metadata: HashMap::new(),
        });
    }

    let header = DBaseHeader::parse(content)?;
    if !header.is_valid() {
        return Ok(FormatDetection {
            format_type: FormatType::Unknown,
            confidence: 0.0,
            version: None,
            metadata: HashMap::new(),
        });
    }

    let mut confidence = 0.8;
    let mut metadata = extract_metadata(content, "dBase");

    // Add header information
    metadata.insert("version".to_string(), header.version_string());
    metadata.insert("record_count".to_string(), header.record_count.to_string());
    metadata.insert("header_length".to_string(), header.header_length.to_string());
    metadata.insert("record_length".to_string(), header.record_length.to_string());
    metadata.insert("last_update".to_string(), header.last_update_string());
    metadata.insert("has_memo".to_string(), header.has_memo().to_string());

    // Validate field descriptors to increase confidence
    if let Ok(fields) = parse_field_descriptors(content, &header) {
        if !fields.is_empty() && fields.iter().all(|f| f.is_valid()) {
            confidence = 0.95;
            metadata.insert("field_count".to_string(), fields.len().to_string());
        }
    }

    Ok(FormatDetection {
        format_type: FormatType::DBase,
        confidence,
        version: Some(header.version_string()),
        metadata,
    })
}

/// Parse field descriptors from dBase header
fn parse_field_descriptors(content: &[u8], header: &DBaseHeader) -> Result<Vec<FieldDescriptor>, ConversionError> {
    let mut fields = Vec::new();
    let mut offset = 32; // Start after main header

    while offset + 32 <= header.header_length as usize && offset + 32 <= content.len() {
        let field_data = &content[offset..offset + 32];
        
        // Check for end of field descriptors (0x0D marker)
        if field_data[0] == 0x0D {
            break;
        }

        if let Ok(field) = FieldDescriptor::parse(field_data) {
            if field.is_valid() {
                fields.push(field);
            }
        }
        
        offset += 32;
    }

    Ok(fields)
}

/// Convert dBase to Markdown
pub fn convert_dbase_to_markdown(content: &[u8]) -> Result<ConversionResult, ConversionError> {
    let detection = detect_dbase_format(content)?;
    if detection.format_type != FormatType::DBase {
        return Err(ConversionError::UnsupportedFormat("Not a valid dBase file".to_string()));
    }

    let header = DBaseHeader::parse(content)?;
    let field_descriptors = parse_field_descriptors(content, &header)?;

    if field_descriptors.is_empty() {
        return Err(ConversionError::InvalidInput("No valid field descriptors found".to_string()));
    }

    // Generate markdown table
    let mut markdown = String::new();
    markdown.push_str(&format!("# dBase Database: {}\n\n", header.version_string()));
    markdown.push_str(&format!("**Records:** {} | **Last Updated:** {}\n\n", 
                              header.record_count, header.last_update_string()));

    // Table header
    markdown.push('|');
    for field in &field_descriptors {
        markdown.push_str(&format!(" {} |", field.name));
    }
    markdown.push('\n');

    // Table separator
    markdown.push('|');
    for _ in &field_descriptors {
        markdown.push_str(" --- |");
    }
    markdown.push('\n');

    // Parse records
    let records_start = header.header_length as usize;
    let mut record_offset = records_start;
    let mut records_processed = 0;
    let max_records = std::cmp::min(header.record_count as usize, 100); // Limit for markdown

    while record_offset + header.record_length as usize <= content.len() && 
          records_processed < max_records {
        
        let record_data = &content[record_offset..record_offset + header.record_length as usize];
        
        if let Ok(record) = DBaseRecord::parse(record_data, &field_descriptors) {
            if !record.deleted {
                markdown.push('|');
                for field_value in &record.fields {
                    let escaped_value = field_value.replace('|', "\\|").replace('\n', " ");
                    markdown.push_str(&format!(" {} |", escaped_value));
                }
                markdown.push('\n');
            }
        }
        
        record_offset += header.record_length as usize;
        records_processed += 1;
    }

    if records_processed >= 100 && header.record_count > 100 {
        markdown.push_str(&format!("\n*Note: Showing first 100 of {} records*\n", header.record_count));
    }

    // Add field information
    markdown.push_str("\n## Field Definitions\n\n");
    for field in &field_descriptors {
        markdown.push_str(&format!("- **{}**: {} (Length: {}", 
                                  field.name, field.field_type.to_string(), field.length));
        if field.decimal_places > 0 {
            markdown.push_str(&format!(", Decimals: {}", field.decimal_places));
        }
        markdown.push_str(")\n");
    }

    let mut metadata = detection.metadata;
    metadata.insert("records_processed".to_string(), records_processed.to_string());
    metadata.insert("fields_found".to_string(), field_descriptors.len().to_string());

    let mut warnings = Vec::new();
    if records_processed >= 100 && header.record_count > 100 {
        warnings.push(format!("Only first 100 of {} records shown in markdown output", header.record_count));
    }

    Ok(ConversionResult {
        content: markdown,
        format: "markdown".to_string(),
        metadata,
        warnings,
    })
}

/// Convert dBase to RTF
pub fn convert_dbase_to_rtf(content: &[u8]) -> Result<ConversionResult, ConversionError> {
    let markdown_result = convert_dbase_to_markdown(content)?;
    
    // Convert markdown table to RTF table (simplified)
    let rtf_content = markdown_result.content
        .replace('\n', "\\par ")
        .replace('|', "\\cell ")
        .replace("---", "");

    let rtf_document = format!(
        "{{\\rtf1\\ansi\\deff0 {{\\fonttbl {{\\f0 Times New Roman;}}}} \\f0\\fs20 {}}}",
        rtf_content
    );

    Ok(ConversionResult {
        content: rtf_document,
        format: "rtf".to_string(),
        metadata: markdown_result.metadata,
        warnings: markdown_result.warnings,
    })
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_dbase_header_parsing() {
        let mut header_data = vec![0u8; 32];
        header_data[0] = DBASE3_SIGNATURE;
        header_data[1] = 23; // Year (2023)
        header_data[2] = 12; // Month
        header_data[3] = 25; // Day
        header_data[8] = 64; // Header length (little-endian)
        header_data[10] = 50; // Record length

        let header = DBaseHeader::parse(&header_data).unwrap();
        assert!(header.is_valid());
        assert_eq!(header.version_string(), "dBase III");
    }

    #[test]
    fn test_dbase_detection() {
        let mut content = vec![0u8; 64];
        content[0] = DBASE4_SIGNATURE;
        content[8] = 64; // Header length
        content[10] = 50; // Record length

        let detection = detect_dbase_format(&content).unwrap();
        assert_eq!(detection.format_type, FormatType::DBase);
        assert!(detection.confidence > 0.7);
    }

    #[test]
    fn test_field_type_parsing() {
        assert_eq!(FieldType::from_char('C'), FieldType::Character);
        assert_eq!(FieldType::from_char('N'), FieldType::Numeric);
        assert_eq!(FieldType::from_char('L'), FieldType::Logical);
        assert_eq!(FieldType::from_char('D'), FieldType::Date);
    }
}
