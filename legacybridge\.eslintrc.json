{"extends": ["next/core-web-vitals", "plugin:@typescript-eslint/recommended", "plugin:@typescript-eslint/recommended-requiring-type-checking"], "parser": "@typescript-eslint/parser", "parserOptions": {"project": "./tsconfig.json", "tsconfigRootDir": "."}, "plugins": ["@typescript-eslint"], "rules": {"@typescript-eslint/no-explicit-any": "error", "@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_", "varsIgnorePattern": "^_"}], "@typescript-eslint/explicit-function-return-type": ["warn", {"allowExpressions": true, "allowTypedFunctionExpressions": true}], "@typescript-eslint/no-floating-promises": "error", "@typescript-eslint/no-misused-promises": "error", "@typescript-eslint/await-thenable": "error", "@typescript-eslint/no-unnecessary-type-assertion": "error", "@typescript-eslint/prefer-nullish-coalescing": "error", "@typescript-eslint/prefer-optional-chain": "error", "@typescript-eslint/strict-boolean-expressions": ["error", {"allowString": false, "allowNumber": false, "allowNullableObject": false}], "@typescript-eslint/no-non-null-assertion": "error", "@typescript-eslint/consistent-type-imports": ["error", {"prefer": "type-imports"}], "@typescript-eslint/no-unnecessary-condition": "error", "@typescript-eslint/no-unsafe-assignment": "error", "@typescript-eslint/no-unsafe-call": "error", "@typescript-eslint/no-unsafe-member-access": "error", "@typescript-eslint/no-unsafe-return": "error", "complexity": ["error", 10], "max-lines": ["error", 300], "max-lines-per-function": ["error", 50], "max-depth": ["error", 4], "max-nested-callbacks": ["error", 3], "no-console": ["warn", {"allow": ["warn", "error"]}]}, "overrides": [{"files": ["*.tsx"], "rules": {"@typescript-eslint/explicit-function-return-type": "off"}}]}