// MCP Legacy Format Service
// Handles conversion of legacy file formats like DOC and WordPerfect

import fs from 'fs';
import path from 'path';
import { exec } from 'child_process';
import util from 'util';
import { MCPConfig } from '../utils/mcp-config';
import { MCPLogger } from '../utils/mcp-logger';
import { ConversionError, ValidationError } from '../middleware/mcp-error-handler';

const execPromise = util.promisify(exec);

export interface LegacyFormatOptions {
  preserveFormatting?: boolean;
  includeMetadata?: boolean;
  extractImages?: boolean;
  imageOutputDir?: string;
}

export class MCPLegacyFormatService {
  constructor(
    private config: MCPConfig,
    private logger: MCPLogger
  ) {
    // Ensure the required tools are installed
    this.checkDependencies();
  }

  // Convert DOC content to Markdown
  async convertDocToMarkdown(content: string, options?: LegacyFormatOptions): Promise<any> {
    this.logger.info('Converting DOC to Markdown', { contentLength: content.length });
    
    if (!this.config.legacyFormats.enableDOC) {
      throw new ValidationError('DOC conversion is not enabled');
    }
    
    try {
      // DOC content is expected to be base64 encoded
      const buffer = Buffer.from(content, 'base64');
      
      // Create temporary file
      const tempDir = path.join(process.cwd(), 'temp');
      if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
      }
      
      const tempFile = path.join(tempDir, `temp_${Date.now()}.doc`);
      await fs.promises.writeFile(tempFile, buffer);
      
      // Convert using the appropriate method
      const result = await this.convertDocFileToMarkdown(tempFile, options);
      
      // Clean up temporary file
      await fs.promises.unlink(tempFile);
      
      return result;
    } catch (error) {
      this.logger.error('DOC to Markdown conversion failed', error);
      
      if (error instanceof ValidationError || error instanceof ConversionError) {
        throw error;
      }
      
      throw new ConversionError('Failed to convert DOC to Markdown', {
        originalError: error instanceof Error ? error.message : String(error)
      });
    }
  }

  // Convert WordPerfect content to Markdown
  async convertWordPerfectToMarkdown(content: string, options?: LegacyFormatOptions): Promise<any> {
    this.logger.info('Converting WordPerfect to Markdown', { contentLength: content.length });
    
    if (!this.config.legacyFormats.enableWordPerfect) {
      throw new ValidationError('WordPerfect conversion is not enabled');
    }
    
    try {
      // WordPerfect content is expected to be base64 encoded
      const buffer = Buffer.from(content, 'base64');
      
      // Create temporary file
      const tempDir = path.join(process.cwd(), 'temp');
      if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
      }
      
      const tempFile = path.join(tempDir, `temp_${Date.now()}.wpd`);
      await fs.promises.writeFile(tempFile, buffer);
      
      // Convert using the appropriate method
      const result = await this.convertWordPerfectFileToMarkdown(tempFile, options);
      
      // Clean up temporary file
      await fs.promises.unlink(tempFile);
      
      return result;
    } catch (error) {
      this.logger.error('WordPerfect to Markdown conversion failed', error);
      
      if (error instanceof ValidationError || error instanceof ConversionError) {
        throw error;
      }
      
      throw new ConversionError('Failed to convert WordPerfect to Markdown', {
        originalError: error instanceof Error ? error.message : String(error)
      });
    }
  }

  // Convert DOC file to Markdown
  async convertDocFileToMarkdown(filePath: string, options?: LegacyFormatOptions): Promise<any> {
    this.logger.info('Converting DOC file to Markdown', { filePath });
    
    if (!this.config.legacyFormats.enableDOC) {
      throw new ValidationError('DOC conversion is not enabled');
    }
    
    try {
      // Check if file exists
      if (!fs.existsSync(filePath)) {
        throw new ValidationError(`File not found: ${filePath}`);
      }
      
      // Generate output file path
      const outputFileName = path.basename(filePath, path.extname(filePath)) + '.md';
      const outputDir = path.join(process.cwd(), 'output');
      
      // Create output directory if it doesn't exist
      if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
      }
      
      const outputPath = path.join(outputDir, outputFileName);
      
      // Convert DOC to Markdown using LibreOffice
      await this.convertWithLibreOffice(filePath, outputPath, options);
      
      // Read the converted content
      const content = await fs.promises.readFile(outputPath, 'utf8');
      
      return {
        content,
        metadata: {
          convertedAt: new Date().toISOString(),
          originalFormat: 'DOC',
          preservedFormatting: options?.preserveFormatting || false
        },
        outputPath,
        outputFileName
      };
    } catch (error) {
      this.logger.error('DOC file conversion failed', error);
      
      if (error instanceof ValidationError || error instanceof ConversionError) {
        throw error;
      }
      
      throw new ConversionError('Failed to convert DOC to Markdown', {
        filePath,
        originalError: error instanceof Error ? error.message : String(error)
      });
    }
  }

  // Convert WordPerfect file to Markdown
  async convertWordPerfectFileToMarkdown(filePath: string, options?: LegacyFormatOptions): Promise<any> {
    this.logger.info('Converting WordPerfect file to Markdown', { filePath });
    
    if (!this.config.legacyFormats.enableWordPerfect) {
      throw new ValidationError('WordPerfect conversion is not enabled');
    }
    
    try {
      // Check if file exists
      if (!fs.existsSync(filePath)) {
        throw new ValidationError(`File not found: ${filePath}`);
      }
      
      // Generate output file path
      const outputFileName = path.basename(filePath, path.extname(filePath)) + '.md';
      const outputDir = path.join(process.cwd(), 'output');
      
      // Create output directory if it doesn't exist
      if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
      }
      
      const outputPath = path.join(outputDir, outputFileName);
      
      // Convert WordPerfect to Markdown using LibreOffice
      await this.convertWithLibreOffice(filePath, outputPath, options);
      
      // Read the converted content
      const content = await fs.promises.readFile(outputPath, 'utf8');
      
      return {
        content,
        metadata: {
          convertedAt: new Date().toISOString(),
          originalFormat: 'WordPerfect',
          preservedFormatting: options?.preserveFormatting || false
        },
        outputPath,
        outputFileName
      };
    } catch (error) {
      this.logger.error('WordPerfect file conversion failed', error);
      
      if (error instanceof ValidationError || error instanceof ConversionError) {
        throw error;
      }
      
      throw new ConversionError('Failed to convert WordPerfect to Markdown', {
        filePath,
        originalError: error instanceof Error ? error.message : String(error)
      });
    }
  }

  // Convert file using LibreOffice
  private async convertWithLibreOffice(inputPath: string, outputPath: string, options?: LegacyFormatOptions): Promise<void> {
    try {
      // First convert to ODT format
      const tempDir = path.dirname(outputPath);
      const tempOdtPath = path.join(tempDir, `${path.basename(inputPath, path.extname(inputPath))}.odt`);
      
      // Use LibreOffice to convert to ODT
      await execPromise(`libreoffice --headless --convert-to odt --outdir "${tempDir}" "${inputPath}"`);
      
      // Then convert ODT to Markdown using lightweight conversion
      await this.convertOdtToMarkdown(tempOdtPath, outputPath, options);
      
      // Clean up temporary ODT file
      await fs.promises.unlink(tempOdtPath);
      
      // Extract images if requested
      if (options?.extractImages) {
        await this.extractImages(tempOdtPath, options.imageOutputDir || path.dirname(outputPath));
      }
    } catch (error) {
      throw new ConversionError('Failed to convert file with LibreOffice', {
        originalError: error instanceof Error ? error.message : String(error)
      });
    }
  }

  // Extract images from ODT file
  private async extractImages(odtPath: string, outputDir: string): Promise<void> {
    try {
      // Create image output directory if it doesn't exist
      if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
      }
      
      // Extract images using unzip
      await execPromise(`unzip -j "${odtPath}" "Pictures/*" -d "${outputDir}"`);
    } catch (error) {
      this.logger.error('Failed to extract images', error);
      // Don't throw, just log the error
    }
  }

  // Check if required dependencies are installed
  private async checkDependencies(): Promise<void> {
    try {
      // Check for LibreOffice
      await execPromise('libreoffice --version');
      
      // Note: We use lightweight conversion instead of Pandoc to keep the solution small
      
      this.logger.info('All required dependencies are installed');
    } catch (error) {
      this.logger.warn('Some dependencies are missing', error);
      
      // Disable legacy format conversion if dependencies are missing
      this.config.legacyFormats.enableDOC = false;
      this.config.legacyFormats.enableWordPerfect = false;
      
      this.logger.warn('Legacy format conversion has been disabled due to missing dependencies');
    }
  }

  /**
   * Lightweight ODT to Markdown conversion (replaces Pandoc)
   * This is a simple conversion that extracts text content from ODT files
   */
  private async convertOdtToMarkdown(odtPath: string, outputPath: string, options?: any): Promise<void> {
    try {
      // For now, use LibreOffice to convert ODT to plain text, then format as Markdown
      const txtPath = odtPath.replace('.odt', '.txt');
      await execPromise(`libreoffice --headless --convert-to txt --outdir "${path.dirname(txtPath)}" "${odtPath}"`);

      // Read the text content
      const textContent = await fs.promises.readFile(txtPath, 'utf8');

      // Simple text to Markdown conversion
      const markdownContent = this.convertTextToMarkdown(textContent, options);

      // Write the Markdown file
      await fs.promises.writeFile(outputPath, markdownContent, 'utf8');

      // Clean up temporary text file
      await fs.promises.unlink(txtPath);

    } catch (error) {
      throw new Error(`Failed to convert ODT to Markdown: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Simple text to Markdown conversion
   */
  private convertTextToMarkdown(text: string, options?: any): string {
    let markdown = text;

    // Basic formatting conversions
    // Convert multiple newlines to paragraph breaks
    markdown = markdown.replace(/\n\s*\n/g, '\n\n');

    // Add basic structure - treat lines that look like headers
    markdown = markdown.replace(/^([A-Z][A-Z\s]+)$/gm, '# $1');

    // Preserve formatting if requested
    if (options?.preserveFormatting) {
      // Keep original line breaks
      markdown = markdown.replace(/\n/g, '  \n');
    }

    return markdown.trim();
  }
}