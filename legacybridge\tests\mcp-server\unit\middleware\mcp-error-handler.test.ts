// Unit tests for MCPErrorHandler
import { MCPErrorHandler, ValidationError, NotFoundError, ConversionError, AuthenticationError } from '../../../../src/mcp-server/middleware/mcp-error-handler';
import { MockMCPLogger } from '../../mocks/logger.mock';
import { createMockRequest, createMockResponse, createMockNextFunction } from '../../mocks/express.mock';

describe('MCPErrorHandler', () => {
  let mockLogger: MockMCPLogger;
  let errorHandler: any;
  
  beforeEach(() => {
    mockLogger = new MockMCPLogger();
    errorHandler = MCPErrorHandler(mockLogger as any);
  });
  
  test('should handle ValidationError', () => {
    const error = new ValidationError('Invalid input');
    const req = createMockRequest();
    const res = createMockResponse();
    const next = createMockNextFunction();

    errorHandler(error, req as any, res as any, next);

    expect(res.status).toHaveBeenCalledWith(400);
    expect(res.json).toHaveBeenCalledWith({
      status: 'error',
      errorCode: 'VALIDATION_ERROR',
      message: 'Invalid input',
      details: undefined,
      requestId: undefined,
      timestamp: expect.any(String)
    });
    expect(mockLogger.warn).toHaveBeenCalled();
  });
  
  test('should handle NotFoundError', () => {
    const error = new NotFoundError('Resource not found');
    const req = createMockRequest();
    const res = createMockResponse();
    const next = createMockNextFunction();

    errorHandler(error, req as any, res as any, next);

    expect(res.status).toHaveBeenCalledWith(404);
    expect(res.json).toHaveBeenCalledWith({
      status: 'error',
      errorCode: 'NOT_FOUND',
      message: 'Resource not found',
      details: undefined,
      requestId: undefined,
      timestamp: expect.any(String)
    });
    expect(mockLogger.warn).toHaveBeenCalled();
  });
  
  test('should handle ConversionError', () => {
    const originalNodeEnv = process.env.NODE_ENV;
    process.env.NODE_ENV = 'production'; // Ensure production mode for this test

    const error = new ConversionError('Conversion failed', { format: 'RTF' });
    const req = createMockRequest();
    const res = createMockResponse();
    const next = createMockNextFunction();

    errorHandler(error, req as any, res as any, next);

    expect(res.status).toHaveBeenCalledWith(422);
    expect(res.json).toHaveBeenCalledWith({
      status: 'error',
      errorCode: 'CONVERSION_ERROR',
      message: 'Failed to convert the document',
      details: undefined, // details are hidden in production
      requestId: undefined,
      timestamp: expect.any(String)
    });
    expect(mockLogger.warn).toHaveBeenCalled();

    // Restore NODE_ENV
    process.env.NODE_ENV = originalNodeEnv;
  });
  
  test('should handle AuthenticationError', () => {
    const error = new AuthenticationError('Unauthorized');
    const req = createMockRequest();
    const res = createMockResponse();
    const next = createMockNextFunction();

    errorHandler(error, req as any, res as any, next);

    expect(res.status).toHaveBeenCalledWith(401);
    expect(res.json).toHaveBeenCalledWith({
      status: 'error',
      errorCode: 'AUTHENTICATION_ERROR',
      message: 'Authentication failed',
      details: undefined,
      requestId: undefined,
      timestamp: expect.any(String)
    });
    expect(mockLogger.warn).toHaveBeenCalled();
  });
  
  test('should handle generic Error', () => {
    const error = new Error('Something went wrong');
    const req = createMockRequest();
    const res = createMockResponse();
    const next = createMockNextFunction();

    errorHandler(error, req as any, res as any, next);

    expect(res.status).toHaveBeenCalledWith(500);
    expect(res.json).toHaveBeenCalledWith({
      status: 'error',
      errorCode: 'INTERNAL_ERROR',
      message: 'An unexpected error occurred',
      details: undefined,
      requestId: undefined,
      timestamp: expect.any(String)
    });
    expect(mockLogger.error).toHaveBeenCalled();
  });
  
  test('should include error details in development environment', () => {
    const originalNodeEnv = process.env.NODE_ENV;
    process.env.NODE_ENV = 'development';

    const error = new ConversionError('Something went wrong', { format: 'RTF' });
    const req = createMockRequest();
    const res = createMockResponse();
    const next = createMockNextFunction();

    errorHandler(error, req as any, res as any, next);

    expect(res.status).toHaveBeenCalledWith(422);
    expect(res.json).toHaveBeenCalledWith({
      status: 'error',
      errorCode: 'CONVERSION_ERROR',
      message: 'Failed to convert the document',
      details: { format: 'RTF' },
      requestId: undefined,
      timestamp: expect.any(String)
    });

    // Restore NODE_ENV
    process.env.NODE_ENV = originalNodeEnv;
  });
  
  test('should handle errors with request ID', () => {
    const error = new Error('Something went wrong');
    const req = createMockRequest({
      headers: {
        'x-request-id': 'test-request-id'
      }
    });
    const res = createMockResponse();
    const next = createMockNextFunction();

    errorHandler(error, req as any, res as any, next);

    expect(res.status).toHaveBeenCalledWith(500);
    expect(res.json).toHaveBeenCalledWith({
      status: 'error',
      errorCode: 'INTERNAL_ERROR',
      message: 'An unexpected error occurred',
      details: undefined,
      requestId: 'test-request-id',
      timestamp: expect.any(String)
    });
    expect(mockLogger.error).toHaveBeenCalled();
  });
});