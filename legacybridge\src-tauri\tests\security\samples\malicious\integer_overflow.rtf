{\rtf1\ansi\deff0
\par This document tests integer overflow vulnerabilities:
\par
\fs2147483647 Maximum positive integer font size
\par
\fs-2147483648 Maximum negative integer font size
\par
\fs9999999999999999999 Extremely large number
\par
\li-9999999999999999999 Extremely negative indent
\par
\ri4294967296 Unsigned 32-bit overflow
\par
\cellx2147483648 Cell position overflow
\par
\trowd\trgaph-2147483648\trleft2147483647
\par
{\*\panose 255 255 255 255 255 255 255 255 255 255}
\par
\uc999999999 Unicode skip count overflow
\par
\u2147483647 Maximum Unicode value
\par
\u-2147483648 Minimum Unicode value
\par
Table with extreme dimensions:
\par
\trowd\cellx1000000000\cellx2000000000\cellx3000000000\cellx4000000000
\intbl Cell1\cell Cell2\cell Cell3\cell Cell4\cell\row
}