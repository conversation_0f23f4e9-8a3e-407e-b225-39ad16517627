version: '3.9'

services:
  # MCP Server
  mcp-server:
    build:
      context: .
      dockerfile: Dockerfile.mcp
      args:
        VERSION: ${VERSION:-dev}
        BUILD_DATE: ${BUILD_DATE:-now}
    image: legacybridge-mcp:${VERSION:-latest}
    container_name: legacybridge-mcp-server
    ports:
      - "${MCP_PORT:-3030}:3030"
    environment:
      NODE_ENV: ${NODE_ENV:-production}
      LOG_LEVEL: ${LOG_LEVEL:-info}
      MCP_PORT: 3030
      CACHE_ENABLED: ${CACHE_ENABLED:-true}
      CACHE_TYPE: ${CACHE_TYPE:-redis}
      REDIS_URL: redis://redis:6379
      ENABLE_DOC: ${ENABLE_DOC:-false}
      ENABLE_WORDPERFECT: ${ENABLE_WORDPERFECT:-false}
      API_KEYS: ${API_KEYS:-}
      JWT_SECRET: ${JWT_SECRET:-}
    depends_on:
      redis:
        condition: service_healthy
    volumes:
      - ./uploads:/app/uploads
      - ./output:/app/output
      - ./logs:/app/logs
    networks:
      - mcp-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3030/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s

  # Redis cache
  redis:
    image: redis:7-alpine
    container_name: legacybridge-mcp-redis
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - mcp-network
    restart: unless-stopped

  # Nginx reverse proxy (optional)
  nginx:
    image: nginx:alpine
    container_name: legacybridge-mcp-nginx
    ports:
      - "${NGINX_PORT:-80}:80"
    volumes:
      - ./nginx/nginx.mcp.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - mcp-server
    networks:
      - mcp-network
    profiles:
      - production
    restart: unless-stopped

networks:
  mcp-network:
    driver: bridge

volumes:
  redis-data:
    driver: local