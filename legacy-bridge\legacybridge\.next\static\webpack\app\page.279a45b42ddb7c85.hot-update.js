"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/DragDropZone.tsx":
/*!*****************************************!*\
  !*** ./src/components/DragDropZone.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DragDropZone: () => (/* binding */ DragDropZone)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _lib_stores_files__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/stores/files */ \"(app-pages-browser)/./src/lib/stores/files.ts\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ DragDropZone auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst DragDropZone = (param)=>{\n    let { onFilesAdded, className } = param;\n    _s();\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragCounter, setDragCounter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { files, addFiles, removeFile } = (0,_lib_stores_files__WEBPACK_IMPORTED_MODULE_2__.useFileStore)();\n    const handleDragEnter = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DragDropZone.useCallback[handleDragEnter]\": (e)=>{\n            e.preventDefault();\n            e.stopPropagation();\n            setDragCounter({\n                \"DragDropZone.useCallback[handleDragEnter]\": (prev)=>prev + 1\n            }[\"DragDropZone.useCallback[handleDragEnter]\"]);\n            if (e.dataTransfer.items && e.dataTransfer.items.length > 0) {\n                setIsDragging(true);\n            }\n        }\n    }[\"DragDropZone.useCallback[handleDragEnter]\"], []);\n    const handleDragLeave = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DragDropZone.useCallback[handleDragLeave]\": (e)=>{\n            e.preventDefault();\n            e.stopPropagation();\n            setDragCounter({\n                \"DragDropZone.useCallback[handleDragLeave]\": (prev)=>prev - 1\n            }[\"DragDropZone.useCallback[handleDragLeave]\"]);\n            if (dragCounter - 1 === 0) {\n                setIsDragging(false);\n            }\n        }\n    }[\"DragDropZone.useCallback[handleDragLeave]\"], [\n        dragCounter\n    ]);\n    const handleDragOver = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DragDropZone.useCallback[handleDragOver]\": (e)=>{\n            e.preventDefault();\n            e.stopPropagation();\n        }\n    }[\"DragDropZone.useCallback[handleDragOver]\"], []);\n    const validateFiles = (fileList)=>{\n        const validFiles = [];\n        const errors = [];\n        Array.from(fileList).forEach((file)=>{\n            if (file.name.endsWith('.rtf') || file.name.endsWith('.md')) {\n                validFiles.push(file);\n            } else {\n                errors.push(\"\".concat(file.name, \" is not a valid file type\"));\n            }\n        });\n        if (errors.length > 0) {\n            setError(errors.join(', '));\n            setTimeout(()=>setError(null), 5000);\n        }\n        return validFiles;\n    };\n    const handleDrop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DragDropZone.useCallback[handleDrop]\": (e)=>{\n            e.preventDefault();\n            e.stopPropagation();\n            setIsDragging(false);\n            setDragCounter(0);\n            const validFiles = validateFiles(e.dataTransfer.files);\n            if (validFiles.length > 0) {\n                addFiles(validFiles);\n                onFilesAdded === null || onFilesAdded === void 0 ? void 0 : onFilesAdded(validFiles);\n            }\n        }\n    }[\"DragDropZone.useCallback[handleDrop]\"], [\n        addFiles,\n        onFilesAdded\n    ]);\n    const handleFileInput = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DragDropZone.useCallback[handleFileInput]\": (e)=>{\n            if (e.target.files) {\n                const validFiles = validateFiles(e.target.files);\n                if (validFiles.length > 0) {\n                    addFiles(validFiles);\n                    onFilesAdded === null || onFilesAdded === void 0 ? void 0 : onFilesAdded(validFiles);\n                }\n            }\n        }\n    }[\"DragDropZone.useCallback[handleFileInput]\"], [\n        addFiles,\n        onFilesAdded\n    ]);\n    const formatFileSize = (bytes)=>{\n        if (bytes < 1024) return bytes + ' B';\n        if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';\n        return (bytes / (1024 * 1024)).toFixed(1) + ' MB';\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)('w-full', className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n            initial: {\n                opacity: 0,\n                y: 20\n            },\n            animate: {\n                opacity: 1,\n                y: 0\n            },\n            transition: {\n                duration: 0.5\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    onDragEnter: handleDragEnter,\n                    onDragLeave: handleDragLeave,\n                    onDragOver: handleDragOver,\n                    onDrop: handleDrop,\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"file\",\n                            id: \"file-input\",\n                            className: \"sr-only\",\n                            multiple: true,\n                            accept: \".rtf,.md\",\n                            onChange: handleFileInput,\n                            \"aria-describedby\": \"file-input-description\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\components\\\\DragDropZone.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            id: \"file-input-description\",\n                            className: \"sr-only\",\n                            children: \"Select RTF or Markdown files to convert. Maximum file size: 10MB. Supported formats: RTF, Markdown.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\components\\\\DragDropZone.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.label, {\n                            htmlFor: \"file-input\",\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)('relative flex flex-col items-center justify-center', 'w-full h-64 p-8', 'border-2 border-dashed rounded-lg', 'cursor-pointer transition-all duration-200', 'hover:border-primary hover:bg-primary/5', 'focus-within:border-primary focus-within:bg-primary/5', 'focus-within:ring-2 focus-within:ring-primary focus-within:ring-offset-2', isDragging && 'border-primary bg-primary/10', 'group'),\n                            animate: {\n                                scale: isDragging ? 1.02 : 1,\n                                borderColor: isDragging ? 'rgb(99 102 241)' : 'rgb(229 231 235)'\n                            },\n                            transition: {\n                                duration: 0.2\n                            },\n                            role: \"button\",\n                            \"aria-label\": \"Upload RTF or Markdown files by clicking or dragging and dropping\",\n                            tabIndex: 0,\n                            onKeyDown: (e)=>{\n                                if (e.key === 'Enter' || e.key === ' ') {\n                                    var _document_getElementById;\n                                    e.preventDefault();\n                                    (_document_getElementById = document.getElementById('file-input')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.click();\n                                }\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                    animate: {\n                                        y: isDragging ? -10 : 0,\n                                        scale: isDragging ? 1.1 : 1\n                                    },\n                                    transition: {\n                                        type: 'spring',\n                                        stiffness: 300,\n                                        damping: 20\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_FileText_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)('w-12 h-12 mb-4 text-muted-foreground', 'group-hover:text-primary transition-colors', isDragging && 'text-primary')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\components\\\\DragDropZone.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\components\\\\DragDropZone.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.h3, {\n                                    className: \"text-lg font-semibold mb-2 text-foreground\",\n                                    animate: {\n                                        scale: isDragging ? 1.05 : 1\n                                    },\n                                    children: isDragging ? 'Drop your files here' : 'Drag & drop files here'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\components\\\\DragDropZone.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-muted-foreground mb-4\",\n                                    children: \"or click to browse\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\components\\\\DragDropZone.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                            variant: \"secondary\",\n                                            children: \"RTF\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\components\\\\DragDropZone.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                            variant: \"secondary\",\n                                            children: \"Markdown\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\components\\\\DragDropZone.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\components\\\\DragDropZone.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\components\\\\DragDropZone.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\components\\\\DragDropZone.tsx\",\n                    lineNumber: 103,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.AnimatePresence, {\n                    children: error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: -10\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        exit: {\n                            opacity: 0,\n                            y: -10\n                        },\n                        className: \"mt-4 p-3 bg-destructive/10 text-destructive rounded-md flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_FileText_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\components\\\\DragDropZone.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\components\\\\DragDropZone.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\components\\\\DragDropZone.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\components\\\\DragDropZone.tsx\",\n                    lineNumber: 184,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.AnimatePresence, {\n                    children: files.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            height: 0\n                        },\n                        animate: {\n                            opacity: 1,\n                            height: 'auto'\n                        },\n                        exit: {\n                            opacity: 0,\n                            height: 0\n                        },\n                        className: \"mt-6 space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-sm font-medium text-muted-foreground\",\n                                children: [\n                                    \"Selected Files (\",\n                                    files.length,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\components\\\\DragDropZone.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 15\n                            }, undefined),\n                            files.map((file, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        x: -20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    exit: {\n                                        opacity: 0,\n                                        x: 20\n                                    },\n                                    transition: {\n                                        delay: index * 0.05\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                        className: \"p-4 hover:shadow-md transition-shadow\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                                                whileHover: {\n                                                                    rotate: 5\n                                                                },\n                                                                whileTap: {\n                                                                    scale: 0.95\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_FileText_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    className: \"w-8 h-8 text-primary\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\components\\\\DragDropZone.tsx\",\n                                                                    lineNumber: 226,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\components\\\\DragDropZone.tsx\",\n                                                                lineNumber: 222,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium truncate max-w-[300px]\",\n                                                                        children: file.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\components\\\\DragDropZone.tsx\",\n                                                                        lineNumber: 230,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2 text-sm text-muted-foreground\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: formatFileSize(file.size)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\components\\\\DragDropZone.tsx\",\n                                                                                lineNumber: 234,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"•\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\components\\\\DragDropZone.tsx\",\n                                                                                lineNumber: 235,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                variant: \"outline\",\n                                                                                className: \"text-xs\",\n                                                                                children: file.type.toUpperCase()\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\components\\\\DragDropZone.tsx\",\n                                                                                lineNumber: 236,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            file.status !== 'idle' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: \"•\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\components\\\\DragDropZone.tsx\",\n                                                                                        lineNumber: 241,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                        variant: file.status === 'completed' ? 'default' : file.status === 'error' ? 'destructive' : 'secondary',\n                                                                                        className: \"text-xs\",\n                                                                                        children: file.status\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\components\\\\DragDropZone.tsx\",\n                                                                                        lineNumber: 242,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\components\\\\DragDropZone.tsx\",\n                                                                        lineNumber: 233,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\components\\\\DragDropZone.tsx\",\n                                                                lineNumber: 229,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\components\\\\DragDropZone.tsx\",\n                                                        lineNumber: 221,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                                        whileHover: {\n                                                            scale: 1.1\n                                                        },\n                                                        whileTap: {\n                                                            scale: 0.9\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            variant: \"ghost\",\n                                                            size: \"icon\",\n                                                            onClick: ()=>removeFile(file.id),\n                                                            className: \"h-8 w-8\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_FileText_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\components\\\\DragDropZone.tsx\",\n                                                                lineNumber: 268,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\components\\\\DragDropZone.tsx\",\n                                                            lineNumber: 262,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\components\\\\DragDropZone.tsx\",\n                                                        lineNumber: 258,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\components\\\\DragDropZone.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            file.status === 'converting' && file.progress !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                                initial: {\n                                                    opacity: 0\n                                                },\n                                                animate: {\n                                                    opacity: 1\n                                                },\n                                                className: \"mt-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full bg-secondary rounded-full h-2 overflow-hidden\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                                        className: \"h-full bg-primary\",\n                                                        initial: {\n                                                            width: 0\n                                                        },\n                                                        animate: {\n                                                            width: \"\".concat(file.progress, \"%\")\n                                                        },\n                                                        transition: {\n                                                            duration: 0.3\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\components\\\\DragDropZone.tsx\",\n                                                        lineNumber: 281,\n                                                        columnNumber: 27\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\components\\\\DragDropZone.tsx\",\n                                                    lineNumber: 280,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\components\\\\DragDropZone.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\components\\\\DragDropZone.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, file.id, false, {\n                                    fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\components\\\\DragDropZone.tsx\",\n                                    lineNumber: 212,\n                                    columnNumber: 17\n                                }, undefined))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\components\\\\DragDropZone.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\components\\\\DragDropZone.tsx\",\n                    lineNumber: 199,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\components\\\\DragDropZone.tsx\",\n            lineNumber: 97,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\dev\\\\legacy-bridge\\\\legacy-bridge\\\\legacybridge\\\\src\\\\components\\\\DragDropZone.tsx\",\n        lineNumber: 96,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DragDropZone, \"nrsaEZAQs5nLKrJmkcjkN+lB1y8=\", false, function() {\n    return [\n        _lib_stores_files__WEBPACK_IMPORTED_MODULE_2__.useFileStore\n    ];\n});\n_c = DragDropZone;\nvar _c;\n$RefreshReg$(_c, \"DragDropZone\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/DragDropZone.tsx\n"));

/***/ })

});