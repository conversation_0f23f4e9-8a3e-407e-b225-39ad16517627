// Mock MCPCache for testing
export class MockMC<PERSON>ache {
  get = jest.fn();
  set = jest.fn();
  delete = jest.fn();
  clear = jest.fn();
  close = jest.fn();
  
  constructor() {
    this.get.mockImplementation(() => Promise.resolve(null));
    this.set.mockImplementation(() => Promise.resolve(true));
    this.delete.mockImplementation(() => Promise.resolve(true));
    this.clear.mockImplementation(() => Promise.resolve());
    this.close.mockImplementation(() => Promise.resolve());
  }
  
  reset() {
    this.get.mockClear();
    this.set.mockClear();
    this.delete.mockClear();
    this.clear.mockClear();
    this.close.mockClear();
  }
  
  // Helper to set up mock cache hits
  mockCacheHit(key: string, value: any) {
    this.get.mockImplementation((k: string) => {
      if (k === key) {
        return Promise.resolve(value);
      }
      return Promise.resolve(null);
    });
  }
}