// Global setup for MCP Server Playwright tests
import { chromium, FullConfig } from '@playwright/test';

async function globalSetup(config: FullConfig) {
  console.log('🚀 Starting MCP Server Playwright test setup...');
  
  // Create a browser instance for health checks
  const browser = await chromium.launch();
  const page = await browser.newPage();
  
  try {
    // Wait for the server to be ready by checking health endpoint
    let retries = 30; // 30 seconds max wait
    let serverReady = false;
    
    while (retries > 0 && !serverReady) {
      try {
        const response = await page.request.get('http://localhost:3032/health');
        if (response.status() === 200) {
          serverReady = true;
          console.log('✅ MCP Server is ready for testing');
        }
      } catch (error) {
        // Server not ready yet, wait and retry
        await new Promise(resolve => setTimeout(resolve, 1000));
        retries--;
      }
    }
    
    if (!serverReady) {
      console.error('❌ MCP Server failed to start within timeout period');
      throw new Error('MCP Server not ready for testing');
    }
    
  } finally {
    await browser.close();
  }
}

export default globalSetup;
