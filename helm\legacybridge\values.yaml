# Default values for legacybridge
replicaCount: 3

image:
  repository: legacybridge
  pullPolicy: IfNotPresent
  tag: "latest"

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  create: true
  annotations: {}
  name: ""

podAnnotations:
  prometheus.io/scrape: "true"
  prometheus.io/port: "9090"
  prometheus.io/path: "/metrics"

podSecurityContext:
  runAsNonRoot: true
  runAsUser: 1000
  fsGroup: 1000

securityContext:
  allowPrivilegeEscalation: false
  readOnlyRootFilesystem: true
  capabilities:
    drop:
    - ALL

service:
  type: ClusterIP
  port: 80
  targetPort: 3000
  metricsPort: 9090

ingress:
  enabled: true
  className: nginx
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/rate-limit: "100"
  hosts:
    - host: legacybridge.example.com
      paths:
        - path: /
          pathType: Prefix
  tls:
    - secretName: legacybridge-tls
      hosts:
        - legacybridge.example.com

resources:
  limits:
    cpu: 1000m
    memory: 512Mi
  requests:
    cpu: 250m
    memory: 256Mi

autoscaling:
  enabled: true
  minReplicas: 3
  maxReplicas: 10
  targetCPUUtilizationPercentage: 70
  targetMemoryUtilizationPercentage: 80

nodeSelector: {}
tolerations: []
affinity:
  podAntiAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
    - weight: 100
      podAffinityTerm:
        labelSelector:
          matchExpressions:
          - key: app.kubernetes.io/name
            operator: In
            values:
            - legacybridge
        topologyKey: kubernetes.io/hostname

env:
  - name: NODE_ENV
    value: production
  - name: PORT
    value: "3000"
  - name: LOG_LEVEL
    value: info

config:
  maxFileSize: 52428800
  batchMaxFiles: 100
  conversionTimeout: 300000
  corsOrigins: "https://legacybridge.example.com"
  rateLimit: 100
  rateWindow: 60000

secrets:
  create: true
  databaseUrl: ""
  jwtSecret: ""
  apiKey: ""

postgresql:
  enabled: true
  auth:
    username: legacybridge
    password: changeme
    database: legacybridge
  primary:
    persistence:
      enabled: true
      size: 10Gi

redis:
  enabled: true
  auth:
    enabled: true
    password: changeme
  master:
    persistence:
      enabled: true
      size: 8Gi

monitoring:
  enabled: true
  serviceMonitor:
    enabled: true
    interval: 30s
  prometheusRule:
    enabled: true
  grafanaDashboard:
    enabled: true

healthcheck:
  liveness:
    path: /api/health
    initialDelaySeconds: 30
    periodSeconds: 10
    timeoutSeconds: 5
    failureThreshold: 3
  readiness:
    path: /api/ready
    initialDelaySeconds: 5
    periodSeconds: 5
    timeoutSeconds: 3
    failureThreshold: 3

podDisruptionBudget:
  enabled: true
  minAvailable: 2