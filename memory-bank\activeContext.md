# Active Context

## Code Changes (2025-07-26 22:59:30)

### Added Files
- .github/workflows/ci.yml
- COMPREHENSIVE_SECURITY_ANALYSIS.json
- docker-compose.yml
- docs/api/interactive-explorer/index.html
- docs/troubleshooting/interactive-wizard/index.html
- helm/legacybridge/Chart.yaml
- helm/legacybridge/values-production.yaml
- helm/legacybridge/values.yaml
- k8s/configmap.yaml
- k8s/deployment-optimized.yaml
- k8s/deployment.yaml
- k8s/ingress.yaml
- k8s/monitoring-stack.yaml
- k8s/monitoring.yaml
- legacybridge/.eslintrc.json
- legacybridge/ENTERPRISE_PACKAGE/MANIFEST.json
- legacybridge/ENTERPRISE_PACKAGE/examples/other/test_dll.c
- legacybridge/ENTERPRISE_PACKAGE/examples/other/test_dll.py
- legacybridge/ENTERPRISE_PACKAGE/include/legacybridge.h
- legacybridge/components.json
- legacybridge/deployment_package/include/legacybridge.h
- legacybridge/frontend-integration-example.ts
- legacybridge/include/legacybridge.h
- legacybridge/jest.config.js
- legacybridge/next.config.ts
- legacybridge/package-lock.json
- legacybridge/package.json
- legacybridge/perf_test.c
- legacybridge/playwright.config.ts
- legacybridge/src/__tests__/security/xss.test.tsx
- legacybridge/src/app/api/monitoring/alerts/route.ts
- legacybridge/src/app/api/monitoring/alerts/slack/route.ts
- legacybridge/src/app/api/monitoring/metrics/route.ts
- legacybridge/src/app/api/monitoring/prometheus/route.ts
- legacybridge/src/app/demo/page.tsx
- legacybridge/src/app/globals-backup.css
- legacybridge/src/app/globals.css
- legacybridge/src/app/home-page.tsx
- legacybridge/src/app/layout.tsx
- legacybridge/src/app/monitoring/page.tsx
- legacybridge/src/app/page.tsx
- legacybridge/src/components/ConversionProgress.tsx
- legacybridge/src/components/DiffView.tsx
- legacybridge/src/components/DownloadManager.tsx
- legacybridge/src/components/DragDropZone.tsx
- legacybridge/src/components/ErrorBoundary.tsx
- legacybridge/src/components/ErrorDisplay.tsx
- legacybridge/src/components/ErrorLogViewer.tsx
- legacybridge/src/components/LoadingAnimation.tsx
- legacybridge/src/components/MarkdownPreview.tsx
- legacybridge/src/components/PreviewPanel.tsx
- legacybridge/src/components/SuccessAnimation.tsx
- legacybridge/src/components/SyntaxHighlighter.tsx
- legacybridge/src/components/ThemeProvider.tsx
- legacybridge/src/components/UnifiedErrorDisplay.tsx
- legacybridge/src/components/layout/Footer.tsx
- legacybridge/src/components/layout/Header.tsx
- legacybridge/src/components/layout/MainLayout.tsx
- legacybridge/src/components/monitoring/BuildProgressRing.tsx
- legacybridge/src/components/monitoring/EnhancedFunctionCallMatrix.tsx
- legacybridge/src/components/monitoring/FunctionCallMatrix.tsx
- legacybridge/src/components/monitoring/LogStreamViewer.tsx
- legacybridge/src/components/monitoring/MonitoringDashboard.tsx
- legacybridge/src/components/monitoring/PerformanceChart.tsx
- legacybridge/src/components/monitoring/SystemHealthCard.tsx
- legacybridge/src/components/monitoring/index.ts
- legacybridge/src/components/ui/alert-dialog.tsx
- legacybridge/src/components/ui/badge.tsx
- legacybridge/src/components/ui/button.tsx
- legacybridge/src/components/ui/card.tsx
- legacybridge/src/components/ui/dropdown-menu.tsx
- legacybridge/src/components/ui/input.tsx
- legacybridge/src/components/ui/label.tsx
- legacybridge/src/components/ui/progress.tsx
- legacybridge/src/components/ui/separator.tsx
- legacybridge/src/components/ui/switch.tsx
- legacybridge/src/components/ui/tabs.tsx
- legacybridge/src/components/ui/tooltip.tsx
- legacybridge/src/enterprise/admin/AdminDashboard.tsx
- legacybridge/src/enterprise/api/enterprise-api.ts
- legacybridge/src/enterprise/audit/audit-logger.ts
- legacybridge/src/enterprise/auth/authentication.ts
- legacybridge/src/enterprise/auth/rbac.ts
- legacybridge/src/enterprise/config/kubernetes-deployment.yaml
- legacybridge/src/enterprise/services/tenant-conversion-service.ts
- legacybridge/src/enterprise/tenancy/tenant-context.ts
- legacybridge/src/hooks/useDebounce.ts
- legacybridge/src/hooks/useMonitoringWebSocket.ts
- legacybridge/src/lib/download-service.ts
- legacybridge/src/lib/error-logger.ts
- legacybridge/src/lib/monitoring/alert-manager.ts
- legacybridge/src/lib/monitoring/websocket-server.ts
- legacybridge/src/lib/sanitizer.ts
- legacybridge/src/lib/stores/files.ts
- legacybridge/src/lib/tauri-api.ts
- legacybridge/src/lib/unified-error-api.ts
- legacybridge/src/lib/utils.ts
- legacybridge/src/middleware.ts
- legacybridge/src/styles/monitoring.css
- legacybridge/src/types/errors.ts
- legacybridge/src/types/index.ts
- legacybridge/src/types/monitoring.ts
- legacybridge/src-tauri/security.config.json
- legacybridge/src-tauri/target/.rustc_info.json
- legacybridge/src-tauri/target/debug/.fingerprint/adler2-05d1217c9be2ffb5/lib-adler2.json
- legacybridge/src-tauri/target/debug/.fingerprint/adler2-3e0a0cffe9594183/lib-adler2.json
- legacybridge/src-tauri/target/debug/.fingerprint/adler2-af5b0e47f1b187b9/lib-adler2.json
- legacybridge/src-tauri/target/debug/.fingerprint/ahash-97dcf011a296f4f6/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/ahash-e4e7d86eec43b304/lib-ahash.json
- legacybridge/src-tauri/target/debug/.fingerprint/ahash-fc5a584595c101e1/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/aho-corasick-1b7f3ecbcf3f171a/lib-aho_corasick.json
- legacybridge/src-tauri/target/debug/.fingerprint/aho-corasick-7186a1056f7f5843/lib-aho_corasick.json
- legacybridge/src-tauri/target/debug/.fingerprint/alloc-no-stdlib-4faf44e5c60a75cc/lib-alloc_no_stdlib.json
- legacybridge/src-tauri/target/debug/.fingerprint/alloc-no-stdlib-cc81188fe16931f8/lib-alloc_no_stdlib.json
- legacybridge/src-tauri/target/debug/.fingerprint/alloc-no-stdlib-decfe55c8f032221/lib-alloc_no_stdlib.json
- legacybridge/src-tauri/target/debug/.fingerprint/alloc-stdlib-8889dec45a50107c/lib-alloc_stdlib.json
- legacybridge/src-tauri/target/debug/.fingerprint/alloc-stdlib-a32f2c3cd43873bf/lib-alloc_stdlib.json
- legacybridge/src-tauri/target/debug/.fingerprint/alloc-stdlib-e32a36e2e3b9cd7b/lib-alloc_stdlib.json
- legacybridge/src-tauri/target/debug/.fingerprint/anyhow-1fea51c98c81cce8/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/anyhow-ab7828b45f7ca0be/lib-anyhow.json
- legacybridge/src-tauri/target/debug/.fingerprint/anyhow-b3031e426d49d7c9/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/anyhow-cb0106135621df54/lib-anyhow.json
- legacybridge/src-tauri/target/debug/.fingerprint/anyhow-f834acad29629d96/lib-anyhow.json
- legacybridge/src-tauri/target/debug/.fingerprint/arboard-0dc92af9f7f16130/lib-arboard.json
- legacybridge/src-tauri/target/debug/.fingerprint/arboard-76b34dbf540965c3/lib-arboard.json
- legacybridge/src-tauri/target/debug/.fingerprint/atk-51e821f707a61158/lib-atk.json
- legacybridge/src-tauri/target/debug/.fingerprint/atk-d17074bec303be7a/lib-atk.json
- legacybridge/src-tauri/target/debug/.fingerprint/atk-d86ccfb1bb7cc594/lib-atk.json
- legacybridge/src-tauri/target/debug/.fingerprint/atk-sys-17f16e6971759b3d/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/atk-sys-491945411001faa9/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/atk-sys-583a2fb8a2924a8e/lib-atk_sys.json
- legacybridge/src-tauri/target/debug/.fingerprint/atk-sys-7ba85bcbee8819d5/lib-atk_sys.json
- legacybridge/src-tauri/target/debug/.fingerprint/atk-sys-89fedf3d54ca69fe/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/atk-sys-8fa7f8f19a0e746d/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/atk-sys-a249268b70b10712/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/atk-sys-c972af78a282a794/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/atk-sys-e3072c1d8e635a30/lib-atk_sys.json
- legacybridge/src-tauri/target/debug/.fingerprint/autocfg-9280f9c2f723f569/lib-autocfg.json
- legacybridge/src-tauri/target/debug/.fingerprint/base64-8f6ac19e81e48ded/lib-base64.json
- legacybridge/src-tauri/target/debug/.fingerprint/base64-9ded01860c590c16/lib-base64.json
- legacybridge/src-tauri/target/debug/.fingerprint/bitflags-473b852bda163ccf/lib-bitflags.json
- legacybridge/src-tauri/target/debug/.fingerprint/bitflags-8a4d4b1731737631/lib-bitflags.json
- legacybridge/src-tauri/target/debug/.fingerprint/bitflags-a17809c42b30ead1/lib-bitflags.json
- legacybridge/src-tauri/target/debug/.fingerprint/bitflags-c5e8392f486cd43f/lib-bitflags.json
- legacybridge/src-tauri/target/debug/.fingerprint/bitflags-c9e75a596c91d0d8/lib-bitflags.json
- legacybridge/src-tauri/target/debug/.fingerprint/block-buffer-84156d25b3c3d385/lib-block_buffer.json
- legacybridge/src-tauri/target/debug/.fingerprint/brotli-1845120df4c5cd55/lib-brotli.json
- legacybridge/src-tauri/target/debug/.fingerprint/brotli-decompressor-22147ee2fdc43176/lib-brotli_decompressor.json
- legacybridge/src-tauri/target/debug/.fingerprint/brotli-decompressor-e57eb2187f4d6a4c/lib-brotli_decompressor.json
- legacybridge/src-tauri/target/debug/.fingerprint/brotli-decompressor-ea66140830592fd8/lib-brotli_decompressor.json
- legacybridge/src-tauri/target/debug/.fingerprint/brotli-e27bbcef34a06fa3/lib-brotli.json
- legacybridge/src-tauri/target/debug/.fingerprint/brotli-ff791bae63f9755e/lib-brotli.json
- legacybridge/src-tauri/target/debug/.fingerprint/bstr-561d75d2881d0b48/lib-bstr.json
- legacybridge/src-tauri/target/debug/.fingerprint/bumpalo-c6c6bb635c4686db/lib-bumpalo.json
- legacybridge/src-tauri/target/debug/.fingerprint/bytemuck-881637ac429b7db2/lib-bytemuck.json
- legacybridge/src-tauri/target/debug/.fingerprint/byteorder-128526876fcc1990/lib-byteorder.json
- legacybridge/src-tauri/target/debug/.fingerprint/byteorder-bf74668de8f2b493/lib-byteorder.json
- legacybridge/src-tauri/target/debug/.fingerprint/byteorder-d2dd8daca59f888a/lib-byteorder.json
- legacybridge/src-tauri/target/debug/.fingerprint/byteorder-lite-c85076c3565f09a8/lib-byteorder_lite.json
- legacybridge/src-tauri/target/debug/.fingerprint/bytes-8b7319ab20ce4b75/lib-bytes.json
- legacybridge/src-tauri/target/debug/.fingerprint/bytes-e9fddd1bd45d8900/lib-bytes.json
- legacybridge/src-tauri/target/debug/.fingerprint/cairo-rs-1d6c97cf3160fa30/lib-cairo.json
- legacybridge/src-tauri/target/debug/.fingerprint/cairo-rs-aaef09b3b9573a54/lib-cairo.json
- legacybridge/src-tauri/target/debug/.fingerprint/cairo-rs-e6b680f3d3b8505b/lib-cairo.json
- legacybridge/src-tauri/target/debug/.fingerprint/cairo-sys-rs-0a247bc1c3a9e517/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/cairo-sys-rs-1048d13c7b8c873b/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/cairo-sys-rs-229fb8de0bde8d1a/lib-cairo_sys.json
- legacybridge/src-tauri/target/debug/.fingerprint/cairo-sys-rs-3fa5a905188ba5c4/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/cairo-sys-rs-66303dafe0b61f55/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/cairo-sys-rs-7dc89ef3059221c9/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/cairo-sys-rs-9d4bb81daf45edb2/lib-cairo_sys.json
- legacybridge/src-tauri/target/debug/.fingerprint/cairo-sys-rs-a0782c6e6d12accd/lib-cairo_sys.json
- legacybridge/src-tauri/target/debug/.fingerprint/cairo-sys-rs-fdb0f5e6b919583b/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/cargo_toml-6fa27bcde72a59ad/lib-cargo_toml.json
- legacybridge/src-tauri/target/debug/.fingerprint/cargo_toml-77ad7102d83e8f5b/lib-cargo_toml.json
- legacybridge/src-tauri/target/debug/.fingerprint/cc-2057a129cd2a02c3/lib-cc.json
- legacybridge/src-tauri/target/debug/.fingerprint/cfb-0f46bab0abd2cb6c/lib-cfb.json
- legacybridge/src-tauri/target/debug/.fingerprint/cfb-74a22b11b1942765/lib-cfb.json
- legacybridge/src-tauri/target/debug/.fingerprint/cfb-f2f4ca72aec948b8/lib-cfb.json
- legacybridge/src-tauri/target/debug/.fingerprint/cfg-expr-67dc737b391e60b1/lib-cfg_expr.json
- legacybridge/src-tauri/target/debug/.fingerprint/cfg-expr-6dac77e90a9fc2e9/lib-cfg_expr.json
- legacybridge/src-tauri/target/debug/.fingerprint/cfg-if-37c365409298422e/lib-cfg_if.json
- legacybridge/src-tauri/target/debug/.fingerprint/cfg-if-b090a885f2700b8b/lib-cfg_if.json
- legacybridge/src-tauri/target/debug/.fingerprint/cfg-if-cb2748525c1e0288/lib-cfg_if.json
- legacybridge/src-tauri/target/debug/.fingerprint/chrono-eb3eb03a16e412b5/lib-chrono.json
- legacybridge/src-tauri/target/debug/.fingerprint/convert_case-0928d336c7f239df/lib-convert_case.json
- legacybridge/src-tauri/target/debug/.fingerprint/cpufeatures-e69806b0dee56216/lib-cpufeatures.json
- legacybridge/src-tauri/target/debug/.fingerprint/crc32fast-3a22bbf460790681/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/crc32fast-5466925a00199899/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/crc32fast-81e12e74c410a72f/lib-crc32fast.json
- legacybridge/src-tauri/target/debug/.fingerprint/crc32fast-9163957251cda90c/lib-crc32fast.json
- legacybridge/src-tauri/target/debug/.fingerprint/crc32fast-d6edb6a16f9972cf/lib-crc32fast.json
- legacybridge/src-tauri/target/debug/.fingerprint/crossbeam-channel-440c1c64ccffe30f/lib-crossbeam_channel.json
- legacybridge/src-tauri/target/debug/.fingerprint/crossbeam-deque-db865e64ae5e2f3c/lib-crossbeam_deque.json
- legacybridge/src-tauri/target/debug/.fingerprint/crossbeam-deque-f0686b61841f828a/lib-crossbeam_deque.json
- legacybridge/src-tauri/target/debug/.fingerprint/crossbeam-epoch-56f02b583a0b8539/lib-crossbeam_epoch.json
- legacybridge/src-tauri/target/debug/.fingerprint/crossbeam-epoch-5d380abd02926c22/lib-crossbeam_epoch.json
- legacybridge/src-tauri/target/debug/.fingerprint/crossbeam-utils-250510a4114e5a1e/lib-crossbeam_utils.json
- legacybridge/src-tauri/target/debug/.fingerprint/crossbeam-utils-2e7512e383e7552c/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/crossbeam-utils-7b9bd07d9fad49b5/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/crossbeam-utils-8107c299d7212acb/lib-crossbeam_utils.json
- legacybridge/src-tauri/target/debug/.fingerprint/crypto-common-02607a0a764a683c/lib-crypto_common.json
- legacybridge/src-tauri/target/debug/.fingerprint/cssparser-5b119ddd1061afd0/lib-cssparser.json
- legacybridge/src-tauri/target/debug/.fingerprint/cssparser-67ff2aad3c2bb83c/lib-cssparser.json
- legacybridge/src-tauri/target/debug/.fingerprint/cssparser-7051fcf6ccacbbcc/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/cssparser-87f9b0159b6b1d9a/lib-cssparser.json
- legacybridge/src-tauri/target/debug/.fingerprint/cssparser-965bc40595a91fd5/lib-cssparser.json
- legacybridge/src-tauri/target/debug/.fingerprint/cssparser-e07421ea14d10e98/lib-cssparser.json
- legacybridge/src-tauri/target/debug/.fingerprint/cssparser-e8ce5d8e777f0448/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/cssparser-f0ed05fb55ef0b87/lib-cssparser.json
- legacybridge/src-tauri/target/debug/.fingerprint/cssparser-macros-700350a955f0889f/lib-cssparser_macros.json
- legacybridge/src-tauri/target/debug/.fingerprint/cssparser-macros-a583eb56892ac155/lib-cssparser_macros.json
- legacybridge/src-tauri/target/debug/.fingerprint/ctor-4efa2d660b4c35d0/lib-ctor.json
- legacybridge/src-tauri/target/debug/.fingerprint/ctor-79da51bea8ab36ca/lib-ctor.json
- legacybridge/src-tauri/target/debug/.fingerprint/darling-6fc5fddb9c59ce9e/lib-darling.json
- legacybridge/src-tauri/target/debug/.fingerprint/darling-b9746b8e5e65e74e/lib-darling.json
- legacybridge/src-tauri/target/debug/.fingerprint/darling-f08373290550826a/lib-darling.json
- legacybridge/src-tauri/target/debug/.fingerprint/darling_core-1131837b6eb9a635/lib-darling_core.json
- legacybridge/src-tauri/target/debug/.fingerprint/darling_core-63d51d043acc9375/lib-darling_core.json
- legacybridge/src-tauri/target/debug/.fingerprint/darling_core-710d33041e67c155/lib-darling_core.json
- legacybridge/src-tauri/target/debug/.fingerprint/darling_macro-44464d2faa927b5b/lib-darling_macro.json
- legacybridge/src-tauri/target/debug/.fingerprint/darling_macro-482defafcdfe400b/lib-darling_macro.json
- legacybridge/src-tauri/target/debug/.fingerprint/darling_macro-f391f42b6e0e5b10/lib-darling_macro.json
- legacybridge/src-tauri/target/debug/.fingerprint/derive_more-644c14a4fd584ae0/lib-derive_more.json
- legacybridge/src-tauri/target/debug/.fingerprint/derive_more-c160597a889afc85/lib-derive_more.json
- legacybridge/src-tauri/target/debug/.fingerprint/digest-cfca3daec1be695c/lib-digest.json
- legacybridge/src-tauri/target/debug/.fingerprint/dirs-next-7d620c9753ffc43f/lib-dirs_next.json
- legacybridge/src-tauri/target/debug/.fingerprint/dirs-next-8fdda3b2a0ef922a/lib-dirs_next.json
- legacybridge/src-tauri/target/debug/.fingerprint/dirs-next-b7619c14e78f6b45/lib-dirs_next.json
- legacybridge/src-tauri/target/debug/.fingerprint/dirs-sys-next-0afb42b863351952/lib-dirs_sys_next.json
- legacybridge/src-tauri/target/debug/.fingerprint/dirs-sys-next-239eeaf310ffe1d5/lib-dirs_sys_next.json
- legacybridge/src-tauri/target/debug/.fingerprint/dirs-sys-next-d1a67c7ae8fb3ae3/lib-dirs_sys_next.json
- legacybridge/src-tauri/target/debug/.fingerprint/displaydoc-1958f3e5815acf50/lib-displaydoc.json
- legacybridge/src-tauri/target/debug/.fingerprint/displaydoc-f09e92e998eb8f82/lib-displaydoc.json
- legacybridge/src-tauri/target/debug/.fingerprint/downcast-rs-9d238031b6b2c4e9/lib-downcast_rs.json
- legacybridge/src-tauri/target/debug/.fingerprint/downcast-rs-beecdc26953421c1/lib-downcast_rs.json
- legacybridge/src-tauri/target/debug/.fingerprint/dtoa-1f44ad2db860c5a3/lib-dtoa.json
- legacybridge/src-tauri/target/debug/.fingerprint/dtoa-64d4d1636072386c/lib-dtoa.json
- legacybridge/src-tauri/target/debug/.fingerprint/dtoa-b5e155b33c11d683/lib-dtoa.json
- legacybridge/src-tauri/target/debug/.fingerprint/dtoa-short-39cf53b0d571b673/lib-dtoa_short.json
- legacybridge/src-tauri/target/debug/.fingerprint/dtoa-short-b4b2626e931a289a/lib-dtoa_short.json
- legacybridge/src-tauri/target/debug/.fingerprint/dtoa-short-c1ac67b434f7896c/lib-dtoa_short.json
- legacybridge/src-tauri/target/debug/.fingerprint/dunce-47915fe7b5d83d3c/lib-dunce.json
- legacybridge/src-tauri/target/debug/.fingerprint/dunce-9c9ab31823e0a1cd/lib-dunce.json
- legacybridge/src-tauri/target/debug/.fingerprint/dunce-fcaf53e054e92b7e/lib-dunce.json
- legacybridge/src-tauri/target/debug/.fingerprint/either-6324834661ac878a/lib-either.json
- legacybridge/src-tauri/target/debug/.fingerprint/embed-resource-2527ce08b4d4ac24/lib-embed_resource.json
- legacybridge/src-tauri/target/debug/.fingerprint/embed-resource-f0f09536f4ca4c17/lib-embed_resource.json
- legacybridge/src-tauri/target/debug/.fingerprint/encoding_rs-927c849606bf76fa/lib-encoding_rs.json
- legacybridge/src-tauri/target/debug/.fingerprint/equivalent-9ff5cbb44f5b198f/lib-equivalent.json
- legacybridge/src-tauri/target/debug/.fingerprint/equivalent-f34383553d737d05/lib-equivalent.json
- legacybridge/src-tauri/target/debug/.fingerprint/equivalent-fb6cc83cf281e9bc/lib-equivalent.json
- legacybridge/src-tauri/target/debug/.fingerprint/fastrand-4c7d3317299366b7/lib-fastrand.json
- legacybridge/src-tauri/target/debug/.fingerprint/fastrand-85d33d4943525743/lib-fastrand.json
- legacybridge/src-tauri/target/debug/.fingerprint/fdeflate-1a67f8c9bc6be976/lib-fdeflate.json
- legacybridge/src-tauri/target/debug/.fingerprint/fdeflate-6561cc89d9584425/lib-fdeflate.json
- legacybridge/src-tauri/target/debug/.fingerprint/fdeflate-fdc3ba469c7432f9/lib-fdeflate.json
- legacybridge/src-tauri/target/debug/.fingerprint/field-offset-0f02695a089a2616/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/field-offset-884226fe82d67a13/lib-field_offset.json
- legacybridge/src-tauri/target/debug/.fingerprint/field-offset-ab071ccc80f0340a/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/field-offset-e282d4ecc77dbce1/lib-field_offset.json
- legacybridge/src-tauri/target/debug/.fingerprint/filetime-31ec814ddf9cbaf1/lib-filetime.json
- legacybridge/src-tauri/target/debug/.fingerprint/fixedbitset-0ad471d3baf3a070/lib-fixedbitset.json
- legacybridge/src-tauri/target/debug/.fingerprint/fixedbitset-26f8dea34c01443a/lib-fixedbitset.json
- legacybridge/src-tauri/target/debug/.fingerprint/flate2-9e9899f0bd0d6aeb/lib-flate2.json
- legacybridge/src-tauri/target/debug/.fingerprint/flate2-c0203fb02d42b8bb/lib-flate2.json
- legacybridge/src-tauri/target/debug/.fingerprint/flate2-f3975ad901c0b851/lib-flate2.json
- legacybridge/src-tauri/target/debug/.fingerprint/fnv-293287ae6d18546d/lib-fnv.json
- legacybridge/src-tauri/target/debug/.fingerprint/fnv-47ced615a30bf5a9/lib-fnv.json
- legacybridge/src-tauri/target/debug/.fingerprint/fnv-e0546c5af4cc890e/lib-fnv.json
- legacybridge/src-tauri/target/debug/.fingerprint/form_urlencoded-8ca2ab51bd96be76/lib-form_urlencoded.json
- legacybridge/src-tauri/target/debug/.fingerprint/form_urlencoded-9d1ba0e264b8f657/lib-form_urlencoded.json
- legacybridge/src-tauri/target/debug/.fingerprint/form_urlencoded-b360c402ab379b06/lib-form_urlencoded.json
- legacybridge/src-tauri/target/debug/.fingerprint/form_urlencoded-e4c073c0b429176c/lib-form_urlencoded.json
- legacybridge/src-tauri/target/debug/.fingerprint/futf-4a4816fed7926697/lib-futf.json
- legacybridge/src-tauri/target/debug/.fingerprint/futf-d250804e04a533fc/lib-futf.json
- legacybridge/src-tauri/target/debug/.fingerprint/futf-f430e80acef82aff/lib-futf.json
- legacybridge/src-tauri/target/debug/.fingerprint/futures-channel-27d0a6d4c699510d/lib-futures_channel.json
- legacybridge/src-tauri/target/debug/.fingerprint/futures-channel-9ea0bf10b0e17f86/lib-futures_channel.json
- legacybridge/src-tauri/target/debug/.fingerprint/futures-core-61cc14625ddc4347/lib-futures_core.json
- legacybridge/src-tauri/target/debug/.fingerprint/futures-core-ca94740c9c4ce7e3/lib-futures_core.json
- legacybridge/src-tauri/target/debug/.fingerprint/futures-executor-366830d3cf7f0285/lib-futures_executor.json
- legacybridge/src-tauri/target/debug/.fingerprint/futures-executor-4fc2a7f5117f3bbd/lib-futures_executor.json
- legacybridge/src-tauri/target/debug/.fingerprint/futures-executor-f5bb197872d50ea9/lib-futures_executor.json
- legacybridge/src-tauri/target/debug/.fingerprint/futures-io-67b82bea7b1e0bc8/lib-futures_io.json
- legacybridge/src-tauri/target/debug/.fingerprint/futures-io-83cfef114f18ef98/lib-futures_io.json
- legacybridge/src-tauri/target/debug/.fingerprint/futures-macro-0775212c9ad295ba/lib-futures_macro.json
- legacybridge/src-tauri/target/debug/.fingerprint/futures-macro-29412c1d89c49486/lib-futures_macro.json
- legacybridge/src-tauri/target/debug/.fingerprint/futures-task-10180464d5af716d/lib-futures_task.json
- legacybridge/src-tauri/target/debug/.fingerprint/futures-task-a3603f61c0b3f23a/lib-futures_task.json
- legacybridge/src-tauri/target/debug/.fingerprint/futures-util-28f632b10974f4e1/lib-futures_util.json
- legacybridge/src-tauri/target/debug/.fingerprint/futures-util-a24462057690f1fe/lib-futures_util.json
- legacybridge/src-tauri/target/debug/.fingerprint/futures-util-ee8ae291faf360e4/lib-futures_util.json
- legacybridge/src-tauri/target/debug/.fingerprint/fxhash-4e2622675201e2c1/lib-fxhash.json
- legacybridge/src-tauri/target/debug/.fingerprint/fxhash-78b4a7d9b8f4391d/lib-fxhash.json
- legacybridge/src-tauri/target/debug/.fingerprint/fxhash-b26902c151e83a2b/lib-fxhash.json
- legacybridge/src-tauri/target/debug/.fingerprint/gdk-5d909011f6f3cd4b/lib-gdk.json
- legacybridge/src-tauri/target/debug/.fingerprint/gdk-9fdef6e5b481c2df/lib-gdk.json
- legacybridge/src-tauri/target/debug/.fingerprint/gdk-cb2f1aa0ea0e66f5/lib-gdk.json
- legacybridge/src-tauri/target/debug/.fingerprint/gdk-pixbuf-5027654f32110eef/lib-gdk_pixbuf.json
- legacybridge/src-tauri/target/debug/.fingerprint/gdk-pixbuf-8acb108648bb00bc/lib-gdk_pixbuf.json
- legacybridge/src-tauri/target/debug/.fingerprint/gdk-pixbuf-e8fb0f477b8c302b/lib-gdk_pixbuf.json
- legacybridge/src-tauri/target/debug/.fingerprint/gdk-pixbuf-sys-2945721dc0240ecc/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/gdk-pixbuf-sys-4dfda644b410bd60/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/gdk-pixbuf-sys-5b73af4567421646/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/gdk-pixbuf-sys-7e5b70e9008a56b5/lib-gdk_pixbuf_sys.json
- legacybridge/src-tauri/target/debug/.fingerprint/gdk-pixbuf-sys-964a1beb046bf1ee/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/gdk-pixbuf-sys-ca3de26bcde2c582/lib-gdk_pixbuf_sys.json
- legacybridge/src-tauri/target/debug/.fingerprint/gdk-pixbuf-sys-d3541bde49872614/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/gdk-pixbuf-sys-d533991ee4da6fa2/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/gdk-pixbuf-sys-ef59e9a8f019218b/lib-gdk_pixbuf_sys.json
- legacybridge/src-tauri/target/debug/.fingerprint/gdk-sys-0eb17dd5b005c861/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/gdk-sys-15b7fb034f97d732/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/gdk-sys-43e02b7901908e3a/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/gdk-sys-53824193dbe08c4c/lib-gdk_sys.json
- legacybridge/src-tauri/target/debug/.fingerprint/gdk-sys-635d681bacff3e8d/lib-gdk_sys.json
- legacybridge/src-tauri/target/debug/.fingerprint/gdk-sys-aadfb34506b1ea64/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/gdk-sys-f7970bc930a0ac24/lib-gdk_sys.json
- legacybridge/src-tauri/target/debug/.fingerprint/gdk-sys-f9f981de3cdeb703/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/gdk-sys-fce6fc738d4d3807/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/gdkwayland-sys-02898417aef2fa90/lib-gdk_wayland_sys.json
- legacybridge/src-tauri/target/debug/.fingerprint/gdkwayland-sys-19e3bbcd4f5dc8d7/lib-gdk_wayland_sys.json
- legacybridge/src-tauri/target/debug/.fingerprint/gdkx11-sys-03b9c556316e4e51/lib-gdk_x11_sys.json
- legacybridge/src-tauri/target/debug/.fingerprint/gdkx11-sys-050fcf8165479069/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/gdkx11-sys-13f1bbfa7087b2cc/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/gdkx11-sys-2589d152aa7a581f/lib-gdk_x11_sys.json
- legacybridge/src-tauri/target/debug/.fingerprint/gdkx11-sys-5086ad119b1bdf70/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/gdkx11-sys-9a57d15c65ea63c6/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/gdkx11-sys-b3b48c399b603989/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/gdkx11-sys-dae9f4cf3f5e65b0/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/gdkx11-sys-f24ee65028c918a8/lib-gdk_x11_sys.json
- legacybridge/src-tauri/target/debug/.fingerprint/generic-array-19356362d6f962e8/lib-generic_array.json
- legacybridge/src-tauri/target/debug/.fingerprint/generic-array-27e0812fe9f1c82e/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/generic-array-4c580ccec29e826a/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/getopts-1e31137d13fd7a2b/lib-getopts.json
- legacybridge/src-tauri/target/debug/.fingerprint/getrandom-0e1cb3a3a72c087b/lib-getrandom.json
- legacybridge/src-tauri/target/debug/.fingerprint/getrandom-1e95800acddc8c06/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/getrandom-5422fbed6c906ea1/lib-getrandom.json
- legacybridge/src-tauri/target/debug/.fingerprint/getrandom-60d7a7452527bf30/lib-getrandom.json
- legacybridge/src-tauri/target/debug/.fingerprint/getrandom-80e01523aec00a2d/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/getrandom-88ee85d9da9a01a7/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/getrandom-9e8b3d5773fcdc6f/lib-getrandom.json
- legacybridge/src-tauri/target/debug/.fingerprint/getrandom-d0affc805db26321/lib-getrandom.json
- legacybridge/src-tauri/target/debug/.fingerprint/getrandom-d711ff5e6df75d7a/lib-getrandom.json
- legacybridge/src-tauri/target/debug/.fingerprint/getrandom-e4a29fbc9a1e6161/lib-getrandom.json
- legacybridge/src-tauri/target/debug/.fingerprint/getrandom-fc59d817df58cfa2/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/getrandom-fe0d3fde63ff8a70/lib-getrandom.json
- legacybridge/src-tauri/target/debug/.fingerprint/gio-028fd2ef48cbcb11/lib-gio.json
- legacybridge/src-tauri/target/debug/.fingerprint/gio-1c397efd1134ebf1/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/gio-4ebc827756b2ca91/lib-gio.json
- legacybridge/src-tauri/target/debug/.fingerprint/gio-c6808de3da4a0800/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/gio-d248e6c6ebd8826c/lib-gio.json
- legacybridge/src-tauri/target/debug/.fingerprint/gio-sys-07e5e72714b11237/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/gio-sys-1af39c63a0dd7a08/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/gio-sys-45613a8edb372e96/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/gio-sys-5108b424cf914ed7/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/gio-sys-705e561ead4a62fb/lib-gio_sys.json
- legacybridge/src-tauri/target/debug/.fingerprint/gio-sys-7fad1f85c92fb357/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/gio-sys-b9fe932b8444a9dd/lib-gio_sys.json
- legacybridge/src-tauri/target/debug/.fingerprint/gio-sys-cc72806b960ed734/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/gio-sys-f72f9115a5c913f0/lib-gio_sys.json
- legacybridge/src-tauri/target/debug/.fingerprint/glib-1de80573a003361f/lib-glib.json
- legacybridge/src-tauri/target/debug/.fingerprint/glib-7575183b23fb216d/lib-glib.json
- legacybridge/src-tauri/target/debug/.fingerprint/glib-b7a6fffe063dc0d5/lib-glib.json
- legacybridge/src-tauri/target/debug/.fingerprint/glib-macros-584794d614268d3d/lib-glib_macros.json
- legacybridge/src-tauri/target/debug/.fingerprint/glib-macros-604edb9f6ca8ca12/lib-glib_macros.json
- legacybridge/src-tauri/target/debug/.fingerprint/glib-macros-978266b1a2a44304/lib-glib_macros.json
- legacybridge/src-tauri/target/debug/.fingerprint/glib-sys-20d5584b056e65fa/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/glib-sys-500543497ac693e5/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/glib-sys-6f0a0032b775b3c5/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/glib-sys-754c2e2a78874024/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/glib-sys-7b8a6573900abe7c/lib-glib_sys.json
- legacybridge/src-tauri/target/debug/.fingerprint/glib-sys-ade0e5603ee4b886/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/glib-sys-b7a77aad1e3df103/lib-glib_sys.json
- legacybridge/src-tauri/target/debug/.fingerprint/glib-sys-c23502e81ac1e516/lib-glib_sys.json
- legacybridge/src-tauri/target/debug/.fingerprint/glib-sys-c8bef42cc21d66a4/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/glob-d440303c68ac7461/lib-glob.json
- legacybridge/src-tauri/target/debug/.fingerprint/glob-dab261d264aad835/lib-glob.json
- legacybridge/src-tauri/target/debug/.fingerprint/glob-efea7dff89a0408c/lib-glob.json
- legacybridge/src-tauri/target/debug/.fingerprint/globset-37b13b58176980ac/lib-globset.json
- legacybridge/src-tauri/target/debug/.fingerprint/gobject-sys-094f7deb6f446137/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/gobject-sys-7ec6399300fecfac/lib-gobject_sys.json
- legacybridge/src-tauri/target/debug/.fingerprint/gobject-sys-8bee13ba87183bc2/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/gobject-sys-8ea4dd36a3e4c326/lib-gobject_sys.json
- legacybridge/src-tauri/target/debug/.fingerprint/gobject-sys-9094ab7433e8eb15/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/gobject-sys-ab2ecd35c364e766/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/gobject-sys-cb4bd6b030de31a6/lib-gobject_sys.json
- legacybridge/src-tauri/target/debug/.fingerprint/gobject-sys-f7eda89ab27014d3/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/gobject-sys-fbdcb4f9de9b08f0/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/gtk-087023b0eb851d01/lib-gtk.json
- legacybridge/src-tauri/target/debug/.fingerprint/gtk-11fbdd83dd3839d8/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/gtk-4969a0b562dde501/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/gtk-c735b59b5a9cfb59/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/gtk-db9482c0c84e3083/lib-gtk.json
- legacybridge/src-tauri/target/debug/.fingerprint/gtk-f4ebdbb0a157cc3e/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/gtk-sys-0920853597789940/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/gtk-sys-626b447aba992085/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/gtk-sys-8fa10c9f3e78c10b/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/gtk-sys-9491cf6aa8f8ca4d/lib-gtk_sys.json
- legacybridge/src-tauri/target/debug/.fingerprint/gtk-sys-9e872a1ac2d79c25/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/gtk-sys-b5494be2186f9699/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/gtk-sys-b97ecd6d8aa5c0f2/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/gtk-sys-c277aeeab182bf32/lib-gtk_sys.json
- legacybridge/src-tauri/target/debug/.fingerprint/gtk-sys-d8c4bbcb310771c9/lib-gtk_sys.json
- legacybridge/src-tauri/target/debug/.fingerprint/gtk3-macros-4fb9b1b9eef70263/lib-gtk3_macros.json
- legacybridge/src-tauri/target/debug/.fingerprint/gtk3-macros-547f4596253a48d6/lib-gtk3_macros.json
- legacybridge/src-tauri/target/debug/.fingerprint/gtk3-macros-d134ec9617326c4c/lib-gtk3_macros.json
- legacybridge/src-tauri/target/debug/.fingerprint/hashbrown-331ea8df9461b71a/lib-hashbrown.json
- legacybridge/src-tauri/target/debug/.fingerprint/hashbrown-6c3187470aeae78e/lib-hashbrown.json
- legacybridge/src-tauri/target/debug/.fingerprint/hashbrown-9ddec0b1511cc5f3/lib-hashbrown.json
- legacybridge/src-tauri/target/debug/.fingerprint/hashbrown-a41a37f53da4b3c0/lib-hashbrown.json
- legacybridge/src-tauri/target/debug/.fingerprint/hashbrown-a7922e91caf03274/lib-hashbrown.json
- legacybridge/src-tauri/target/debug/.fingerprint/hashbrown-d0f10f6dd8e8a795/lib-hashbrown.json
- legacybridge/src-tauri/target/debug/.fingerprint/heck-022ddcbe783e6ebf/lib-heck.json
- legacybridge/src-tauri/target/debug/.fingerprint/heck-a6a7539774212eec/lib-heck.json
- legacybridge/src-tauri/target/debug/.fingerprint/heck-a7d8637379091bcc/lib-heck.json
- legacybridge/src-tauri/target/debug/.fingerprint/heck-bee8b7d8b2f07ad8/lib-heck.json
- legacybridge/src-tauri/target/debug/.fingerprint/heck-eb50d75f7af7f099/lib-heck.json
- legacybridge/src-tauri/target/debug/.fingerprint/html5ever-1ddf287aa82ca871/lib-html5ever.json
- legacybridge/src-tauri/target/debug/.fingerprint/html5ever-2c6960342c57b5f1/lib-html5ever.json
- legacybridge/src-tauri/target/debug/.fingerprint/html5ever-668d35656030c94d/lib-html5ever.json
- legacybridge/src-tauri/target/debug/.fingerprint/html5ever-6bb4dca0526d8a26/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/html5ever-85fcbb0d5da27454/lib-html5ever.json
- legacybridge/src-tauri/target/debug/.fingerprint/html5ever-8bf0ff554a07e358/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/html5ever-92b5ba316adad128/lib-html5ever.json
- legacybridge/src-tauri/target/debug/.fingerprint/html5ever-b22cafebde705370/lib-html5ever.json
- legacybridge/src-tauri/target/debug/.fingerprint/http-afccbdff98b8722a/lib-http.json
- legacybridge/src-tauri/target/debug/.fingerprint/http-range-101c9cb8fd3c61d6/lib-http_range.json
- legacybridge/src-tauri/target/debug/.fingerprint/iana-time-zone-11a5b6b7b6babd6c/lib-iana_time_zone.json
- legacybridge/src-tauri/target/debug/.fingerprint/ico-b1a58dce64aa4334/lib-ico.json
- legacybridge/src-tauri/target/debug/.fingerprint/icu_collections-20fe74e46043cb72/lib-icu_collections.json
- legacybridge/src-tauri/target/debug/.fingerprint/icu_collections-852646c90ccc16df/lib-icu_collections.json
- legacybridge/src-tauri/target/debug/.fingerprint/icu_collections-9ef44ffa0178d254/lib-icu_collections.json
- legacybridge/src-tauri/target/debug/.fingerprint/icu_collections-b32f81d87511a8f9/lib-icu_collections.json
- legacybridge/src-tauri/target/debug/.fingerprint/icu_collections-db98ce615f4408f4/lib-icu_collections.json
- legacybridge/src-tauri/target/debug/.fingerprint/icu_locale_core-19f1a8347a79f823/lib-icu_locale_core.json
- legacybridge/src-tauri/target/debug/.fingerprint/icu_locale_core-2ad5e33d7266649f/lib-icu_locale_core.json
- legacybridge/src-tauri/target/debug/.fingerprint/icu_locale_core-46bef119a0c09619/lib-icu_locale_core.json
- legacybridge/src-tauri/target/debug/.fingerprint/icu_locale_core-83029e1b1200409d/lib-icu_locale_core.json
- legacybridge/src-tauri/target/debug/.fingerprint/icu_locale_core-8c61cb7201150376/lib-icu_locale_core.json
- legacybridge/src-tauri/target/debug/.fingerprint/icu_normalizer-226710c223443378/lib-icu_normalizer.json
- legacybridge/src-tauri/target/debug/.fingerprint/icu_normalizer-71564ae595efbee3/lib-icu_normalizer.json
- legacybridge/src-tauri/target/debug/.fingerprint/icu_normalizer-8ac2327ce7ed12b9/lib-icu_normalizer.json
- legacybridge/src-tauri/target/debug/.fingerprint/icu_normalizer-91918c4821c1969b/lib-icu_normalizer.json
- legacybridge/src-tauri/target/debug/.fingerprint/icu_normalizer-97e673c1264ead32/lib-icu_normalizer.json
- legacybridge/src-tauri/target/debug/.fingerprint/icu_normalizer-f9d5e4d5d7f3ced1/lib-icu_normalizer.json
- legacybridge/src-tauri/target/debug/.fingerprint/icu_normalizer_data-1108723fc1f3855c/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/icu_normalizer_data-2ef95b62657dd4d2/lib-icu_normalizer_data.json
- legacybridge/src-tauri/target/debug/.fingerprint/icu_normalizer_data-8af801d1e279beea/lib-icu_normalizer_data.json
- legacybridge/src-tauri/target/debug/.fingerprint/icu_normalizer_data-908ab14563240597/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/icu_normalizer_data-f68d778e2827f63c/lib-icu_normalizer_data.json
- legacybridge/src-tauri/target/debug/.fingerprint/icu_properties-18e11bce51a9b066/lib-icu_properties.json
- legacybridge/src-tauri/target/debug/.fingerprint/icu_properties-2895627a4c832959/lib-icu_properties.json
- legacybridge/src-tauri/target/debug/.fingerprint/icu_properties-2d004b38c0e47f9f/lib-icu_properties.json
- legacybridge/src-tauri/target/debug/.fingerprint/icu_properties-3238f750c2dd2d7d/lib-icu_properties.json
- legacybridge/src-tauri/target/debug/.fingerprint/icu_properties-6e528be0ad41f19d/lib-icu_properties.json
- legacybridge/src-tauri/target/debug/.fingerprint/icu_properties_data-0f885550de43ec9f/lib-icu_properties_data.json
- legacybridge/src-tauri/target/debug/.fingerprint/icu_properties_data-2d36f9144fa2df16/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/icu_properties_data-416641e4b695464a/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/icu_properties_data-629e8c450fbfb726/lib-icu_properties_data.json
- legacybridge/src-tauri/target/debug/.fingerprint/icu_properties_data-91561ce0fcc0a22a/lib-icu_properties_data.json
- legacybridge/src-tauri/target/debug/.fingerprint/icu_provider-257e0d5c54da3f55/lib-icu_provider.json
- legacybridge/src-tauri/target/debug/.fingerprint/icu_provider-310660fc0e204692/lib-icu_provider.json
- legacybridge/src-tauri/target/debug/.fingerprint/icu_provider-b0e7571008775241/lib-icu_provider.json
- legacybridge/src-tauri/target/debug/.fingerprint/icu_provider-e186fc650e4e614b/lib-icu_provider.json
- legacybridge/src-tauri/target/debug/.fingerprint/icu_provider-f32c6216ee3c0023/lib-icu_provider.json
- legacybridge/src-tauri/target/debug/.fingerprint/ident_case-810f24c216404eb6/lib-ident_case.json
- legacybridge/src-tauri/target/debug/.fingerprint/idna-06fdd47bcc2c5443/lib-idna.json
- legacybridge/src-tauri/target/debug/.fingerprint/idna-18438c3478028861/lib-idna.json
- legacybridge/src-tauri/target/debug/.fingerprint/idna-23a07875e2eb54b3/lib-idna.json
- legacybridge/src-tauri/target/debug/.fingerprint/idna-315a0d6383f22aa2/lib-idna.json
- legacybridge/src-tauri/target/debug/.fingerprint/idna-ac85c27ae3daf848/lib-idna.json
- legacybridge/src-tauri/target/debug/.fingerprint/idna-dc236808f591ed83/lib-idna.json
- legacybridge/src-tauri/target/debug/.fingerprint/idna_adapter-0e7e1ba7438efb99/lib-idna_adapter.json
- legacybridge/src-tauri/target/debug/.fingerprint/idna_adapter-22459ba798f7f2d6/lib-idna_adapter.json
- legacybridge/src-tauri/target/debug/.fingerprint/idna_adapter-653749f690d8fcd1/lib-idna_adapter.json
- legacybridge/src-tauri/target/debug/.fingerprint/idna_adapter-b03a465b980bc780/lib-idna_adapter.json
- legacybridge/src-tauri/target/debug/.fingerprint/idna_adapter-bd693b2fb14fc9fb/lib-idna_adapter.json
- legacybridge/src-tauri/target/debug/.fingerprint/idna_adapter-d2ea1b4ec0db2b53/lib-idna_adapter.json
- legacybridge/src-tauri/target/debug/.fingerprint/ignore-4d8d8033aa2d6a51/lib-ignore.json
- legacybridge/src-tauri/target/debug/.fingerprint/image-0490fb49142f5d42/lib-image.json
- legacybridge/src-tauri/target/debug/.fingerprint/indexmap-185e61acf5ee28dc/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/indexmap-1dc03b6235ac421d/lib-indexmap.json
- legacybridge/src-tauri/target/debug/.fingerprint/indexmap-2ca38de8ea07d198/lib-indexmap.json
- legacybridge/src-tauri/target/debug/.fingerprint/indexmap-3dd5f498375b71dc/lib-indexmap.json
- legacybridge/src-tauri/target/debug/.fingerprint/indexmap-3e0b6701933e443b/lib-indexmap.json
- legacybridge/src-tauri/target/debug/.fingerprint/indexmap-58d13af8978bf8d7/lib-indexmap.json
- legacybridge/src-tauri/target/debug/.fingerprint/indexmap-889c4f020f4bb4ae/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/indexmap-e996facc31a75bd2/lib-indexmap.json
- legacybridge/src-tauri/target/debug/.fingerprint/infer-1f575d245a29172d/lib-infer.json
- legacybridge/src-tauri/target/debug/.fingerprint/infer-7c4eb5c6318c4455/lib-infer.json
- legacybridge/src-tauri/target/debug/.fingerprint/infer-bbc96d40cf3f5535/lib-infer.json
- legacybridge/src-tauri/target/debug/.fingerprint/instant-3c27b86060ad4dc9/lib-instant.json
- legacybridge/src-tauri/target/debug/.fingerprint/itoa-08c0ac77a7f9c5e1/lib-itoa.json
- legacybridge/src-tauri/target/debug/.fingerprint/itoa-09a02d2f7a267e4c/lib-itoa.json
- legacybridge/src-tauri/target/debug/.fingerprint/itoa-2622358e5e622a7a/lib-itoa.json
- legacybridge/src-tauri/target/debug/.fingerprint/itoa-3319deb1e2767d8c/lib-itoa.json
- legacybridge/src-tauri/target/debug/.fingerprint/itoa-a3b339ff7e2fb47f/lib-itoa.json
- legacybridge/src-tauri/target/debug/.fingerprint/itoa-b11a99c3fb91e0ba/lib-itoa.json
- legacybridge/src-tauri/target/debug/.fingerprint/javascriptcore-rs-04e2c00a05d5cc9e/lib-javascriptcore.json
- legacybridge/src-tauri/target/debug/.fingerprint/javascriptcore-rs-84affa4da60a093e/lib-javascriptcore.json
- legacybridge/src-tauri/target/debug/.fingerprint/javascriptcore-rs-sys-0537ee940c0a007d/lib-javascriptcore_rs_sys.json
- legacybridge/src-tauri/target/debug/.fingerprint/javascriptcore-rs-sys-073d6438c67bf780/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/javascriptcore-rs-sys-13b44be74e3e77b3/lib-javascriptcore_rs_sys.json
- legacybridge/src-tauri/target/debug/.fingerprint/javascriptcore-rs-sys-6956bea5ee309ed0/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/javascriptcore-rs-sys-71b03e0e6499c7f0/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/javascriptcore-rs-sys-9b15eddb3f1e62e8/lib-javascriptcore_rs_sys.json
- legacybridge/src-tauri/target/debug/.fingerprint/javascriptcore-rs-sys-a1fdce96018fedf1/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/javascriptcore-rs-sys-a31efcfc3b6604ea/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/javascriptcore-rs-sys-d34b0c6b919ea48f/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/json-patch-18e5351ca73d56b0/lib-json_patch.json
- legacybridge/src-tauri/target/debug/.fingerprint/json-patch-28850890fde08ba0/lib-json_patch.json
- legacybridge/src-tauri/target/debug/.fingerprint/json-patch-2a870914f899a913/lib-json_patch.json
- legacybridge/src-tauri/target/debug/.fingerprint/json-patch-53eb3b675ac091ab/lib-json_patch.json
- legacybridge/src-tauri/target/debug/.fingerprint/json-patch-b579ea71502e60ea/lib-json_patch.json
- legacybridge/src-tauri/target/debug/.fingerprint/json-patch-bbba3d0056899f63/lib-json_patch.json
- legacybridge/src-tauri/target/debug/.fingerprint/jsonptr-105980f6281bae8b/lib-jsonptr.json
- legacybridge/src-tauri/target/debug/.fingerprint/jsonptr-1c946bcc102b26bd/lib-jsonptr.json
- legacybridge/src-tauri/target/debug/.fingerprint/jsonptr-240a52c71815455c/lib-jsonptr.json
- legacybridge/src-tauri/target/debug/.fingerprint/jsonptr-310bd9b73ac48333/lib-jsonptr.json
- legacybridge/src-tauri/target/debug/.fingerprint/jsonptr-8474384e3e81e785/lib-jsonptr.json
- legacybridge/src-tauri/target/debug/.fingerprint/jsonptr-b076a6de1e5a6421/lib-jsonptr.json
- legacybridge/src-tauri/target/debug/.fingerprint/kuchikiki-507aada0826670fb/lib-kuchikiki.json
- legacybridge/src-tauri/target/debug/.fingerprint/kuchikiki-9197e9629973f7f0/lib-kuchikiki.json
- legacybridge/src-tauri/target/debug/.fingerprint/kuchikiki-cc7e9171350ae034/lib-kuchikiki.json
- legacybridge/src-tauri/target/debug/.fingerprint/kuchikiki-d597c1bd4126858b/lib-kuchikiki.json
- legacybridge/src-tauri/target/debug/.fingerprint/kuchikiki-dabb0b7f371af4fc/lib-kuchikiki.json
- legacybridge/src-tauri/target/debug/.fingerprint/kuchikiki-eb6c7208ea5cf3bc/lib-kuchikiki.json
- legacybridge/src-tauri/target/debug/.fingerprint/lazy_static-0b43ed0abed31a7b/lib-lazy_static.json
- legacybridge/src-tauri/target/debug/.fingerprint/lazy_static-a60c6f0d6f5b0a48/lib-lazy_static.json
- legacybridge/src-tauri/target/debug/.fingerprint/legacybridge-84017c341ea2704c/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/legacybridge-96d1cc8ca564adc3/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/legacybridge-e42858f9dec50622/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/legacybridge-fe4568b69958cf9d/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/libappindicator-9d4d06fa63c4a792/lib-libappindicator.json
- legacybridge/src-tauri/target/debug/.fingerprint/libappindicator-bcb429276fe29d42/lib-libappindicator.json
- legacybridge/src-tauri/target/debug/.fingerprint/libappindicator-da92aba58c7187d9/lib-libappindicator.json
- legacybridge/src-tauri/target/debug/.fingerprint/libappindicator-sys-2becbb24336d859b/lib-libappindicator_sys.json
- legacybridge/src-tauri/target/debug/.fingerprint/libappindicator-sys-399fdb190fca0895/lib-libappindicator_sys.json
- legacybridge/src-tauri/target/debug/.fingerprint/libappindicator-sys-db6203a5a9a190f6/lib-libappindicator_sys.json
- legacybridge/src-tauri/target/debug/.fingerprint/libc-46cba43152271a15/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/libc-6b3d6fdfe0f41b70/lib-libc.json
- legacybridge/src-tauri/target/debug/.fingerprint/libc-9c9836c48cf0592d/lib-libc.json
- legacybridge/src-tauri/target/debug/.fingerprint/libc-9d68f4abca27c9af/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/libc-b95e77ed68474f44/lib-libc.json
- legacybridge/src-tauri/target/debug/.fingerprint/libloading-6a650b7558b1b4f9/lib-libloading.json
- legacybridge/src-tauri/target/debug/.fingerprint/libloading-da14d22d1a793ea5/lib-libloading.json
- legacybridge/src-tauri/target/debug/.fingerprint/linux-raw-sys-04ff83a022e41154/lib-linux_raw_sys.json
- legacybridge/src-tauri/target/debug/.fingerprint/linux-raw-sys-39d79d0f85925b3c/lib-linux_raw_sys.json
- legacybridge/src-tauri/target/debug/.fingerprint/linux-raw-sys-789ead2d9c1e45d3/lib-linux_raw_sys.json
- legacybridge/src-tauri/target/debug/.fingerprint/linux-raw-sys-a5eb9065dcdc6b30/lib-linux_raw_sys.json
- legacybridge/src-tauri/target/debug/.fingerprint/litemap-18a5c0d79183effd/lib-litemap.json
- legacybridge/src-tauri/target/debug/.fingerprint/litemap-2646f13c8202c17e/lib-litemap.json
- legacybridge/src-tauri/target/debug/.fingerprint/litemap-99f568b3a9a26fa6/lib-litemap.json
- legacybridge/src-tauri/target/debug/.fingerprint/lock_api-0375b16e9183e098/lib-lock_api.json
- legacybridge/src-tauri/target/debug/.fingerprint/lock_api-91537e545019d5b9/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/lock_api-96adb08174198570/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/lock_api-abdffdb57d5f17ae/lib-lock_api.json
- legacybridge/src-tauri/target/debug/.fingerprint/lock_api-ddb6ee63b8707003/lib-lock_api.json
- legacybridge/src-tauri/target/debug/.fingerprint/log-9457cdfbb6cf2e49/lib-log.json
- legacybridge/src-tauri/target/debug/.fingerprint/log-99b206798956b112/lib-log.json
- legacybridge/src-tauri/target/debug/.fingerprint/log-f0d82592a119a008/lib-log.json
- legacybridge/src-tauri/target/debug/.fingerprint/mac-5d3d644fa06c280c/lib-mac.json
- legacybridge/src-tauri/target/debug/.fingerprint/mac-7e574ecd81117964/lib-mac.json
- legacybridge/src-tauri/target/debug/.fingerprint/mac-a3996de2bb1d5e9b/lib-mac.json
- legacybridge/src-tauri/target/debug/.fingerprint/markup5ever-07f998bb5a49798a/lib-markup5ever.json
- legacybridge/src-tauri/target/debug/.fingerprint/markup5ever-1751389f929aaa91/lib-markup5ever.json
- legacybridge/src-tauri/target/debug/.fingerprint/markup5ever-28ac22535aa9887c/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/markup5ever-2a2d7ef01b2bc166/lib-markup5ever.json
- legacybridge/src-tauri/target/debug/.fingerprint/markup5ever-50ce6a79a58d31c2/lib-markup5ever.json
- legacybridge/src-tauri/target/debug/.fingerprint/markup5ever-8eb356f8420c41f2/lib-markup5ever.json
- legacybridge/src-tauri/target/debug/.fingerprint/markup5ever-a08d44b5d0e7ad23/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/markup5ever-b33736698c260f00/lib-markup5ever.json
- legacybridge/src-tauri/target/debug/.fingerprint/markup5ever-bb555c5e36016e22/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/markup5ever-d368c9bcc6d6e09d/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/matches-3bd284ac216e41c5/lib-matches.json
- legacybridge/src-tauri/target/debug/.fingerprint/matches-6f4b0627d6b2705e/lib-matches.json
- legacybridge/src-tauri/target/debug/.fingerprint/matches-a221264a8fc5d402/lib-matches.json
- legacybridge/src-tauri/target/debug/.fingerprint/memchr-0325c9e8c520c841/lib-memchr.json
- legacybridge/src-tauri/target/debug/.fingerprint/memchr-3b4e17255c9a44e9/lib-memchr.json
- legacybridge/src-tauri/target/debug/.fingerprint/memchr-956eb8a303448e86/lib-memchr.json
- legacybridge/src-tauri/target/debug/.fingerprint/memoffset-95a85221d994e62e/lib-memoffset.json
- legacybridge/src-tauri/target/debug/.fingerprint/memoffset-d32ab160bd8e6022/lib-memoffset.json
- legacybridge/src-tauri/target/debug/.fingerprint/memoffset-d9645b3521a71f66/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/memoffset-f4a6c90d4e3f18eb/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/minimal-lexical-4821177b5920fd08/lib-minimal_lexical.json
- legacybridge/src-tauri/target/debug/.fingerprint/minimal-lexical-7ab26ee8385d3391/lib-minimal_lexical.json
- legacybridge/src-tauri/target/debug/.fingerprint/miniz_oxide-34f0ce6f87a5096b/lib-miniz_oxide.json
- legacybridge/src-tauri/target/debug/.fingerprint/miniz_oxide-3f54dbaf647422e3/lib-miniz_oxide.json
- legacybridge/src-tauri/target/debug/.fingerprint/miniz_oxide-fa1fa3118d70d1b8/lib-miniz_oxide.json
- legacybridge/src-tauri/target/debug/.fingerprint/new_debug_unreachable-5c3350bd7cc9be26/lib-debug_unreachable.json
- legacybridge/src-tauri/target/debug/.fingerprint/new_debug_unreachable-b8aa6216bce7eec3/lib-debug_unreachable.json
- legacybridge/src-tauri/target/debug/.fingerprint/new_debug_unreachable-c8e9799986ec0197/lib-debug_unreachable.json
- legacybridge/src-tauri/target/debug/.fingerprint/nodrop-2ea557c569710dfc/lib-nodrop.json
- legacybridge/src-tauri/target/debug/.fingerprint/nodrop-42e3a3f6b10ad4e8/lib-nodrop.json
- legacybridge/src-tauri/target/debug/.fingerprint/nodrop-aed711551b9f4ff9/lib-nodrop.json
- legacybridge/src-tauri/target/debug/.fingerprint/nom-01a8db5385fcde03/lib-nom.json
- legacybridge/src-tauri/target/debug/.fingerprint/nom-4e6e7f87c2907fc5/lib-nom.json
- legacybridge/src-tauri/target/debug/.fingerprint/num-traits-2cfce387b6a6387c/lib-num_traits.json
- legacybridge/src-tauri/target/debug/.fingerprint/num-traits-4bd4620eb102112a/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/num-traits-db33cf0e2b124d41/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/num-traits-e82b41a37b4a2fe5/lib-num_traits.json
- legacybridge/src-tauri/target/debug/.fingerprint/num_cpus-b64a6908218f4c45/lib-num_cpus.json
- legacybridge/src-tauri/target/debug/.fingerprint/once_cell-140027e2059e248c/lib-once_cell.json
- legacybridge/src-tauri/target/debug/.fingerprint/once_cell-63d5b3f27b4e174c/lib-once_cell.json
- legacybridge/src-tauri/target/debug/.fingerprint/once_cell-ea3f104e8162aad3/lib-once_cell.json
- legacybridge/src-tauri/target/debug/.fingerprint/os_pipe-e8c5c35c242ce730/lib-os_pipe.json
- legacybridge/src-tauri/target/debug/.fingerprint/pango-0a111b0428a4d5b5/lib-pango.json
- legacybridge/src-tauri/target/debug/.fingerprint/pango-426b63f401d97969/lib-pango.json
- legacybridge/src-tauri/target/debug/.fingerprint/pango-a65fffc6c589b738/lib-pango.json
- legacybridge/src-tauri/target/debug/.fingerprint/pango-sys-2985bc37af737028/lib-pango_sys.json
- legacybridge/src-tauri/target/debug/.fingerprint/pango-sys-43f89b1ff9cfcaae/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/pango-sys-57ecb05aec85c355/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/pango-sys-69e2b1b00a4c5636/lib-pango_sys.json
- legacybridge/src-tauri/target/debug/.fingerprint/pango-sys-9d71807840b94978/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/pango-sys-afb7f942e2de8063/lib-pango_sys.json
- legacybridge/src-tauri/target/debug/.fingerprint/pango-sys-c0aa0ff9c7982ef4/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/pango-sys-d3772fa2cce36422/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/pango-sys-f8568520a51c0a78/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/parking_lot-30ea085ccdded2c7/lib-parking_lot.json
- legacybridge/src-tauri/target/debug/.fingerprint/parking_lot-6af260e5b4d822b3/lib-parking_lot.json
- legacybridge/src-tauri/target/debug/.fingerprint/parking_lot-96d1480aac9b4abb/lib-parking_lot.json
- legacybridge/src-tauri/target/debug/.fingerprint/parking_lot-cd0010173e2be4b2/lib-parking_lot.json
- legacybridge/src-tauri/target/debug/.fingerprint/parking_lot_core-45dfc77ead6300d1/lib-parking_lot_core.json
- legacybridge/src-tauri/target/debug/.fingerprint/parking_lot_core-4f738a7fc746933f/lib-parking_lot_core.json
- legacybridge/src-tauri/target/debug/.fingerprint/parking_lot_core-6d0f0b990e41b73b/lib-parking_lot_core.json
- legacybridge/src-tauri/target/debug/.fingerprint/parking_lot_core-970a786237060aee/lib-parking_lot_core.json
- legacybridge/src-tauri/target/debug/.fingerprint/parking_lot_core-999b582fa35630d3/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/parking_lot_core-d5ba795b49a036a8/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/percent-encoding-11747775ba895eae/lib-percent_encoding.json
- legacybridge/src-tauri/target/debug/.fingerprint/percent-encoding-28f54ea3166949e7/lib-percent_encoding.json
- legacybridge/src-tauri/target/debug/.fingerprint/percent-encoding-4a9e4925548880d4/lib-percent_encoding.json
- legacybridge/src-tauri/target/debug/.fingerprint/petgraph-307d76bba5ee5a73/lib-petgraph.json
- legacybridge/src-tauri/target/debug/.fingerprint/petgraph-cfce3afc64b506bd/lib-petgraph.json
- legacybridge/src-tauri/target/debug/.fingerprint/phf-0d775ea852bb3d87/lib-phf.json
- legacybridge/src-tauri/target/debug/.fingerprint/phf-782212fd6680b692/lib-phf.json
- legacybridge/src-tauri/target/debug/.fingerprint/phf-85b8a87378c6fcbf/lib-phf.json
- legacybridge/src-tauri/target/debug/.fingerprint/phf-96af95c83712c346/lib-phf.json
- legacybridge/src-tauri/target/debug/.fingerprint/phf-a0979f4b4cfe5535/lib-phf.json
- legacybridge/src-tauri/target/debug/.fingerprint/phf-bd13fe5dd5ac83a1/lib-phf.json
- legacybridge/src-tauri/target/debug/.fingerprint/phf-c33365e8a8718fb4/lib-phf.json
- legacybridge/src-tauri/target/debug/.fingerprint/phf-cbb1aeba7ca32c67/lib-phf.json
- legacybridge/src-tauri/target/debug/.fingerprint/phf-dec746b66e4dd2a6/lib-phf.json
- legacybridge/src-tauri/target/debug/.fingerprint/phf-e9cfa0cf44479a5b/lib-phf.json
- legacybridge/src-tauri/target/debug/.fingerprint/phf-f2564e2331517187/lib-phf.json
- legacybridge/src-tauri/target/debug/.fingerprint/phf-f81e8bac55ae85a4/lib-phf.json
- legacybridge/src-tauri/target/debug/.fingerprint/phf_codegen-3828660b9914c18b/lib-phf_codegen.json
- legacybridge/src-tauri/target/debug/.fingerprint/phf_codegen-a9cd7449d9056e1b/lib-phf_codegen.json
- legacybridge/src-tauri/target/debug/.fingerprint/phf_codegen-dbfd6ca1e27b5727/lib-phf_codegen.json
- legacybridge/src-tauri/target/debug/.fingerprint/phf_codegen-e750b3670448f27f/lib-phf_codegen.json
- legacybridge/src-tauri/target/debug/.fingerprint/phf_generator-40efa800fa7f3dd9/lib-phf_generator.json
- legacybridge/src-tauri/target/debug/.fingerprint/phf_generator-706a8a191cd82077/lib-phf_generator.json
- legacybridge/src-tauri/target/debug/.fingerprint/phf_generator-8859d9a99cf43f05/lib-phf_generator.json
- legacybridge/src-tauri/target/debug/.fingerprint/phf_generator-8fb01cce3ea05038/lib-phf_generator.json
- legacybridge/src-tauri/target/debug/.fingerprint/phf_generator-bd2755f94b2d463e/lib-phf_generator.json
- legacybridge/src-tauri/target/debug/.fingerprint/phf_generator-c3c7b0deed21b2ce/lib-phf_generator.json
- legacybridge/src-tauri/target/debug/.fingerprint/phf_macros-17c5a7228407edc8/lib-phf_macros.json
- legacybridge/src-tauri/target/debug/.fingerprint/phf_macros-3af391d7b258f28a/lib-phf_macros.json
- legacybridge/src-tauri/target/debug/.fingerprint/phf_macros-424f895def5c58c1/lib-phf_macros.json
- legacybridge/src-tauri/target/debug/.fingerprint/phf_macros-7843641ae9cf5246/lib-phf_macros.json
- legacybridge/src-tauri/target/debug/.fingerprint/phf_macros-dbfd4ea26eb8405c/lib-phf_macros.json
- legacybridge/src-tauri/target/debug/.fingerprint/phf_shared-04b7d42474c9fa4d/lib-phf_shared.json
- legacybridge/src-tauri/target/debug/.fingerprint/phf_shared-05597d41fc3202e2/lib-phf_shared.json
- legacybridge/src-tauri/target/debug/.fingerprint/phf_shared-15225aa9444d5e7a/lib-phf_shared.json
- legacybridge/src-tauri/target/debug/.fingerprint/phf_shared-33a960114c85a186/lib-phf_shared.json
- legacybridge/src-tauri/target/debug/.fingerprint/phf_shared-37f3dc82dc2f84d8/lib-phf_shared.json
- legacybridge/src-tauri/target/debug/.fingerprint/phf_shared-3a9c079c77a48e41/lib-phf_shared.json
- legacybridge/src-tauri/target/debug/.fingerprint/phf_shared-5c8e456e5076966f/lib-phf_shared.json
- legacybridge/src-tauri/target/debug/.fingerprint/phf_shared-8a35e3de8b7da46b/lib-phf_shared.json
- legacybridge/src-tauri/target/debug/.fingerprint/phf_shared-b845dc25ac445e14/lib-phf_shared.json
- legacybridge/src-tauri/target/debug/.fingerprint/phf_shared-bd20b1991185a0ed/lib-phf_shared.json
- legacybridge/src-tauri/target/debug/.fingerprint/pin-project-lite-39d1c3577857fa1a/lib-pin_project_lite.json
- legacybridge/src-tauri/target/debug/.fingerprint/pin-project-lite-8304a63f5944e937/lib-pin_project_lite.json
- legacybridge/src-tauri/target/debug/.fingerprint/pin-utils-41b432c87f555421/lib-pin_utils.json
- legacybridge/src-tauri/target/debug/.fingerprint/pin-utils-4afe9bfe4113f5ad/lib-pin_utils.json
- legacybridge/src-tauri/target/debug/.fingerprint/pkg-config-318096eedab39bc1/lib-pkg_config.json
- legacybridge/src-tauri/target/debug/.fingerprint/png-279240e2369a899b/lib-png.json
- legacybridge/src-tauri/target/debug/.fingerprint/png-885dae1a82a0cc66/lib-png.json
- legacybridge/src-tauri/target/debug/.fingerprint/png-985e70070a142690/lib-png.json
- legacybridge/src-tauri/target/debug/.fingerprint/potential_utf-21042dfa4f36dc5a/lib-potential_utf.json
- legacybridge/src-tauri/target/debug/.fingerprint/potential_utf-2a745722c978770b/lib-potential_utf.json
- legacybridge/src-tauri/target/debug/.fingerprint/potential_utf-912e90817180cd94/lib-potential_utf.json
- legacybridge/src-tauri/target/debug/.fingerprint/potential_utf-d88a9487a43a8587/lib-potential_utf.json
- legacybridge/src-tauri/target/debug/.fingerprint/potential_utf-dfca70f55b7b0ce2/lib-potential_utf.json
- legacybridge/src-tauri/target/debug/.fingerprint/ppv-lite86-0ba84a8c16a66d1f/lib-ppv_lite86.json
- legacybridge/src-tauri/target/debug/.fingerprint/ppv-lite86-2b81e3e9bc1c058a/lib-ppv_lite86.json
- legacybridge/src-tauri/target/debug/.fingerprint/ppv-lite86-9c53b44efc885e9a/lib-ppv_lite86.json
- legacybridge/src-tauri/target/debug/.fingerprint/precomputed-hash-1a50fe4e03dd4de1/lib-precomputed_hash.json
- legacybridge/src-tauri/target/debug/.fingerprint/precomputed-hash-3afebc8e17490e65/lib-precomputed_hash.json
- legacybridge/src-tauri/target/debug/.fingerprint/precomputed-hash-6285410d23fb2201/lib-precomputed_hash.json
- legacybridge/src-tauri/target/debug/.fingerprint/proc-macro-crate-32fa06d696279cf9/lib-proc_macro_crate.json
- legacybridge/src-tauri/target/debug/.fingerprint/proc-macro-crate-c99a93ab47ef8d17/lib-proc_macro_crate.json
- legacybridge/src-tauri/target/debug/.fingerprint/proc-macro-crate-f2341b570a87d61a/lib-proc_macro_crate.json
- legacybridge/src-tauri/target/debug/.fingerprint/proc-macro-error-045b50110a06d8fb/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/proc-macro-error-7d573fda02fedde1/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/proc-macro-error-7fbd9199c146acb7/lib-proc_macro_error.json
- legacybridge/src-tauri/target/debug/.fingerprint/proc-macro-error-attr-c125fabd42371746/lib-proc_macro_error_attr.json
- legacybridge/src-tauri/target/debug/.fingerprint/proc-macro-error-attr-ea676c37298f4260/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/proc-macro-error-attr-fcc5e60b7bca9861/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/proc-macro-hack-6f3ecb64266a5810/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/proc-macro-hack-9c6365aaf3d0c4b6/lib-proc_macro_hack.json
- legacybridge/src-tauri/target/debug/.fingerprint/proc-macro-hack-b024f556a64a9047/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/proc-macro2-12bae66f386eed49/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/proc-macro2-6c7c911819aa3c74/lib-proc_macro2.json
- legacybridge/src-tauri/target/debug/.fingerprint/proc-macro2-88c32e371799c1f7/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/pulldown-cmark-09a56c69468adfc6/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/pulldown-cmark-698f540c2f6f45a6/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/pulldown-cmark-7e955867e8316be9/lib-pulldown_cmark.json
- legacybridge/src-tauri/target/debug/.fingerprint/quick-xml-172c868ad7719047/lib-quick_xml.json
- legacybridge/src-tauri/target/debug/.fingerprint/quick-xml-19d6b4b6c77eb721/lib-quick_xml.json
- legacybridge/src-tauri/target/debug/.fingerprint/quote-982a968c3358e50a/lib-quote.json
- legacybridge/src-tauri/target/debug/.fingerprint/rand-3cae161562329621/lib-rand.json
- legacybridge/src-tauri/target/debug/.fingerprint/rand-a977d438add2bd72/lib-rand.json
- legacybridge/src-tauri/target/debug/.fingerprint/rand-ed43e628842f5c57/lib-rand.json
- legacybridge/src-tauri/target/debug/.fingerprint/rand-ef7d32f381487369/lib-rand.json
- legacybridge/src-tauri/target/debug/.fingerprint/rand-f06d4d9dbd1a111e/lib-rand.json
- legacybridge/src-tauri/target/debug/.fingerprint/rand_chacha-29460cbcdc53ab0e/lib-rand_chacha.json
- legacybridge/src-tauri/target/debug/.fingerprint/rand_chacha-6e37cc665ac14646/lib-rand_chacha.json
- legacybridge/src-tauri/target/debug/.fingerprint/rand_chacha-c8cab7d7ca6d0745/lib-rand_chacha.json
- legacybridge/src-tauri/target/debug/.fingerprint/rand_chacha-dd587978cdcbf24b/lib-rand_chacha.json
- legacybridge/src-tauri/target/debug/.fingerprint/rand_chacha-e3397f72c3ef38fc/lib-rand_chacha.json
- legacybridge/src-tauri/target/debug/.fingerprint/rand_core-7895376a369bdda9/lib-rand_core.json
- legacybridge/src-tauri/target/debug/.fingerprint/rand_core-a1610036feeba558/lib-rand_core.json
- legacybridge/src-tauri/target/debug/.fingerprint/rand_core-df0e3f5638eeb3a1/lib-rand_core.json
- legacybridge/src-tauri/target/debug/.fingerprint/rand_core-eae3602b18e41230/lib-rand_core.json
- legacybridge/src-tauri/target/debug/.fingerprint/rand_core-ee8cd4a9d579b65f/lib-rand_core.json
- legacybridge/src-tauri/target/debug/.fingerprint/rand_pcg-7e7f0dc7eced442e/lib-rand_pcg.json
- legacybridge/src-tauri/target/debug/.fingerprint/rand_pcg-f022e90efd419447/lib-rand_pcg.json
- legacybridge/src-tauri/target/debug/.fingerprint/raw-window-handle-046e5733cac2f3f8/lib-raw_window_handle.json
- legacybridge/src-tauri/target/debug/.fingerprint/raw-window-handle-89f0d9d3f744ff83/lib-raw_window_handle.json
- legacybridge/src-tauri/target/debug/.fingerprint/rayon-4ff8ca5fb762da10/lib-rayon.json
- legacybridge/src-tauri/target/debug/.fingerprint/rayon-core-0d96fe85b924d093/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/rayon-core-63e32c33ac28b7b8/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/rayon-core-d553ee6d6c2c4e0e/lib-rayon_core.json
- legacybridge/src-tauri/target/debug/.fingerprint/regex-automata-194d15b753ec5db1/lib-regex_automata.json
- legacybridge/src-tauri/target/debug/.fingerprint/regex-automata-4d1a9295726817b8/lib-regex_automata.json
- legacybridge/src-tauri/target/debug/.fingerprint/regex-c9535a90ea986019/lib-regex.json
- legacybridge/src-tauri/target/debug/.fingerprint/regex-syntax-0c15c8b98756dc69/lib-regex_syntax.json
- legacybridge/src-tauri/target/debug/.fingerprint/regex-syntax-7628c20512df11bb/lib-regex_syntax.json
- legacybridge/src-tauri/target/debug/.fingerprint/rfd-253f1d70e29e0560/lib-rfd.json
- legacybridge/src-tauri/target/debug/.fingerprint/rfd-2a45c030e0cd2e6a/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/rfd-542c2017fb1a2aa7/lib-rfd.json
- legacybridge/src-tauri/target/debug/.fingerprint/rfd-c0da96b575a0cec3/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/rfd-ff25900a3ec66468/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/rustc_version-d23e013d1f9c7a62/lib-rustc_version.json
- legacybridge/src-tauri/target/debug/.fingerprint/rustix-05a5f3a2434b7163/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/rustix-38bcc0287be8b6c8/lib-rustix.json
- legacybridge/src-tauri/target/debug/.fingerprint/rustix-79a26db31cf3d350/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/rustix-9b229370c65bc4c7/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/rustix-acd08c3f4f9a9139/lib-rustix.json
- legacybridge/src-tauri/target/debug/.fingerprint/rustix-e2908b7dec96fb6a/lib-rustix.json
- legacybridge/src-tauri/target/debug/.fingerprint/rustix-e665478e2e70fbfd/lib-rustix.json
- legacybridge/src-tauri/target/debug/.fingerprint/rustix-fec6bff42e61eb60/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/ryu-1d74fbb8396b52fe/lib-ryu.json
- legacybridge/src-tauri/target/debug/.fingerprint/ryu-27d616b1525b0bd5/lib-ryu.json
- legacybridge/src-tauri/target/debug/.fingerprint/ryu-43e3057b26bcc6dd/lib-ryu.json
- legacybridge/src-tauri/target/debug/.fingerprint/same-file-28d000125fa0aea8/lib-same_file.json
- legacybridge/src-tauri/target/debug/.fingerprint/same-file-885ba4335dbafcd5/lib-same_file.json
- legacybridge/src-tauri/target/debug/.fingerprint/same-file-d14afa9973feedb9/lib-same_file.json
- legacybridge/src-tauri/target/debug/.fingerprint/scopeguard-05a74e14a06af6ee/lib-scopeguard.json
- legacybridge/src-tauri/target/debug/.fingerprint/scopeguard-25b1508fc49a71ee/lib-scopeguard.json
- legacybridge/src-tauri/target/debug/.fingerprint/scopeguard-5790c81990cfeb1b/lib-scopeguard.json
- legacybridge/src-tauri/target/debug/.fingerprint/selectors-0144884d70b7c88c/lib-selectors.json
- legacybridge/src-tauri/target/debug/.fingerprint/selectors-2c0e640945b604ab/lib-selectors.json
- legacybridge/src-tauri/target/debug/.fingerprint/selectors-5b214e196cb51d0b/lib-selectors.json
- legacybridge/src-tauri/target/debug/.fingerprint/selectors-7324ff6e4eafaf6a/lib-selectors.json
- legacybridge/src-tauri/target/debug/.fingerprint/selectors-86d8b2bd3af6677f/lib-selectors.json
- legacybridge/src-tauri/target/debug/.fingerprint/selectors-8c318d3d80208862/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/selectors-c395210e02157667/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/selectors-ca31c95c1c4d95f3/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/selectors-dffdd734e8207cbe/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/selectors-f0e9650ad74b3aa2/lib-selectors.json
- legacybridge/src-tauri/target/debug/.fingerprint/semver-24aa839688b4f383/lib-semver.json
- legacybridge/src-tauri/target/debug/.fingerprint/semver-27155ce1bcaa046f/lib-semver.json
- legacybridge/src-tauri/target/debug/.fingerprint/semver-43b6b9e48f33dd86/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/semver-b6aac3c7cceb0190/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/semver-c67fc956ffbece98/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/semver-f105bdaea43e2301/lib-semver.json
- legacybridge/src-tauri/target/debug/.fingerprint/semver-f8d0bc77d8a9dbf1/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/serde-394bff2edf939c0f/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/serde-720d62ef2ad45f30/lib-serde.json
- legacybridge/src-tauri/target/debug/.fingerprint/serde-a4e39656d26e57b4/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/serde-c99e24849cf3748c/lib-serde.json
- legacybridge/src-tauri/target/debug/.fingerprint/serde-db8916dc3636d64d/lib-serde.json
- legacybridge/src-tauri/target/debug/.fingerprint/serde-f2326cd567507822/lib-serde.json
- legacybridge/src-tauri/target/debug/.fingerprint/serde-f4cf87bffee051d5/lib-serde.json
- legacybridge/src-tauri/target/debug/.fingerprint/serde_derive-281f88a588553224/lib-serde_derive.json
- legacybridge/src-tauri/target/debug/.fingerprint/serde_derive-33f7142bb96b03eb/lib-serde_derive.json
- legacybridge/src-tauri/target/debug/.fingerprint/serde_json-29f41cacaf7fe98d/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/serde_json-3c8dfe85d38cf818/lib-serde_json.json
- legacybridge/src-tauri/target/debug/.fingerprint/serde_json-5cebf9d3cc00f890/lib-serde_json.json
- legacybridge/src-tauri/target/debug/.fingerprint/serde_json-5e3be07eb5e12d7d/lib-serde_json.json
- legacybridge/src-tauri/target/debug/.fingerprint/serde_json-6b1d23ed76d05b29/lib-serde_json.json
- legacybridge/src-tauri/target/debug/.fingerprint/serde_json-834fa8642aba7ab0/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/serde_json-9a10ea942381bebe/lib-serde_json.json
- legacybridge/src-tauri/target/debug/.fingerprint/serde_json-a735741d7d7ddd74/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/serde_json-c3c0acf42029b402/lib-serde_json.json
- legacybridge/src-tauri/target/debug/.fingerprint/serde_json-f56be8f41abff05d/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/serde_repr-3d570e2d57a5ec3a/lib-serde_repr.json
- legacybridge/src-tauri/target/debug/.fingerprint/serde_repr-dc43083ab15736f5/lib-serde_repr.json
- legacybridge/src-tauri/target/debug/.fingerprint/serde_spanned-85d54b5ef88d45f1/lib-serde_spanned.json
- legacybridge/src-tauri/target/debug/.fingerprint/serde_spanned-94ab67727ccff6a5/lib-serde_spanned.json
- legacybridge/src-tauri/target/debug/.fingerprint/serde_spanned-d132382893a2b4af/lib-serde_spanned.json
- legacybridge/src-tauri/target/debug/.fingerprint/serde_with-3a8df88de60ece29/lib-serde_with.json
- legacybridge/src-tauri/target/debug/.fingerprint/serde_with-83a2a6a3691e272f/lib-serde_with.json
- legacybridge/src-tauri/target/debug/.fingerprint/serde_with-a63c79eeb6da9c84/lib-serde_with.json
- legacybridge/src-tauri/target/debug/.fingerprint/serde_with-b47ec93a580e6366/lib-serde_with.json
- legacybridge/src-tauri/target/debug/.fingerprint/serde_with-e3c7181c734a3ab0/lib-serde_with.json
- legacybridge/src-tauri/target/debug/.fingerprint/serde_with_macros-35731ac9b30365bd/lib-serde_with_macros.json
- legacybridge/src-tauri/target/debug/.fingerprint/serde_with_macros-80d47c7f6dd8e02d/lib-serde_with_macros.json
- legacybridge/src-tauri/target/debug/.fingerprint/serde_with_macros-8c6bfe63a8ff87e3/lib-serde_with_macros.json
- legacybridge/src-tauri/target/debug/.fingerprint/serialize-to-javascript-438bb97297560e03/lib-serialize_to_javascript.json
- legacybridge/src-tauri/target/debug/.fingerprint/serialize-to-javascript-504ab1ef215d8668/lib-serialize_to_javascript.json
- legacybridge/src-tauri/target/debug/.fingerprint/serialize-to-javascript-impl-22f7d25bcce3ce98/lib-serialize_to_javascript_impl.json
- legacybridge/src-tauri/target/debug/.fingerprint/serialize-to-javascript-impl-cb708d86aeaee853/lib-serialize_to_javascript_impl.json
- legacybridge/src-tauri/target/debug/.fingerprint/servo_arc-4b63f3cd6688cad2/lib-servo_arc.json
- legacybridge/src-tauri/target/debug/.fingerprint/servo_arc-5fd47fb1fe5ee3b2/lib-servo_arc.json
- legacybridge/src-tauri/target/debug/.fingerprint/servo_arc-c58d39aedd8dc609/lib-servo_arc.json
- legacybridge/src-tauri/target/debug/.fingerprint/sha2-03052023fb561358/lib-sha2.json
- legacybridge/src-tauri/target/debug/.fingerprint/shlex-83c7e0da01d13271/lib-shlex.json
- legacybridge/src-tauri/target/debug/.fingerprint/simd-adler32-0912ca60551a92a3/lib-simd_adler32.json
- legacybridge/src-tauri/target/debug/.fingerprint/simd-adler32-869c65d5c51bf93d/lib-simd_adler32.json
- legacybridge/src-tauri/target/debug/.fingerprint/simd-adler32-ce98ab6204197f68/lib-simd_adler32.json
- legacybridge/src-tauri/target/debug/.fingerprint/siphasher-5b4b36d10fee9677/lib-siphasher.json
- legacybridge/src-tauri/target/debug/.fingerprint/siphasher-93671a6e331badf8/lib-siphasher.json
- legacybridge/src-tauri/target/debug/.fingerprint/siphasher-9687c34e6cce449f/lib-siphasher.json
- legacybridge/src-tauri/target/debug/.fingerprint/siphasher-bbcb89d2f79153d5/lib-siphasher.json
- legacybridge/src-tauri/target/debug/.fingerprint/siphasher-c79b637dfcfe2c39/lib-siphasher.json
- legacybridge/src-tauri/target/debug/.fingerprint/siphasher-eb9f6fcaed9e690a/lib-siphasher.json
- legacybridge/src-tauri/target/debug/.fingerprint/slab-63943b7eb7b60ca4/lib-slab.json
- legacybridge/src-tauri/target/debug/.fingerprint/slab-a3361e62d1c2857b/lib-slab.json
- legacybridge/src-tauri/target/debug/.fingerprint/smallvec-4a26b102a8527c64/lib-smallvec.json
- legacybridge/src-tauri/target/debug/.fingerprint/smallvec-8a9450a91f868832/lib-smallvec.json
- legacybridge/src-tauri/target/debug/.fingerprint/smallvec-8b06ef5a2f3b912c/lib-smallvec.json
- legacybridge/src-tauri/target/debug/.fingerprint/soup2-09320ee8de7b2122/lib-soup.json
- legacybridge/src-tauri/target/debug/.fingerprint/soup2-336f44ecc7a89312/lib-soup.json
- legacybridge/src-tauri/target/debug/.fingerprint/soup2-sys-0625263b65164077/lib-soup_sys.json
- legacybridge/src-tauri/target/debug/.fingerprint/soup2-sys-1aa37dfd64a28155/lib-soup_sys.json
- legacybridge/src-tauri/target/debug/.fingerprint/soup2-sys-41e3429c24b4a160/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/soup2-sys-46d10370304639cb/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/soup2-sys-5b3b09862bc589e6/lib-soup_sys.json
- legacybridge/src-tauri/target/debug/.fingerprint/soup2-sys-74b01d66ad917528/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/soup2-sys-a91d5b4f1daf7431/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/soup2-sys-c0288397650b0a95/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/soup2-sys-e6f6e3fb2f184ef4/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/stable_deref_trait-06ef3ba6e7277ca9/lib-stable_deref_trait.json
- legacybridge/src-tauri/target/debug/.fingerprint/stable_deref_trait-3717159d49df65e5/lib-stable_deref_trait.json
- legacybridge/src-tauri/target/debug/.fingerprint/stable_deref_trait-4e2768f260013686/lib-stable_deref_trait.json
- legacybridge/src-tauri/target/debug/.fingerprint/state-bff724df82a157df/lib-state.json
- legacybridge/src-tauri/target/debug/.fingerprint/string_cache-003cfbe84d119a8d/lib-string_cache.json
- legacybridge/src-tauri/target/debug/.fingerprint/string_cache-055a8cb7ab5c728f/lib-string_cache.json
- legacybridge/src-tauri/target/debug/.fingerprint/string_cache-44e29131a1cb7340/lib-string_cache.json
- legacybridge/src-tauri/target/debug/.fingerprint/string_cache-6602fedda05d02f4/lib-string_cache.json
- legacybridge/src-tauri/target/debug/.fingerprint/string_cache-bcf4a56ef873606a/lib-string_cache.json
- legacybridge/src-tauri/target/debug/.fingerprint/string_cache-c90aab51603d9b47/lib-string_cache.json
- legacybridge/src-tauri/target/debug/.fingerprint/string_cache_codegen-14b86c010a3ec6bd/lib-string_cache_codegen.json
- legacybridge/src-tauri/target/debug/.fingerprint/string_cache_codegen-d76b38f30771c3fc/lib-string_cache_codegen.json
- legacybridge/src-tauri/target/debug/.fingerprint/strsim-ad112818830f0ac6/lib-strsim.json
- legacybridge/src-tauri/target/debug/.fingerprint/syn-384b6654963be71b/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/syn-4409d60084ea512e/lib-syn.json
- legacybridge/src-tauri/target/debug/.fingerprint/syn-5ad6df5f04a8f46f/lib-syn.json
- legacybridge/src-tauri/target/debug/.fingerprint/syn-619c63a240afd599/lib-syn.json
- legacybridge/src-tauri/target/debug/.fingerprint/syn-d96af843025de00a/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/synstructure-2158bcfbc69ed1f3/lib-synstructure.json
- legacybridge/src-tauri/target/debug/.fingerprint/synstructure-c828d09a78bfa4c6/lib-synstructure.json
- legacybridge/src-tauri/target/debug/.fingerprint/system-deps-7f75a13b3bd06ebd/lib-system_deps.json
- legacybridge/src-tauri/target/debug/.fingerprint/system-deps-8c3a496b94be5c3e/lib-system_deps.json
- legacybridge/src-tauri/target/debug/.fingerprint/system-deps-94e2b0ab04cb20fb/lib-system_deps.json
- legacybridge/src-tauri/target/debug/.fingerprint/system-deps-9987863ac3fb89a2/lib-system_deps.json
- legacybridge/src-tauri/target/debug/.fingerprint/system-deps-c8cd8817a6327f4d/lib-system_deps.json
- legacybridge/src-tauri/target/debug/.fingerprint/system-deps-ce1aa3c9156dace3/lib-system_deps.json
- legacybridge/src-tauri/target/debug/.fingerprint/tao-4e65b66ed37deb6c/lib-tao.json
- legacybridge/src-tauri/target/debug/.fingerprint/tao-727d8647765f1e17/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/tao-7448dda8e3bbab92/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/tao-a7eb4a20abef100d/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/tao-ca3ef7af880c6918/lib-tao.json
- legacybridge/src-tauri/target/debug/.fingerprint/tao-d713ccbffa2fd477/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/tar-3c35b4d9e317258b/lib-tar.json
- legacybridge/src-tauri/target/debug/.fingerprint/target-lexicon-368e7e9394533810/lib-target_lexicon.json
- legacybridge/src-tauri/target/debug/.fingerprint/target-lexicon-9871ee972bb88a70/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/target-lexicon-b34e3cb066da1e41/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/tauri-32e9f5f33774a095/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/tauri-33a84912da55141a/lib-tauri.json
- legacybridge/src-tauri/target/debug/.fingerprint/tauri-6943cdc1f04c4c2c/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/tauri-build-34d5c43a116849b4/lib-tauri_build.json
- legacybridge/src-tauri/target/debug/.fingerprint/tauri-build-3b6313e9391eb12d/lib-tauri_build.json
- legacybridge/src-tauri/target/debug/.fingerprint/tauri-codegen-cca8ccf0b966d763/lib-tauri_codegen.json
- legacybridge/src-tauri/target/debug/.fingerprint/tauri-codegen-f87155710e538233/lib-tauri_codegen.json
- legacybridge/src-tauri/target/debug/.fingerprint/tauri-e3113f5030eab91d/lib-tauri.json
- legacybridge/src-tauri/target/debug/.fingerprint/tauri-macros-b757892cc66b6d91/lib-tauri_macros.json
- legacybridge/src-tauri/target/debug/.fingerprint/tauri-macros-baba89261345a8cb/lib-tauri_macros.json
- legacybridge/src-tauri/target/debug/.fingerprint/tauri-runtime-bc8ec3e236669d10/lib-tauri_runtime.json
- legacybridge/src-tauri/target/debug/.fingerprint/tauri-runtime-d0f8dc584e348c86/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/tauri-runtime-dfa30c3175974610/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/tauri-runtime-e4eb080d6972ccad/lib-tauri_runtime.json
- legacybridge/src-tauri/target/debug/.fingerprint/tauri-runtime-wry-47d7cee07c858396/lib-tauri_runtime_wry.json
- legacybridge/src-tauri/target/debug/.fingerprint/tauri-runtime-wry-a477f906092cacf6/lib-tauri_runtime_wry.json
- legacybridge/src-tauri/target/debug/.fingerprint/tauri-runtime-wry-b55ed302205f0c3b/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/tauri-runtime-wry-fa371952ca2e16d2/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/tauri-utils-32d4714e63bc9a82/lib-tauri_utils.json
- legacybridge/src-tauri/target/debug/.fingerprint/tauri-utils-40a8aa9d9c482aca/lib-tauri_utils.json
- legacybridge/src-tauri/target/debug/.fingerprint/tauri-utils-70bdfd547ccf6fc8/lib-tauri_utils.json
- legacybridge/src-tauri/target/debug/.fingerprint/tauri-utils-d5dd6b01fd4e6ccd/lib-tauri_utils.json
- legacybridge/src-tauri/target/debug/.fingerprint/tauri-utils-d7ffa07d4c76685b/lib-tauri_utils.json
- legacybridge/src-tauri/target/debug/.fingerprint/tauri-winres-a34ab1b5bb96b3c7/lib-tauri_winres.json
- legacybridge/src-tauri/target/debug/.fingerprint/tauri-winres-c3dc1938fbd5568a/lib-tauri_winres.json
- legacybridge/src-tauri/target/debug/.fingerprint/tempfile-a710880ba830f7cc/lib-tempfile.json
- legacybridge/src-tauri/target/debug/.fingerprint/tempfile-e9dd2d955365e05e/lib-tempfile.json
- legacybridge/src-tauri/target/debug/.fingerprint/tendril-314eb722d2682e50/lib-tendril.json
- legacybridge/src-tauri/target/debug/.fingerprint/tendril-cb4473ef262c370e/lib-tendril.json
- legacybridge/src-tauri/target/debug/.fingerprint/tendril-cb7ed33bafb67207/lib-tendril.json
- legacybridge/src-tauri/target/debug/.fingerprint/thin-slice-0b0329056f8d6cf0/lib-thin_slice.json
- legacybridge/src-tauri/target/debug/.fingerprint/thin-slice-3fe2cab965fecddb/lib-thin_slice.json
- legacybridge/src-tauri/target/debug/.fingerprint/thin-slice-f613f3809e40789f/lib-thin_slice.json
- legacybridge/src-tauri/target/debug/.fingerprint/thiserror-0071623f7d1c94d3/lib-thiserror.json
- legacybridge/src-tauri/target/debug/.fingerprint/thiserror-03f64d6076313cdd/lib-thiserror.json
- legacybridge/src-tauri/target/debug/.fingerprint/thiserror-04cf86ab6b5dbce9/lib-thiserror.json
- legacybridge/src-tauri/target/debug/.fingerprint/thiserror-0ab41f997ece6c55/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/thiserror-1fe595b50b76ce20/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/thiserror-289b827818904376/lib-thiserror.json
- legacybridge/src-tauri/target/debug/.fingerprint/thiserror-5c7e835cbcf3ecb5/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/thiserror-92e70e684d292252/lib-thiserror.json
- legacybridge/src-tauri/target/debug/.fingerprint/thiserror-b8cd02a6a03190c6/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/thiserror-c979563b3c0b54c4/lib-thiserror.json
- legacybridge/src-tauri/target/debug/.fingerprint/thiserror-e331dfbd12363c1e/lib-thiserror.json
- legacybridge/src-tauri/target/debug/.fingerprint/thiserror-e9973dbc811fb405/lib-thiserror.json
- legacybridge/src-tauri/target/debug/.fingerprint/thiserror-impl-1664de7404b21c0a/lib-thiserror_impl.json
- legacybridge/src-tauri/target/debug/.fingerprint/thiserror-impl-27211b4cd95daa00/lib-thiserror_impl.json
- legacybridge/src-tauri/target/debug/.fingerprint/thiserror-impl-d692a08b242a13d6/lib-thiserror_impl.json
- legacybridge/src-tauri/target/debug/.fingerprint/thiserror-impl-dabd0b86c40bb86a/lib-thiserror_impl.json
- legacybridge/src-tauri/target/debug/.fingerprint/tinystr-2ba9200e65d18b03/lib-tinystr.json
- legacybridge/src-tauri/target/debug/.fingerprint/tinystr-6234c532ac691cb6/lib-tinystr.json
- legacybridge/src-tauri/target/debug/.fingerprint/tinystr-7519b93a22ab0085/lib-tinystr.json
- legacybridge/src-tauri/target/debug/.fingerprint/tinystr-7b2c5abfeda1fff7/lib-tinystr.json
- legacybridge/src-tauri/target/debug/.fingerprint/tinystr-a3c2f6a55fd94176/lib-tinystr.json
- legacybridge/src-tauri/target/debug/.fingerprint/tokio-40fa631170786a68/lib-tokio.json
- legacybridge/src-tauri/target/debug/.fingerprint/toml-13881c10f631aa32/lib-toml.json
- legacybridge/src-tauri/target/debug/.fingerprint/toml-22471f32276f934a/lib-toml.json
- legacybridge/src-tauri/target/debug/.fingerprint/toml-5f55a4d62faebc66/lib-toml.json
- legacybridge/src-tauri/target/debug/.fingerprint/toml-80645993a4f98597/lib-toml.json
- legacybridge/src-tauri/target/debug/.fingerprint/toml-9307f843dc956958/lib-toml.json
- legacybridge/src-tauri/target/debug/.fingerprint/toml-a84b5c7877881904/lib-toml.json
- legacybridge/src-tauri/target/debug/.fingerprint/toml-b2eaebe3f3b483ee/lib-toml.json
- legacybridge/src-tauri/target/debug/.fingerprint/toml-bab7cf93be11a72b/lib-toml.json
- legacybridge/src-tauri/target/debug/.fingerprint/toml-ce4dacb49f59374e/lib-toml.json
- legacybridge/src-tauri/target/debug/.fingerprint/toml_datetime-562ffa14bc469cc3/lib-toml_datetime.json
- legacybridge/src-tauri/target/debug/.fingerprint/toml_datetime-579090d97ffb0d3c/lib-toml_datetime.json
- legacybridge/src-tauri/target/debug/.fingerprint/toml_datetime-b5890dd8435f76f1/lib-toml_datetime.json
- legacybridge/src-tauri/target/debug/.fingerprint/toml_edit-575364a8340a8990/lib-toml_edit.json
- legacybridge/src-tauri/target/debug/.fingerprint/toml_edit-5f6a0a1e53d973af/lib-toml_edit.json
- legacybridge/src-tauri/target/debug/.fingerprint/toml_edit-a3a288b545b4c0a4/lib-toml_edit.json
- legacybridge/src-tauri/target/debug/.fingerprint/toml_edit-b16a026bb7efe927/lib-toml_edit.json
- legacybridge/src-tauri/target/debug/.fingerprint/toml_edit-d0873c403be4efc1/lib-toml_edit.json
- legacybridge/src-tauri/target/debug/.fingerprint/toml_edit-d39b485af756cc37/lib-toml_edit.json
- legacybridge/src-tauri/target/debug/.fingerprint/toml_write-1a1891766be87659/lib-toml_write.json
- legacybridge/src-tauri/target/debug/.fingerprint/tracing-attributes-9da9687d4f0ef589/lib-tracing_attributes.json
- legacybridge/src-tauri/target/debug/.fingerprint/tracing-bdf994c6550ee8a8/lib-tracing.json
- legacybridge/src-tauri/target/debug/.fingerprint/tracing-core-ee875bf11b21d8e3/lib-tracing_core.json
- legacybridge/src-tauri/target/debug/.fingerprint/tree_magic_mini-0248fc753b73ae78/lib-tree_magic_mini.json
- legacybridge/src-tauri/target/debug/.fingerprint/typenum-1f282336f2788d1c/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/typenum-4d9d080de023a2a4/lib-typenum.json
- legacybridge/src-tauri/target/debug/.fingerprint/typenum-911c3e43dcb6b56a/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/unicase-3e5176e8129d926b/lib-unicase.json
- legacybridge/src-tauri/target/debug/.fingerprint/unicode-ident-27b708a2ea44f53b/lib-unicode_ident.json
- legacybridge/src-tauri/target/debug/.fingerprint/unicode-segmentation-012cbb8a3d8a0967/lib-unicode_segmentation.json
- legacybridge/src-tauri/target/debug/.fingerprint/unicode-width-6f55947009843c19/lib-unicode_width.json
- legacybridge/src-tauri/target/debug/.fingerprint/url-16a51d40cd02bc2e/lib-url.json
- legacybridge/src-tauri/target/debug/.fingerprint/url-287541da38bfcf8c/lib-url.json
- legacybridge/src-tauri/target/debug/.fingerprint/url-51d114651c35797c/lib-url.json
- legacybridge/src-tauri/target/debug/.fingerprint/url-632f596c4ecba1a3/lib-url.json
- legacybridge/src-tauri/target/debug/.fingerprint/url-9589c77a79183d81/lib-url.json
- legacybridge/src-tauri/target/debug/.fingerprint/url-e0303ac59a147ddc/lib-url.json
- legacybridge/src-tauri/target/debug/.fingerprint/utf-8-4362425f32391cf6/lib-utf8.json
- legacybridge/src-tauri/target/debug/.fingerprint/utf-8-45e4cfda574298fe/lib-utf8.json
- legacybridge/src-tauri/target/debug/.fingerprint/utf-8-8ea22e4a3188d679/lib-utf8.json
- legacybridge/src-tauri/target/debug/.fingerprint/utf8_iter-25f6afe9a4463498/lib-utf8_iter.json
- legacybridge/src-tauri/target/debug/.fingerprint/utf8_iter-7c80e090d74f7ee1/lib-utf8_iter.json
- legacybridge/src-tauri/target/debug/.fingerprint/utf8_iter-9e5e896a9851b53d/lib-utf8_iter.json
- legacybridge/src-tauri/target/debug/.fingerprint/uuid-636ec8e233ca0c4d/lib-uuid.json
- legacybridge/src-tauri/target/debug/.fingerprint/uuid-809285d5e29b3095/lib-uuid.json
- legacybridge/src-tauri/target/debug/.fingerprint/uuid-c9a2ce954872bd50/lib-uuid.json
- legacybridge/src-tauri/target/debug/.fingerprint/version-compare-25d67372a93d2eb8/lib-version_compare.json
- legacybridge/src-tauri/target/debug/.fingerprint/version-compare-2ec694ebe4912d49/lib-version_compare.json
- legacybridge/src-tauri/target/debug/.fingerprint/version_check-a216429c4cbdde93/lib-version_check.json
- legacybridge/src-tauri/target/debug/.fingerprint/walkdir-61d89ec0cab3a80b/lib-walkdir.json
- legacybridge/src-tauri/target/debug/.fingerprint/walkdir-7293ebc2d93a27a7/lib-walkdir.json
- legacybridge/src-tauri/target/debug/.fingerprint/walkdir-ffb13534afc8db8b/lib-walkdir.json
- legacybridge/src-tauri/target/debug/.fingerprint/wayland-backend-9ef3fb108227bcff/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/wayland-backend-a2d414998e07cb1c/lib-wayland_backend.json
- legacybridge/src-tauri/target/debug/.fingerprint/wayland-backend-ae5c8d64c1a3fe4d/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/wayland-backend-c327b52dee9924e5/lib-wayland_backend.json
- legacybridge/src-tauri/target/debug/.fingerprint/wayland-client-05d0eb12d301d742/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/wayland-client-2e81408e4254c0ae/lib-wayland_client.json
- legacybridge/src-tauri/target/debug/.fingerprint/wayland-client-4b8efd710ff835d6/lib-wayland_client.json
- legacybridge/src-tauri/target/debug/.fingerprint/wayland-client-d1f84827c830d3d1/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/wayland-protocols-7a047c2308d34c91/lib-wayland_protocols.json
- legacybridge/src-tauri/target/debug/.fingerprint/wayland-protocols-e37e2dae757a872c/lib-wayland_protocols.json
- legacybridge/src-tauri/target/debug/.fingerprint/wayland-protocols-wlr-5a951eedeca0f3a4/lib-wayland_protocols_wlr.json
- legacybridge/src-tauri/target/debug/.fingerprint/wayland-protocols-wlr-c86c0d277e51b41b/lib-wayland_protocols_wlr.json
- legacybridge/src-tauri/target/debug/.fingerprint/wayland-scanner-399e89c6af994130/lib-wayland_scanner.json
- legacybridge/src-tauri/target/debug/.fingerprint/wayland-scanner-f2954c72db4ef0f4/lib-wayland_scanner.json
- legacybridge/src-tauri/target/debug/.fingerprint/wayland-sys-11a813dbc5c7fdb6/lib-wayland_sys.json
- legacybridge/src-tauri/target/debug/.fingerprint/wayland-sys-56de9b1e0fa4de0c/lib-wayland_sys.json
- legacybridge/src-tauri/target/debug/.fingerprint/wayland-sys-7af962876794c246/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/wayland-sys-b7062937efdab2c0/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/webkit2gtk-7c522570f96801fd/lib-webkit2gtk.json
- legacybridge/src-tauri/target/debug/.fingerprint/webkit2gtk-84e6ba5a5deb485c/lib-webkit2gtk.json
- legacybridge/src-tauri/target/debug/.fingerprint/webkit2gtk-sys-09a0ad1048712e6b/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/webkit2gtk-sys-183d7d3539e87470/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/webkit2gtk-sys-32e328ce824c440c/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/webkit2gtk-sys-5008f2b1da2f6f75/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/webkit2gtk-sys-758f3b700d47f2fe/lib-webkit2gtk_sys.json
- legacybridge/src-tauri/target/debug/.fingerprint/webkit2gtk-sys-8a6d4c0c4144260e/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/webkit2gtk-sys-a1061a6f6cdd7762/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/webkit2gtk-sys-b0e2f625253ebb96/lib-webkit2gtk_sys.json
- legacybridge/src-tauri/target/debug/.fingerprint/winnow-c3069a2d8d8dfc76/lib-winnow.json
- legacybridge/src-tauri/target/debug/.fingerprint/winnow-d9fd3bd1bb11ad5b/lib-winnow.json
- legacybridge/src-tauri/target/debug/.fingerprint/wl-clipboard-rs-6f3c215f03c66714/lib-wl_clipboard_rs.json
- legacybridge/src-tauri/target/debug/.fingerprint/wl-clipboard-rs-94642c1e4af4fd92/lib-wl_clipboard_rs.json
- legacybridge/src-tauri/target/debug/.fingerprint/writeable-927aa235dd70e018/lib-writeable.json
- legacybridge/src-tauri/target/debug/.fingerprint/writeable-de094abb631f85c5/lib-writeable.json
- legacybridge/src-tauri/target/debug/.fingerprint/writeable-e8805dc05e359f4d/lib-writeable.json
- legacybridge/src-tauri/target/debug/.fingerprint/wry-2f65f9c38e581cc0/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/wry-3f887aee2ed728e8/lib-wry.json
- legacybridge/src-tauri/target/debug/.fingerprint/wry-6309a00417d1af6a/lib-wry.json
- legacybridge/src-tauri/target/debug/.fingerprint/wry-a2ec94b195dbbfc3/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/wry-c9740eca4573c1c1/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/x11-378b8389689223d2/lib-x11.json
- legacybridge/src-tauri/target/debug/.fingerprint/x11-962d9ecac511850d/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/x11-ae5c226dbfb8b393/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/x11-b9bb38845a38fc7d/lib-x11.json
- legacybridge/src-tauri/target/debug/.fingerprint/x11-dl-1ae1c979ee0aa1e0/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/x11-dl-2ef534180b3c5e31/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/x11-dl-804e219dd3d38085/lib-x11_dl.json
- legacybridge/src-tauri/target/debug/.fingerprint/x11-dl-90b74afebe6c4ab4/lib-x11_dl.json
- legacybridge/src-tauri/target/debug/.fingerprint/x11rb-8144c698ce8a865a/lib-x11rb.json
- legacybridge/src-tauri/target/debug/.fingerprint/x11rb-protocol-a363fb236dfc2723/lib-x11rb_protocol.json
- legacybridge/src-tauri/target/debug/.fingerprint/xattr-003ef50764fb681b/lib-xattr.json
- legacybridge/src-tauri/target/debug/.fingerprint/yoke-0b2c8275a11c05b0/lib-yoke.json
- legacybridge/src-tauri/target/debug/.fingerprint/yoke-6ad72ee8895fb7de/lib-yoke.json
- legacybridge/src-tauri/target/debug/.fingerprint/yoke-7cb4dfddc6fda969/lib-yoke.json
- legacybridge/src-tauri/target/debug/.fingerprint/yoke-ceacf2f93206944d/lib-yoke.json
- legacybridge/src-tauri/target/debug/.fingerprint/yoke-derive-5b8c7c9941f40c51/lib-yoke_derive.json
- legacybridge/src-tauri/target/debug/.fingerprint/yoke-derive-9ca36c6689711bb9/lib-yoke_derive.json
- legacybridge/src-tauri/target/debug/.fingerprint/yoke-e5ace750743dae26/lib-yoke.json
- legacybridge/src-tauri/target/debug/.fingerprint/zerocopy-a171ac0550851143/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/zerocopy-ad748e34f50b601f/lib-zerocopy.json
- legacybridge/src-tauri/target/debug/.fingerprint/zerocopy-ae6198574733b8e5/lib-zerocopy.json
- legacybridge/src-tauri/target/debug/.fingerprint/zerocopy-ce37cc0dffdc199b/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/zerocopy-ea79c9b68300a1a8/lib-zerocopy.json
- legacybridge/src-tauri/target/debug/.fingerprint/zerofrom-2461729f00074af3/lib-zerofrom.json
- legacybridge/src-tauri/target/debug/.fingerprint/zerofrom-3d62c126cbf5db1f/lib-zerofrom.json
- legacybridge/src-tauri/target/debug/.fingerprint/zerofrom-5487bce3a513117f/lib-zerofrom.json
- legacybridge/src-tauri/target/debug/.fingerprint/zerofrom-7f4338d5228a00ff/lib-zerofrom.json
- legacybridge/src-tauri/target/debug/.fingerprint/zerofrom-9e788ba133863ff7/lib-zerofrom.json
- legacybridge/src-tauri/target/debug/.fingerprint/zerofrom-derive-1f86de79071eab8c/lib-zerofrom_derive.json
- legacybridge/src-tauri/target/debug/.fingerprint/zerofrom-derive-d3d2e02bcc61a54b/lib-zerofrom_derive.json
- legacybridge/src-tauri/target/debug/.fingerprint/zerotrie-381fe9f7e71eeec3/lib-zerotrie.json
- legacybridge/src-tauri/target/debug/.fingerprint/zerotrie-84613b9f18ddf128/lib-zerotrie.json
- legacybridge/src-tauri/target/debug/.fingerprint/zerotrie-985755d221d95289/lib-zerotrie.json
- legacybridge/src-tauri/target/debug/.fingerprint/zerotrie-a45c635499ef0b38/lib-zerotrie.json
- legacybridge/src-tauri/target/debug/.fingerprint/zerotrie-ec07e89ecb7c08cc/lib-zerotrie.json
- legacybridge/src-tauri/target/debug/.fingerprint/zerovec-0661778d855e18da/lib-zerovec.json
- legacybridge/src-tauri/target/debug/.fingerprint/zerovec-1365f8a3c8991cdd/lib-zerovec.json
- legacybridge/src-tauri/target/debug/.fingerprint/zerovec-76e158057aea6b54/lib-zerovec.json
- legacybridge/src-tauri/target/debug/.fingerprint/zerovec-a9dfe56e4405151e/lib-zerovec.json
- legacybridge/src-tauri/target/debug/.fingerprint/zerovec-c0fffd80c8a64ad9/lib-zerovec.json
- legacybridge/src-tauri/target/debug/.fingerprint/zerovec-derive-c6e46e8abab7a50d/lib-zerovec_derive.json
- legacybridge/src-tauri/target/debug/.fingerprint/zerovec-derive-cf94ffabc0f61b75/lib-zerovec_derive.json
- legacybridge/src-tauri/tauri.conf.json
- legacybridge/test_32bit_compatibility.c
- legacybridge/test_dll.c
- legacybridge/test_dll.py
- legacybridge/tests/accessibility/accessibility-tests.spec.ts
- legacybridge/tests/accessibility/component-accessibility.test.tsx
- legacybridge/tests/chaos/chaos-engineering-tests.spec.ts
- legacybridge/tests/framework/test-automation-framework.ts
- legacybridge/tests/framework/types.ts
- legacybridge/tests/integration/file-conversion-flow.test.tsx
- legacybridge/tests/load/k6-load-test.js
- legacybridge/tests/mocks/fileMock.js
- legacybridge/tests/mocks/styleMock.js
- legacybridge/tests/performance/performance-regression.test.ts
- legacybridge/tests/run-all-tests.js
- legacybridge/tests/security/security-test-suite.ts
- legacybridge/tests/setup/global-setup.ts
- legacybridge/tests/setup/global-teardown.ts
- legacybridge/tests/setup/jest.setup.js
- legacybridge/tests/unit/components/ConversionProgress.test.tsx
- legacybridge/tests/unit/components/DragDropZone.test.tsx
- legacybridge/tests/unit/components/ErrorBoundary.test.tsx
- legacybridge/tests/unit/components/MarkdownPreview.test.tsx
- legacybridge/tests/unit/components/MonitoringDashboard.test.tsx
- legacybridge/tests/unit/stores/files.test.ts
- legacybridge/tests/visual-regression/visual-regression-tests.spec.ts
- legacybridge/tsconfig.json
- tests/load/k6-load-test.js
- validate_performance_claims.py


## Code Changes (2025-07-26 21:11:01)

### Added Files
- .github/workflows/ci.yml
- COMPREHENSIVE_SECURITY_ANALYSIS.json
- docker-compose.yml
- docs/api/interactive-explorer/index.html
- docs/troubleshooting/interactive-wizard/index.html
- helm/legacybridge/Chart.yaml
- helm/legacybridge/values-production.yaml
- helm/legacybridge/values.yaml
- k8s/configmap.yaml
- k8s/deployment-optimized.yaml
- k8s/deployment.yaml
- k8s/ingress.yaml
- k8s/monitoring-stack.yaml
- k8s/monitoring.yaml
- legacybridge/.eslintrc.json
- legacybridge/ENTERPRISE_PACKAGE/MANIFEST.json
- legacybridge/ENTERPRISE_PACKAGE/examples/other/test_dll.c
- legacybridge/ENTERPRISE_PACKAGE/examples/other/test_dll.py
- legacybridge/ENTERPRISE_PACKAGE/include/legacybridge.h
- legacybridge/components.json
- legacybridge/deployment_package/include/legacybridge.h
- legacybridge/frontend-integration-example.ts
- legacybridge/include/legacybridge.h
- legacybridge/jest.config.js
- legacybridge/next.config.ts
- legacybridge/package-lock.json
- legacybridge/package.json
- legacybridge/perf_test.c
- legacybridge/playwright.config.ts
- legacybridge/src/__tests__/security/xss.test.tsx
- legacybridge/src/app/api/monitoring/alerts/route.ts
- legacybridge/src/app/api/monitoring/alerts/slack/route.ts
- legacybridge/src/app/api/monitoring/metrics/route.ts
- legacybridge/src/app/api/monitoring/prometheus/route.ts
- legacybridge/src/app/demo/page.tsx
- legacybridge/src/app/globals-backup.css
- legacybridge/src/app/globals.css
- legacybridge/src/app/home-page.tsx
- legacybridge/src/app/layout.tsx
- legacybridge/src/app/monitoring/page.tsx
- legacybridge/src/app/page.tsx
- legacybridge/src/components/ConversionProgress.tsx
- legacybridge/src/components/DiffView.tsx
- legacybridge/src/components/DownloadManager.tsx
- legacybridge/src/components/DragDropZone.tsx
- legacybridge/src/components/ErrorBoundary.tsx
- legacybridge/src/components/ErrorDisplay.tsx
- legacybridge/src/components/ErrorLogViewer.tsx
- legacybridge/src/components/LoadingAnimation.tsx
- legacybridge/src/components/MarkdownPreview.tsx
- legacybridge/src/components/PreviewPanel.tsx
- legacybridge/src/components/SuccessAnimation.tsx
- legacybridge/src/components/SyntaxHighlighter.tsx
- legacybridge/src/components/ThemeProvider.tsx
- legacybridge/src/components/UnifiedErrorDisplay.tsx
- legacybridge/src/components/layout/Footer.tsx
- legacybridge/src/components/layout/Header.tsx
- legacybridge/src/components/layout/MainLayout.tsx
- legacybridge/src/components/monitoring/BuildProgressRing.tsx
- legacybridge/src/components/monitoring/EnhancedFunctionCallMatrix.tsx
- legacybridge/src/components/monitoring/FunctionCallMatrix.tsx
- legacybridge/src/components/monitoring/LogStreamViewer.tsx
- legacybridge/src/components/monitoring/MonitoringDashboard.tsx
- legacybridge/src/components/monitoring/PerformanceChart.tsx
- legacybridge/src/components/monitoring/SystemHealthCard.tsx
- legacybridge/src/components/monitoring/index.ts
- legacybridge/src/components/ui/alert-dialog.tsx
- legacybridge/src/components/ui/badge.tsx
- legacybridge/src/components/ui/button.tsx
- legacybridge/src/components/ui/card.tsx
- legacybridge/src/components/ui/dropdown-menu.tsx
- legacybridge/src/components/ui/input.tsx
- legacybridge/src/components/ui/label.tsx
- legacybridge/src/components/ui/progress.tsx
- legacybridge/src/components/ui/separator.tsx
- legacybridge/src/components/ui/switch.tsx
- legacybridge/src/components/ui/tabs.tsx
- legacybridge/src/components/ui/tooltip.tsx
- legacybridge/src/enterprise/admin/AdminDashboard.tsx
- legacybridge/src/enterprise/api/enterprise-api.ts
- legacybridge/src/enterprise/audit/audit-logger.ts
- legacybridge/src/enterprise/auth/authentication.ts
- legacybridge/src/enterprise/auth/rbac.ts
- legacybridge/src/enterprise/config/kubernetes-deployment.yaml
- legacybridge/src/enterprise/services/tenant-conversion-service.ts
- legacybridge/src/enterprise/tenancy/tenant-context.ts
- legacybridge/src/hooks/useDebounce.ts
- legacybridge/src/hooks/useMonitoringWebSocket.ts
- legacybridge/src/lib/download-service.ts
- legacybridge/src/lib/error-logger.ts
- legacybridge/src/lib/monitoring/alert-manager.ts
- legacybridge/src/lib/monitoring/websocket-server.ts
- legacybridge/src/lib/sanitizer.ts
- legacybridge/src/lib/stores/files.ts
- legacybridge/src/lib/tauri-api.ts
- legacybridge/src/lib/unified-error-api.ts
- legacybridge/src/lib/utils.ts
- legacybridge/src/middleware.ts
- legacybridge/src/styles/monitoring.css
- legacybridge/src/types/errors.ts
- legacybridge/src/types/index.ts
- legacybridge/src/types/monitoring.ts
- legacybridge/src-tauri/security.config.json
- legacybridge/src-tauri/tauri.conf.json
- legacybridge/test_32bit_compatibility.c
- legacybridge/test_dll.c
- legacybridge/test_dll.py
- legacybridge/tests/accessibility/accessibility-tests.spec.ts
- legacybridge/tests/accessibility/component-accessibility.test.tsx
- legacybridge/tests/chaos/chaos-engineering-tests.spec.ts
- legacybridge/tests/framework/test-automation-framework.ts
- legacybridge/tests/framework/types.ts
- legacybridge/tests/integration/file-conversion-flow.test.tsx
- legacybridge/tests/load/k6-load-test.js
- legacybridge/tests/mocks/fileMock.js
- legacybridge/tests/mocks/styleMock.js
- legacybridge/tests/performance/performance-regression.test.ts
- legacybridge/tests/run-all-tests.js
- legacybridge/tests/security/security-test-suite.ts
- legacybridge/tests/setup/global-setup.ts
- legacybridge/tests/setup/global-teardown.ts
- legacybridge/tests/setup/jest.setup.js
- legacybridge/tests/unit/components/ConversionProgress.test.tsx
- legacybridge/tests/unit/components/DragDropZone.test.tsx
- legacybridge/tests/unit/components/ErrorBoundary.test.tsx
- legacybridge/tests/unit/components/MarkdownPreview.test.tsx
- legacybridge/tests/unit/components/MonitoringDashboard.test.tsx
- legacybridge/tests/unit/stores/files.test.ts
- legacybridge/tests/visual-regression/visual-regression-tests.spec.ts
- legacybridge/tsconfig.json
- tests/load/k6-load-test.js
- validate_performance_claims.py


## Code Changes (2025-07-26 21:00:35)

### Added Files
- .github/workflows/ci.yml
- COMPREHENSIVE_SECURITY_ANALYSIS.json
- docker-compose.yml
- docs/api/interactive-explorer/index.html
- docs/troubleshooting/interactive-wizard/index.html
- helm/legacybridge/Chart.yaml
- helm/legacybridge/values-production.yaml
- helm/legacybridge/values.yaml
- k8s/configmap.yaml
- k8s/deployment-optimized.yaml
- k8s/deployment.yaml
- k8s/ingress.yaml
- k8s/monitoring-stack.yaml
- k8s/monitoring.yaml
- legacybridge/.eslintrc.json
- legacybridge/ENTERPRISE_PACKAGE/MANIFEST.json
- legacybridge/ENTERPRISE_PACKAGE/examples/other/test_dll.c
- legacybridge/ENTERPRISE_PACKAGE/examples/other/test_dll.py
- legacybridge/ENTERPRISE_PACKAGE/include/legacybridge.h
- legacybridge/components.json
- legacybridge/coverage/base.css
- legacybridge/coverage/block-navigation.js
- legacybridge/coverage/coverage-final.json
- legacybridge/coverage/index.html
- legacybridge/coverage/lcov-report/base.css
- legacybridge/coverage/lcov-report/block-navigation.js
- legacybridge/coverage/lcov-report/index.html
- legacybridge/coverage/lcov-report/prettify.css
- legacybridge/coverage/lcov-report/prettify.js
- legacybridge/coverage/lcov-report/sorter.js
- legacybridge/coverage/lcov-report/src/app/api/monitoring/alerts/index.html
- legacybridge/coverage/lcov-report/src/app/api/monitoring/alerts/route.ts.html
- legacybridge/coverage/lcov-report/src/app/api/monitoring/alerts/slack/index.html
- legacybridge/coverage/lcov-report/src/app/api/monitoring/alerts/slack/route.ts.html
- legacybridge/coverage/lcov-report/src/app/api/monitoring/metrics/index.html
- legacybridge/coverage/lcov-report/src/app/api/monitoring/metrics/route.ts.html
- legacybridge/coverage/lcov-report/src/app/api/monitoring/prometheus/index.html
- legacybridge/coverage/lcov-report/src/app/api/monitoring/prometheus/route.ts.html
- legacybridge/coverage/lcov-report/src/app/demo/index.html
- legacybridge/coverage/lcov-report/src/app/demo/page.tsx.html
- legacybridge/coverage/lcov-report/src/app/home-page.tsx.html
- legacybridge/coverage/lcov-report/src/app/index.html
- legacybridge/coverage/lcov-report/src/app/layout.tsx.html
- legacybridge/coverage/lcov-report/src/app/monitoring/index.html
- legacybridge/coverage/lcov-report/src/app/monitoring/page.tsx.html
- legacybridge/coverage/lcov-report/src/app/page.tsx.html
- legacybridge/coverage/lcov-report/src/components/ConversionProgress.tsx.html
- legacybridge/coverage/lcov-report/src/components/DiffView.tsx.html
- legacybridge/coverage/lcov-report/src/components/DownloadManager.tsx.html
- legacybridge/coverage/lcov-report/src/components/DragDropZone.tsx.html
- legacybridge/coverage/lcov-report/src/components/ErrorBoundary.tsx.html
- legacybridge/coverage/lcov-report/src/components/ErrorDisplay.tsx.html
- legacybridge/coverage/lcov-report/src/components/ErrorLogViewer.tsx.html
- legacybridge/coverage/lcov-report/src/components/LoadingAnimation.tsx.html
- legacybridge/coverage/lcov-report/src/components/MarkdownPreview.tsx.html
- legacybridge/coverage/lcov-report/src/components/PreviewPanel.tsx.html
- legacybridge/coverage/lcov-report/src/components/SuccessAnimation.tsx.html
- legacybridge/coverage/lcov-report/src/components/SyntaxHighlighter.tsx.html
- legacybridge/coverage/lcov-report/src/components/ThemeProvider.tsx.html
- legacybridge/coverage/lcov-report/src/components/UnifiedErrorDisplay.tsx.html
- legacybridge/coverage/lcov-report/src/components/index.html
- legacybridge/coverage/lcov-report/src/components/layout/Footer.tsx.html
- legacybridge/coverage/lcov-report/src/components/layout/Header.tsx.html
- legacybridge/coverage/lcov-report/src/components/layout/MainLayout.tsx.html
- legacybridge/coverage/lcov-report/src/components/layout/index.html
- legacybridge/coverage/lcov-report/src/components/monitoring/BuildProgressRing.tsx.html
- legacybridge/coverage/lcov-report/src/components/monitoring/EnhancedFunctionCallMatrix.tsx.html
- legacybridge/coverage/lcov-report/src/components/monitoring/FunctionCallMatrix.tsx.html
- legacybridge/coverage/lcov-report/src/components/monitoring/LogStreamViewer.tsx.html
- legacybridge/coverage/lcov-report/src/components/monitoring/MonitoringDashboard.tsx.html
- legacybridge/coverage/lcov-report/src/components/monitoring/PerformanceChart.tsx.html
- legacybridge/coverage/lcov-report/src/components/monitoring/SystemHealthCard.tsx.html
- legacybridge/coverage/lcov-report/src/components/monitoring/index.html
- legacybridge/coverage/lcov-report/src/components/ui/alert-dialog.tsx.html
- legacybridge/coverage/lcov-report/src/components/ui/badge.tsx.html
- legacybridge/coverage/lcov-report/src/components/ui/button.tsx.html
- legacybridge/coverage/lcov-report/src/components/ui/card.tsx.html
- legacybridge/coverage/lcov-report/src/components/ui/dropdown-menu.tsx.html
- legacybridge/coverage/lcov-report/src/components/ui/index.html
- legacybridge/coverage/lcov-report/src/components/ui/input.tsx.html
- legacybridge/coverage/lcov-report/src/components/ui/label.tsx.html
- legacybridge/coverage/lcov-report/src/components/ui/progress.tsx.html
- legacybridge/coverage/lcov-report/src/components/ui/separator.tsx.html
- legacybridge/coverage/lcov-report/src/components/ui/switch.tsx.html
- legacybridge/coverage/lcov-report/src/components/ui/tabs.tsx.html
- legacybridge/coverage/lcov-report/src/components/ui/tooltip.tsx.html
- legacybridge/coverage/lcov-report/src/enterprise/admin/AdminDashboard.tsx.html
- legacybridge/coverage/lcov-report/src/enterprise/admin/index.html
- legacybridge/coverage/lcov-report/src/enterprise/api/enterprise-api.ts.html
- legacybridge/coverage/lcov-report/src/enterprise/api/index.html
- legacybridge/coverage/lcov-report/src/enterprise/audit/audit-logger.ts.html
- legacybridge/coverage/lcov-report/src/enterprise/audit/index.html
- legacybridge/coverage/lcov-report/src/enterprise/auth/authentication.ts.html
- legacybridge/coverage/lcov-report/src/enterprise/auth/index.html
- legacybridge/coverage/lcov-report/src/enterprise/auth/rbac.ts.html
- legacybridge/coverage/lcov-report/src/enterprise/services/index.html
- legacybridge/coverage/lcov-report/src/enterprise/services/tenant-conversion-service.ts.html
- legacybridge/coverage/lcov-report/src/enterprise/tenancy/index.html
- legacybridge/coverage/lcov-report/src/enterprise/tenancy/tenant-context.ts.html
- legacybridge/coverage/lcov-report/src/hooks/index.html
- legacybridge/coverage/lcov-report/src/hooks/useDebounce.ts.html
- legacybridge/coverage/lcov-report/src/hooks/useMonitoringWebSocket.ts.html
- legacybridge/coverage/lcov-report/src/index.html
- legacybridge/coverage/lcov-report/src/lib/download-service.ts.html
- legacybridge/coverage/lcov-report/src/lib/error-logger.ts.html
- legacybridge/coverage/lcov-report/src/lib/index.html
- legacybridge/coverage/lcov-report/src/lib/monitoring/alert-manager.ts.html
- legacybridge/coverage/lcov-report/src/lib/monitoring/index.html
- legacybridge/coverage/lcov-report/src/lib/monitoring/websocket-server.ts.html
- legacybridge/coverage/lcov-report/src/lib/sanitizer.ts.html
- legacybridge/coverage/lcov-report/src/lib/stores/files.ts.html
- legacybridge/coverage/lcov-report/src/lib/stores/index.html
- legacybridge/coverage/lcov-report/src/lib/tauri-api.ts.html
- legacybridge/coverage/lcov-report/src/lib/unified-error-api.ts.html
- legacybridge/coverage/lcov-report/src/lib/utils.ts.html
- legacybridge/coverage/lcov-report/src/middleware.ts.html
- legacybridge/coverage/prettify.css
- legacybridge/coverage/prettify.js
- legacybridge/coverage/sorter.js
- legacybridge/coverage/src/app/api/monitoring/alerts/index.html
- legacybridge/coverage/src/app/api/monitoring/alerts/route.ts.html
- legacybridge/coverage/src/app/api/monitoring/alerts/slack/index.html
- legacybridge/coverage/src/app/api/monitoring/alerts/slack/route.ts.html
- legacybridge/coverage/src/app/api/monitoring/metrics/index.html
- legacybridge/coverage/src/app/api/monitoring/metrics/route.ts.html
- legacybridge/coverage/src/app/api/monitoring/prometheus/index.html
- legacybridge/coverage/src/app/api/monitoring/prometheus/route.ts.html
- legacybridge/coverage/src/app/demo/index.html
- legacybridge/coverage/src/app/demo/page.tsx.html
- legacybridge/coverage/src/app/home-page.tsx.html
- legacybridge/coverage/src/app/index.html
- legacybridge/coverage/src/app/layout.tsx.html
- legacybridge/coverage/src/app/monitoring/index.html
- legacybridge/coverage/src/app/monitoring/page.tsx.html
- legacybridge/coverage/src/app/page.tsx.html
- legacybridge/coverage/src/components/ConversionProgress.tsx.html
- legacybridge/coverage/src/components/DiffView.tsx.html
- legacybridge/coverage/src/components/DownloadManager.tsx.html
- legacybridge/coverage/src/components/DragDropZone.tsx.html
- legacybridge/coverage/src/components/ErrorBoundary.tsx.html
- legacybridge/coverage/src/components/ErrorDisplay.tsx.html
- legacybridge/coverage/src/components/ErrorLogViewer.tsx.html
- legacybridge/coverage/src/components/LoadingAnimation.tsx.html
- legacybridge/coverage/src/components/MarkdownPreview.tsx.html
- legacybridge/coverage/src/components/PreviewPanel.tsx.html
- legacybridge/coverage/src/components/SuccessAnimation.tsx.html
- legacybridge/coverage/src/components/SyntaxHighlighter.tsx.html
- legacybridge/coverage/src/components/ThemeProvider.tsx.html
- legacybridge/coverage/src/components/UnifiedErrorDisplay.tsx.html
- legacybridge/coverage/src/components/index.html
- legacybridge/coverage/src/components/layout/Footer.tsx.html
- legacybridge/coverage/src/components/layout/Header.tsx.html
- legacybridge/coverage/src/components/layout/MainLayout.tsx.html
- legacybridge/coverage/src/components/layout/index.html
- legacybridge/coverage/src/components/monitoring/BuildProgressRing.tsx.html
- legacybridge/coverage/src/components/monitoring/EnhancedFunctionCallMatrix.tsx.html
- legacybridge/coverage/src/components/monitoring/FunctionCallMatrix.tsx.html
- legacybridge/coverage/src/components/monitoring/LogStreamViewer.tsx.html
- legacybridge/coverage/src/components/monitoring/MonitoringDashboard.tsx.html
- legacybridge/coverage/src/components/monitoring/PerformanceChart.tsx.html
- legacybridge/coverage/src/components/monitoring/SystemHealthCard.tsx.html
- legacybridge/coverage/src/components/monitoring/index.html
- legacybridge/coverage/src/components/ui/alert-dialog.tsx.html
- legacybridge/coverage/src/components/ui/badge.tsx.html
- legacybridge/coverage/src/components/ui/button.tsx.html
- legacybridge/coverage/src/components/ui/card.tsx.html
- legacybridge/coverage/src/components/ui/dropdown-menu.tsx.html
- legacybridge/coverage/src/components/ui/index.html
- legacybridge/coverage/src/components/ui/input.tsx.html
- legacybridge/coverage/src/components/ui/label.tsx.html
- legacybridge/coverage/src/components/ui/progress.tsx.html
- legacybridge/coverage/src/components/ui/separator.tsx.html
- legacybridge/coverage/src/components/ui/switch.tsx.html
- legacybridge/coverage/src/components/ui/tabs.tsx.html
- legacybridge/coverage/src/components/ui/tooltip.tsx.html
- legacybridge/coverage/src/enterprise/admin/AdminDashboard.tsx.html
- legacybridge/coverage/src/enterprise/admin/index.html
- legacybridge/coverage/src/enterprise/api/enterprise-api.ts.html
- legacybridge/coverage/src/enterprise/api/index.html
- legacybridge/coverage/src/enterprise/audit/audit-logger.ts.html
- legacybridge/coverage/src/enterprise/audit/index.html
- legacybridge/coverage/src/enterprise/auth/authentication.ts.html
- legacybridge/coverage/src/enterprise/auth/index.html
- legacybridge/coverage/src/enterprise/auth/rbac.ts.html
- legacybridge/coverage/src/enterprise/services/index.html
- legacybridge/coverage/src/enterprise/services/tenant-conversion-service.ts.html
- legacybridge/coverage/src/enterprise/tenancy/index.html
- legacybridge/coverage/src/enterprise/tenancy/tenant-context.ts.html
- legacybridge/coverage/src/hooks/index.html
- legacybridge/coverage/src/hooks/useDebounce.ts.html
- legacybridge/coverage/src/hooks/useMonitoringWebSocket.ts.html
- legacybridge/coverage/src/index.html
- legacybridge/coverage/src/lib/download-service.ts.html
- legacybridge/coverage/src/lib/error-logger.ts.html
- legacybridge/coverage/src/lib/index.html
- legacybridge/coverage/src/lib/monitoring/alert-manager.ts.html
- legacybridge/coverage/src/lib/monitoring/index.html
- legacybridge/coverage/src/lib/monitoring/websocket-server.ts.html
- legacybridge/coverage/src/lib/sanitizer.ts.html
- legacybridge/coverage/src/lib/stores/files.ts.html
- legacybridge/coverage/src/lib/stores/index.html
- legacybridge/coverage/src/lib/tauri-api.ts.html
- legacybridge/coverage/src/lib/unified-error-api.ts.html
- legacybridge/coverage/src/lib/utils.ts.html
- legacybridge/coverage/src/middleware.ts.html
- legacybridge/deployment_package/include/legacybridge.h
- legacybridge/frontend-integration-example.ts
- legacybridge/include/legacybridge.h
- legacybridge/jest.config.js
- legacybridge/next.config.ts
- legacybridge/package-lock.json
- legacybridge/package.json
- legacybridge/perf_test.c
- legacybridge/playwright.config.ts
- legacybridge/src/__tests__/security/xss.test.tsx
- legacybridge/src/app/api/monitoring/alerts/route.ts
- legacybridge/src/app/api/monitoring/alerts/slack/route.ts
- legacybridge/src/app/api/monitoring/metrics/route.ts
- legacybridge/src/app/api/monitoring/prometheus/route.ts
- legacybridge/src/app/demo/page.tsx
- legacybridge/src/app/globals-backup.css
- legacybridge/src/app/globals.css
- legacybridge/src/app/home-page.tsx
- legacybridge/src/app/layout.tsx
- legacybridge/src/app/monitoring/page.tsx
- legacybridge/src/app/page.tsx
- legacybridge/src/components/ConversionProgress.tsx
- legacybridge/src/components/DiffView.tsx
- legacybridge/src/components/DownloadManager.tsx
- legacybridge/src/components/DragDropZone.tsx
- legacybridge/src/components/ErrorBoundary.tsx
- legacybridge/src/components/ErrorDisplay.tsx
- legacybridge/src/components/ErrorLogViewer.tsx
- legacybridge/src/components/LoadingAnimation.tsx
- legacybridge/src/components/MarkdownPreview.tsx
- legacybridge/src/components/PreviewPanel.tsx
- legacybridge/src/components/SuccessAnimation.tsx
- legacybridge/src/components/SyntaxHighlighter.tsx
- legacybridge/src/components/ThemeProvider.tsx
- legacybridge/src/components/UnifiedErrorDisplay.tsx
- legacybridge/src/components/layout/Footer.tsx
- legacybridge/src/components/layout/Header.tsx
- legacybridge/src/components/layout/MainLayout.tsx
- legacybridge/src/components/monitoring/BuildProgressRing.tsx
- legacybridge/src/components/monitoring/EnhancedFunctionCallMatrix.tsx
- legacybridge/src/components/monitoring/FunctionCallMatrix.tsx
- legacybridge/src/components/monitoring/LogStreamViewer.tsx
- legacybridge/src/components/monitoring/MonitoringDashboard.tsx
- legacybridge/src/components/monitoring/PerformanceChart.tsx
- legacybridge/src/components/monitoring/SystemHealthCard.tsx
- legacybridge/src/components/monitoring/index.ts
- legacybridge/src/components/ui/alert-dialog.tsx
- legacybridge/src/components/ui/badge.tsx
- legacybridge/src/components/ui/button.tsx
- legacybridge/src/components/ui/card.tsx
- legacybridge/src/components/ui/dropdown-menu.tsx
- legacybridge/src/components/ui/input.tsx
- legacybridge/src/components/ui/label.tsx
- legacybridge/src/components/ui/progress.tsx
- legacybridge/src/components/ui/separator.tsx
- legacybridge/src/components/ui/switch.tsx
- legacybridge/src/components/ui/tabs.tsx
- legacybridge/src/components/ui/tooltip.tsx
- legacybridge/src/enterprise/admin/AdminDashboard.tsx
- legacybridge/src/enterprise/api/enterprise-api.ts
- legacybridge/src/enterprise/audit/audit-logger.ts
- legacybridge/src/enterprise/auth/authentication.ts
- legacybridge/src/enterprise/auth/rbac.ts
- legacybridge/src/enterprise/config/kubernetes-deployment.yaml
- legacybridge/src/enterprise/services/tenant-conversion-service.ts
- legacybridge/src/enterprise/tenancy/tenant-context.ts
- legacybridge/src/hooks/useDebounce.ts
- legacybridge/src/hooks/useMonitoringWebSocket.ts
- legacybridge/src/lib/download-service.ts
- legacybridge/src/lib/error-logger.ts
- legacybridge/src/lib/monitoring/alert-manager.ts
- legacybridge/src/lib/monitoring/websocket-server.ts
- legacybridge/src/lib/sanitizer.ts
- legacybridge/src/lib/stores/files.ts
- legacybridge/src/lib/tauri-api.ts
- legacybridge/src/lib/unified-error-api.ts
- legacybridge/src/lib/utils.ts
- legacybridge/src/middleware.ts
- legacybridge/src/styles/monitoring.css
- legacybridge/src/types/errors.ts
- legacybridge/src/types/index.ts
- legacybridge/src/types/monitoring.ts
- legacybridge/src-tauri/security.config.json
- legacybridge/src-tauri/target/.rustc_info.json
- legacybridge/src-tauri/target/debug/.fingerprint/adler2-05d1217c9be2ffb5/lib-adler2.json
- legacybridge/src-tauri/target/debug/.fingerprint/adler2-3e0a0cffe9594183/lib-adler2.json
- legacybridge/src-tauri/target/debug/.fingerprint/ahash-97dcf011a296f4f6/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/ahash-e4e7d86eec43b304/lib-ahash.json
- legacybridge/src-tauri/target/debug/.fingerprint/ahash-fc5a584595c101e1/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/aho-corasick-36eb2058a4386c0f/lib-aho_corasick.json
- legacybridge/src-tauri/target/debug/.fingerprint/aho-corasick-7186a1056f7f5843/lib-aho_corasick.json
- legacybridge/src-tauri/target/debug/.fingerprint/aho-corasick-f9ce5e77ac23dda1/lib-aho_corasick.json
- legacybridge/src-tauri/target/debug/.fingerprint/alloc-no-stdlib-4faf44e5c60a75cc/lib-alloc_no_stdlib.json
- legacybridge/src-tauri/target/debug/.fingerprint/alloc-no-stdlib-decfe55c8f032221/lib-alloc_no_stdlib.json
- legacybridge/src-tauri/target/debug/.fingerprint/alloc-stdlib-8889dec45a50107c/lib-alloc_stdlib.json
- legacybridge/src-tauri/target/debug/.fingerprint/alloc-stdlib-a32f2c3cd43873bf/lib-alloc_stdlib.json
- legacybridge/src-tauri/target/debug/.fingerprint/anyhow-1fea51c98c81cce8/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/anyhow-63f574b2bb605bdb/lib-anyhow.json
- legacybridge/src-tauri/target/debug/.fingerprint/anyhow-ab7828b45f7ca0be/lib-anyhow.json
- legacybridge/src-tauri/target/debug/.fingerprint/anyhow-b3031e426d49d7c9/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/anyhow-b564c6e92acbdeb0/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/anyhow-f834acad29629d96/lib-anyhow.json
- legacybridge/src-tauri/target/debug/.fingerprint/arboard-0dc92af9f7f16130/lib-arboard.json
- legacybridge/src-tauri/target/debug/.fingerprint/atk-46d2e858e5dfff88/lib-atk.json
- legacybridge/src-tauri/target/debug/.fingerprint/atk-d86ccfb1bb7cc594/lib-atk.json
- legacybridge/src-tauri/target/debug/.fingerprint/atk-sys-89fedf3d54ca69fe/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/atk-sys-8fa7f8f19a0e746d/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/atk-sys-e3072c1d8e635a30/lib-atk_sys.json
- legacybridge/src-tauri/target/debug/.fingerprint/autocfg-9280f9c2f723f569/lib-autocfg.json
- legacybridge/src-tauri/target/debug/.fingerprint/base64-8f6ac19e81e48ded/lib-base64.json
- legacybridge/src-tauri/target/debug/.fingerprint/base64-9ded01860c590c16/lib-base64.json
- legacybridge/src-tauri/target/debug/.fingerprint/bitflags-8a4d4b1731737631/lib-bitflags.json
- legacybridge/src-tauri/target/debug/.fingerprint/bitflags-a17809c42b30ead1/lib-bitflags.json
- legacybridge/src-tauri/target/debug/.fingerprint/bitflags-c9e75a596c91d0d8/lib-bitflags.json
- legacybridge/src-tauri/target/debug/.fingerprint/bitflags-fbf89c7b1a2692da/lib-bitflags.json
- legacybridge/src-tauri/target/debug/.fingerprint/block-buffer-84156d25b3c3d385/lib-block_buffer.json
- legacybridge/src-tauri/target/debug/.fingerprint/brotli-decompressor-22147ee2fdc43176/lib-brotli_decompressor.json
- legacybridge/src-tauri/target/debug/.fingerprint/brotli-decompressor-ea66140830592fd8/lib-brotli_decompressor.json
- legacybridge/src-tauri/target/debug/.fingerprint/brotli-e27bbcef34a06fa3/lib-brotli.json
- legacybridge/src-tauri/target/debug/.fingerprint/brotli-ff791bae63f9755e/lib-brotli.json
- legacybridge/src-tauri/target/debug/.fingerprint/bstr-561d75d2881d0b48/lib-bstr.json
- legacybridge/src-tauri/target/debug/.fingerprint/bumpalo-c6c6bb635c4686db/lib-bumpalo.json
- legacybridge/src-tauri/target/debug/.fingerprint/bytemuck-881637ac429b7db2/lib-bytemuck.json
- legacybridge/src-tauri/target/debug/.fingerprint/byteorder-128526876fcc1990/lib-byteorder.json
- legacybridge/src-tauri/target/debug/.fingerprint/byteorder-d2dd8daca59f888a/lib-byteorder.json
- legacybridge/src-tauri/target/debug/.fingerprint/byteorder-lite-c85076c3565f09a8/lib-byteorder_lite.json
- legacybridge/src-tauri/target/debug/.fingerprint/bytes-8b7319ab20ce4b75/lib-bytes.json
- legacybridge/src-tauri/target/debug/.fingerprint/cairo-rs-1d6c97cf3160fa30/lib-cairo.json
- legacybridge/src-tauri/target/debug/.fingerprint/cairo-rs-ca178e2e8d34ea0c/lib-cairo.json
- legacybridge/src-tauri/target/debug/.fingerprint/cairo-sys-rs-0a247bc1c3a9e517/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/cairo-sys-rs-229fb8de0bde8d1a/lib-cairo_sys.json
- legacybridge/src-tauri/target/debug/.fingerprint/cairo-sys-rs-fdb0f5e6b919583b/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/cargo_toml-77ad7102d83e8f5b/lib-cargo_toml.json
- legacybridge/src-tauri/target/debug/.fingerprint/cargo_toml-a769a335a2cdd1d0/lib-cargo_toml.json
- legacybridge/src-tauri/target/debug/.fingerprint/cc-2057a129cd2a02c3/lib-cc.json
- legacybridge/src-tauri/target/debug/.fingerprint/cfb-74a22b11b1942765/lib-cfb.json
- legacybridge/src-tauri/target/debug/.fingerprint/cfb-bbde625e5371f03b/lib-cfb.json
- legacybridge/src-tauri/target/debug/.fingerprint/cfb-f2f4ca72aec948b8/lib-cfb.json
- legacybridge/src-tauri/target/debug/.fingerprint/cfg-expr-67dc737b391e60b1/lib-cfg_expr.json
- legacybridge/src-tauri/target/debug/.fingerprint/cfg-expr-6dac77e90a9fc2e9/lib-cfg_expr.json
- legacybridge/src-tauri/target/debug/.fingerprint/cfg-if-37c365409298422e/lib-cfg_if.json
- legacybridge/src-tauri/target/debug/.fingerprint/cfg-if-b090a885f2700b8b/lib-cfg_if.json
- legacybridge/src-tauri/target/debug/.fingerprint/chrono-01ac6bc1c6c09ee7/lib-chrono.json
- legacybridge/src-tauri/target/debug/.fingerprint/chrono-eb3eb03a16e412b5/lib-chrono.json
- legacybridge/src-tauri/target/debug/.fingerprint/convert_case-0928d336c7f239df/lib-convert_case.json
- legacybridge/src-tauri/target/debug/.fingerprint/cpufeatures-e69806b0dee56216/lib-cpufeatures.json
- legacybridge/src-tauri/target/debug/.fingerprint/crc32fast-3a22bbf460790681/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/crc32fast-5466925a00199899/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/crc32fast-81e12e74c410a72f/lib-crc32fast.json
- legacybridge/src-tauri/target/debug/.fingerprint/crc32fast-d6edb6a16f9972cf/lib-crc32fast.json
- legacybridge/src-tauri/target/debug/.fingerprint/crossbeam-channel-440c1c64ccffe30f/lib-crossbeam_channel.json
- legacybridge/src-tauri/target/debug/.fingerprint/crossbeam-deque-db865e64ae5e2f3c/lib-crossbeam_deque.json
- legacybridge/src-tauri/target/debug/.fingerprint/crossbeam-epoch-56f02b583a0b8539/lib-crossbeam_epoch.json
- legacybridge/src-tauri/target/debug/.fingerprint/crossbeam-utils-250510a4114e5a1e/lib-crossbeam_utils.json
- legacybridge/src-tauri/target/debug/.fingerprint/crossbeam-utils-2e7512e383e7552c/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/crossbeam-utils-7b9bd07d9fad49b5/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/crypto-common-02607a0a764a683c/lib-crypto_common.json
- legacybridge/src-tauri/target/debug/.fingerprint/cssparser-19e915fe0fb7f122/lib-cssparser.json
- legacybridge/src-tauri/target/debug/.fingerprint/cssparser-4ee9e8994b43f3a4/lib-cssparser.json
- legacybridge/src-tauri/target/debug/.fingerprint/cssparser-67ff2aad3c2bb83c/lib-cssparser.json
- legacybridge/src-tauri/target/debug/.fingerprint/cssparser-7051fcf6ccacbbcc/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/cssparser-8242c271a5aca0f3/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/cssparser-88dfc4866491fe5b/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/cssparser-e8ce5d8e777f0448/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/cssparser-f0ed05fb55ef0b87/lib-cssparser.json
- legacybridge/src-tauri/target/debug/.fingerprint/cssparser-macros-578486a42167507d/lib-cssparser_macros.json
- legacybridge/src-tauri/target/debug/.fingerprint/cssparser-macros-a583eb56892ac155/lib-cssparser_macros.json
- legacybridge/src-tauri/target/debug/.fingerprint/ctor-53e47902ad2b4fd9/lib-ctor.json
- legacybridge/src-tauri/target/debug/.fingerprint/ctor-79da51bea8ab36ca/lib-ctor.json
- legacybridge/src-tauri/target/debug/.fingerprint/darling-6aed921307f2a509/lib-darling.json
- legacybridge/src-tauri/target/debug/.fingerprint/darling-f08373290550826a/lib-darling.json
- legacybridge/src-tauri/target/debug/.fingerprint/darling_core-1131837b6eb9a635/lib-darling_core.json
- legacybridge/src-tauri/target/debug/.fingerprint/darling_core-d506697b766bad96/lib-darling_core.json
- legacybridge/src-tauri/target/debug/.fingerprint/darling_macro-519f60fba526dd3c/lib-darling_macro.json
- legacybridge/src-tauri/target/debug/.fingerprint/darling_macro-f391f42b6e0e5b10/lib-darling_macro.json
- legacybridge/src-tauri/target/debug/.fingerprint/derive_more-c160597a889afc85/lib-derive_more.json
- legacybridge/src-tauri/target/debug/.fingerprint/derive_more-cc207d130151d689/lib-derive_more.json
- legacybridge/src-tauri/target/debug/.fingerprint/digest-cfca3daec1be695c/lib-digest.json
- legacybridge/src-tauri/target/debug/.fingerprint/dirs-next-8fdda3b2a0ef922a/lib-dirs_next.json
- legacybridge/src-tauri/target/debug/.fingerprint/dirs-next-b7619c14e78f6b45/lib-dirs_next.json
- legacybridge/src-tauri/target/debug/.fingerprint/dirs-sys-next-239eeaf310ffe1d5/lib-dirs_sys_next.json
- legacybridge/src-tauri/target/debug/.fingerprint/dirs-sys-next-d1a67c7ae8fb3ae3/lib-dirs_sys_next.json
- legacybridge/src-tauri/target/debug/.fingerprint/displaydoc-65b9a4a917e33ba9/lib-displaydoc.json
- legacybridge/src-tauri/target/debug/.fingerprint/displaydoc-f09e92e998eb8f82/lib-displaydoc.json
- legacybridge/src-tauri/target/debug/.fingerprint/downcast-rs-9d238031b6b2c4e9/lib-downcast_rs.json
- legacybridge/src-tauri/target/debug/.fingerprint/dtoa-64d4d1636072386c/lib-dtoa.json
- legacybridge/src-tauri/target/debug/.fingerprint/dtoa-b5e155b33c11d683/lib-dtoa.json
- legacybridge/src-tauri/target/debug/.fingerprint/dtoa-short-39cf53b0d571b673/lib-dtoa_short.json
- legacybridge/src-tauri/target/debug/.fingerprint/dtoa-short-b4b2626e931a289a/lib-dtoa_short.json
- legacybridge/src-tauri/target/debug/.fingerprint/dunce-9c9ab31823e0a1cd/lib-dunce.json
- legacybridge/src-tauri/target/debug/.fingerprint/dunce-fcaf53e054e92b7e/lib-dunce.json
- legacybridge/src-tauri/target/debug/.fingerprint/either-6324834661ac878a/lib-either.json
- legacybridge/src-tauri/target/debug/.fingerprint/embed-resource-2527ce08b4d4ac24/lib-embed_resource.json
- legacybridge/src-tauri/target/debug/.fingerprint/embed-resource-65333153efe21872/lib-embed_resource.json
- legacybridge/src-tauri/target/debug/.fingerprint/encoding_rs-927c849606bf76fa/lib-encoding_rs.json
- legacybridge/src-tauri/target/debug/.fingerprint/equivalent-9ff5cbb44f5b198f/lib-equivalent.json
- legacybridge/src-tauri/target/debug/.fingerprint/equivalent-f34383553d737d05/lib-equivalent.json
- legacybridge/src-tauri/target/debug/.fingerprint/fastrand-85d33d4943525743/lib-fastrand.json
- legacybridge/src-tauri/target/debug/.fingerprint/fdeflate-1a67f8c9bc6be976/lib-fdeflate.json
- legacybridge/src-tauri/target/debug/.fingerprint/fdeflate-6561cc89d9584425/lib-fdeflate.json
- legacybridge/src-tauri/target/debug/.fingerprint/field-offset-0f02695a089a2616/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/field-offset-884226fe82d67a13/lib-field_offset.json
- legacybridge/src-tauri/target/debug/.fingerprint/field-offset-ab071ccc80f0340a/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/filetime-31ec814ddf9cbaf1/lib-filetime.json
- legacybridge/src-tauri/target/debug/.fingerprint/fixedbitset-26f8dea34c01443a/lib-fixedbitset.json
- legacybridge/src-tauri/target/debug/.fingerprint/flate2-9e9899f0bd0d6aeb/lib-flate2.json
- legacybridge/src-tauri/target/debug/.fingerprint/flate2-c0203fb02d42b8bb/lib-flate2.json
- legacybridge/src-tauri/target/debug/.fingerprint/fnv-47ced615a30bf5a9/lib-fnv.json
- legacybridge/src-tauri/target/debug/.fingerprint/fnv-e0546c5af4cc890e/lib-fnv.json
- legacybridge/src-tauri/target/debug/.fingerprint/form_urlencoded-8ca2ab51bd96be76/lib-form_urlencoded.json
- legacybridge/src-tauri/target/debug/.fingerprint/form_urlencoded-e4c073c0b429176c/lib-form_urlencoded.json
- legacybridge/src-tauri/target/debug/.fingerprint/futf-4a4816fed7926697/lib-futf.json
- legacybridge/src-tauri/target/debug/.fingerprint/futf-f430e80acef82aff/lib-futf.json
- legacybridge/src-tauri/target/debug/.fingerprint/futures-channel-27d0a6d4c699510d/lib-futures_channel.json
- legacybridge/src-tauri/target/debug/.fingerprint/futures-core-ca94740c9c4ce7e3/lib-futures_core.json
- legacybridge/src-tauri/target/debug/.fingerprint/futures-executor-4fc2a7f5117f3bbd/lib-futures_executor.json
- legacybridge/src-tauri/target/debug/.fingerprint/futures-io-67b82bea7b1e0bc8/lib-futures_io.json
- legacybridge/src-tauri/target/debug/.fingerprint/futures-macro-29412c1d89c49486/lib-futures_macro.json
- legacybridge/src-tauri/target/debug/.fingerprint/futures-task-a3603f61c0b3f23a/lib-futures_task.json
- legacybridge/src-tauri/target/debug/.fingerprint/futures-util-28f632b10974f4e1/lib-futures_util.json
- legacybridge/src-tauri/target/debug/.fingerprint/fxhash-78b4a7d9b8f4391d/lib-fxhash.json
- legacybridge/src-tauri/target/debug/.fingerprint/fxhash-b26902c151e83a2b/lib-fxhash.json
- legacybridge/src-tauri/target/debug/.fingerprint/gdk-a0c39b0444eb3856/lib-gdk.json
- legacybridge/src-tauri/target/debug/.fingerprint/gdk-cb2f1aa0ea0e66f5/lib-gdk.json
- legacybridge/src-tauri/target/debug/.fingerprint/gdk-pixbuf-5027654f32110eef/lib-gdk_pixbuf.json
- legacybridge/src-tauri/target/debug/.fingerprint/gdk-pixbuf-8a369c874ca0cef5/lib-gdk_pixbuf.json
- legacybridge/src-tauri/target/debug/.fingerprint/gdk-pixbuf-sys-7e5b70e9008a56b5/lib-gdk_pixbuf_sys.json
- legacybridge/src-tauri/target/debug/.fingerprint/gdk-pixbuf-sys-964a1beb046bf1ee/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/gdk-pixbuf-sys-d3541bde49872614/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/gdk-sys-15b7fb034f97d732/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/gdk-sys-53824193dbe08c4c/lib-gdk_sys.json
- legacybridge/src-tauri/target/debug/.fingerprint/gdk-sys-fce6fc738d4d3807/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/gdkwayland-sys-19e3bbcd4f5dc8d7/lib-gdk_wayland_sys.json
- legacybridge/src-tauri/target/debug/.fingerprint/gdkx11-sys-050fcf8165479069/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/gdkx11-sys-5086ad119b1bdf70/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/gdkx11-sys-f24ee65028c918a8/lib-gdk_x11_sys.json
- legacybridge/src-tauri/target/debug/.fingerprint/generic-array-19356362d6f962e8/lib-generic_array.json
- legacybridge/src-tauri/target/debug/.fingerprint/generic-array-27e0812fe9f1c82e/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/generic-array-4c580ccec29e826a/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/getopts-1e31137d13fd7a2b/lib-getopts.json
- legacybridge/src-tauri/target/debug/.fingerprint/getrandom-1e95800acddc8c06/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/getrandom-60d7a7452527bf30/lib-getrandom.json
- legacybridge/src-tauri/target/debug/.fingerprint/getrandom-80e01523aec00a2d/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/getrandom-88ee85d9da9a01a7/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/getrandom-9e8b3d5773fcdc6f/lib-getrandom.json
- legacybridge/src-tauri/target/debug/.fingerprint/getrandom-d0affc805db26321/lib-getrandom.json
- legacybridge/src-tauri/target/debug/.fingerprint/getrandom-d711ff5e6df75d7a/lib-getrandom.json
- legacybridge/src-tauri/target/debug/.fingerprint/getrandom-fc59d817df58cfa2/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/getrandom-fe0d3fde63ff8a70/lib-getrandom.json
- legacybridge/src-tauri/target/debug/.fingerprint/gio-1c397efd1134ebf1/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/gio-4ebc827756b2ca91/lib-gio.json
- legacybridge/src-tauri/target/debug/.fingerprint/gio-c6808de3da4a0800/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/gio-ee56ef124b80c203/lib-gio.json
- legacybridge/src-tauri/target/debug/.fingerprint/gio-sys-1af39c63a0dd7a08/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/gio-sys-705e561ead4a62fb/lib-gio_sys.json
- legacybridge/src-tauri/target/debug/.fingerprint/gio-sys-7fad1f85c92fb357/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/glib-7575183b23fb216d/lib-glib.json
- legacybridge/src-tauri/target/debug/.fingerprint/glib-c3ed3a78ff01bad1/lib-glib.json
- legacybridge/src-tauri/target/debug/.fingerprint/glib-macros-978266b1a2a44304/lib-glib_macros.json
- legacybridge/src-tauri/target/debug/.fingerprint/glib-sys-20d5584b056e65fa/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/glib-sys-6f0a0032b775b3c5/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/glib-sys-7b8a6573900abe7c/lib-glib_sys.json
- legacybridge/src-tauri/target/debug/.fingerprint/glob-d440303c68ac7461/lib-glob.json
- legacybridge/src-tauri/target/debug/.fingerprint/glob-dab261d264aad835/lib-glob.json
- legacybridge/src-tauri/target/debug/.fingerprint/globset-37b13b58176980ac/lib-globset.json
- legacybridge/src-tauri/target/debug/.fingerprint/gobject-sys-8bee13ba87183bc2/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/gobject-sys-cb4bd6b030de31a6/lib-gobject_sys.json
- legacybridge/src-tauri/target/debug/.fingerprint/gobject-sys-f7eda89ab27014d3/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/gtk-087023b0eb851d01/lib-gtk.json
- legacybridge/src-tauri/target/debug/.fingerprint/gtk-11fbdd83dd3839d8/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/gtk-4969a0b562dde501/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/gtk-b8328d2862175563/lib-gtk.json
- legacybridge/src-tauri/target/debug/.fingerprint/gtk-sys-626b447aba992085/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/gtk-sys-b97ecd6d8aa5c0f2/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/gtk-sys-c277aeeab182bf32/lib-gtk_sys.json
- legacybridge/src-tauri/target/debug/.fingerprint/gtk3-macros-547f4596253a48d6/lib-gtk3_macros.json
- legacybridge/src-tauri/target/debug/.fingerprint/hashbrown-331ea8df9461b71a/lib-hashbrown.json
- legacybridge/src-tauri/target/debug/.fingerprint/hashbrown-9ddec0b1511cc5f3/lib-hashbrown.json
- legacybridge/src-tauri/target/debug/.fingerprint/hashbrown-a41a37f53da4b3c0/lib-hashbrown.json
- legacybridge/src-tauri/target/debug/.fingerprint/hashbrown-d0f10f6dd8e8a795/lib-hashbrown.json
- legacybridge/src-tauri/target/debug/.fingerprint/heck-022ddcbe783e6ebf/lib-heck.json
- legacybridge/src-tauri/target/debug/.fingerprint/heck-a6a7539774212eec/lib-heck.json
- legacybridge/src-tauri/target/debug/.fingerprint/heck-a7d8637379091bcc/lib-heck.json
- legacybridge/src-tauri/target/debug/.fingerprint/heck-bee8b7d8b2f07ad8/lib-heck.json
- legacybridge/src-tauri/target/debug/.fingerprint/html5ever-2c6960342c57b5f1/lib-html5ever.json
- legacybridge/src-tauri/target/debug/.fingerprint/html5ever-397491a7da71af77/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/html5ever-6456f3646121d196/lib-html5ever.json
- legacybridge/src-tauri/target/debug/.fingerprint/html5ever-6bb4dca0526d8a26/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/html5ever-85fcbb0d5da27454/lib-html5ever.json
- legacybridge/src-tauri/target/debug/.fingerprint/html5ever-8bf0ff554a07e358/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/html5ever-bb6d54c936eee0b9/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/html5ever-c3cff27aa768e997/lib-html5ever.json
- legacybridge/src-tauri/target/debug/.fingerprint/http-afccbdff98b8722a/lib-http.json
- legacybridge/src-tauri/target/debug/.fingerprint/http-range-101c9cb8fd3c61d6/lib-http_range.json
- legacybridge/src-tauri/target/debug/.fingerprint/iana-time-zone-11a5b6b7b6babd6c/lib-iana_time_zone.json
- legacybridge/src-tauri/target/debug/.fingerprint/ico-b1a58dce64aa4334/lib-ico.json
- legacybridge/src-tauri/target/debug/.fingerprint/icu_collections-3d5d8cd9a34ac639/lib-icu_collections.json
- legacybridge/src-tauri/target/debug/.fingerprint/icu_collections-852646c90ccc16df/lib-icu_collections.json
- legacybridge/src-tauri/target/debug/.fingerprint/icu_collections-9ef44ffa0178d254/lib-icu_collections.json
- legacybridge/src-tauri/target/debug/.fingerprint/icu_locale_core-2ad5e33d7266649f/lib-icu_locale_core.json
- legacybridge/src-tauri/target/debug/.fingerprint/icu_locale_core-46bef119a0c09619/lib-icu_locale_core.json
- legacybridge/src-tauri/target/debug/.fingerprint/icu_locale_core-dff73644e887dbd8/lib-icu_locale_core.json
- legacybridge/src-tauri/target/debug/.fingerprint/icu_normalizer-8ac2327ce7ed12b9/lib-icu_normalizer.json
- legacybridge/src-tauri/target/debug/.fingerprint/icu_normalizer-d2e7e611b92ec8a2/lib-icu_normalizer.json
- legacybridge/src-tauri/target/debug/.fingerprint/icu_normalizer-d5625f492552e068/lib-icu_normalizer.json
- legacybridge/src-tauri/target/debug/.fingerprint/icu_normalizer-f9d5e4d5d7f3ced1/lib-icu_normalizer.json
- legacybridge/src-tauri/target/debug/.fingerprint/icu_normalizer_data-06509863f4ff772f/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/icu_normalizer_data-1108723fc1f3855c/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/icu_normalizer_data-2ef95b62657dd4d2/lib-icu_normalizer_data.json
- legacybridge/src-tauri/target/debug/.fingerprint/icu_normalizer_data-42934aab57e35f18/lib-icu_normalizer_data.json
- legacybridge/src-tauri/target/debug/.fingerprint/icu_normalizer_data-908ab14563240597/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/icu_normalizer_data-f68d778e2827f63c/lib-icu_normalizer_data.json
- legacybridge/src-tauri/target/debug/.fingerprint/icu_properties-2895627a4c832959/lib-icu_properties.json
- legacybridge/src-tauri/target/debug/.fingerprint/icu_properties-3238f750c2dd2d7d/lib-icu_properties.json
- legacybridge/src-tauri/target/debug/.fingerprint/icu_properties-9422add0e3effa68/lib-icu_properties.json
- legacybridge/src-tauri/target/debug/.fingerprint/icu_properties_data-0f885550de43ec9f/lib-icu_properties_data.json
- legacybridge/src-tauri/target/debug/.fingerprint/icu_properties_data-2d36f9144fa2df16/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/icu_properties_data-416641e4b695464a/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/icu_properties_data-629e8c450fbfb726/lib-icu_properties_data.json
- legacybridge/src-tauri/target/debug/.fingerprint/icu_properties_data-74a67e5e1bd0606c/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/icu_properties_data-80c33fbb56fb176b/lib-icu_properties_data.json
- legacybridge/src-tauri/target/debug/.fingerprint/icu_provider-257e0d5c54da3f55/lib-icu_provider.json
- legacybridge/src-tauri/target/debug/.fingerprint/icu_provider-310660fc0e204692/lib-icu_provider.json
- legacybridge/src-tauri/target/debug/.fingerprint/icu_provider-d140099df9d510be/lib-icu_provider.json
- legacybridge/src-tauri/target/debug/.fingerprint/ident_case-810f24c216404eb6/lib-ident_case.json
- legacybridge/src-tauri/target/debug/.fingerprint/idna-06fdd47bcc2c5443/lib-idna.json
- legacybridge/src-tauri/target/debug/.fingerprint/idna-18438c3478028861/lib-idna.json
- legacybridge/src-tauri/target/debug/.fingerprint/idna-c45ec4eab66c3bb1/lib-idna.json
- legacybridge/src-tauri/target/debug/.fingerprint/idna-c5e0fa7a913c24e1/lib-idna.json
- legacybridge/src-tauri/target/debug/.fingerprint/idna_adapter-6492e2941c27a55f/lib-idna_adapter.json
- legacybridge/src-tauri/target/debug/.fingerprint/idna_adapter-bd693b2fb14fc9fb/lib-idna_adapter.json
- legacybridge/src-tauri/target/debug/.fingerprint/idna_adapter-c9b4508c9b24969f/lib-idna_adapter.json
- legacybridge/src-tauri/target/debug/.fingerprint/idna_adapter-d2ea1b4ec0db2b53/lib-idna_adapter.json
- legacybridge/src-tauri/target/debug/.fingerprint/ignore-4d8d8033aa2d6a51/lib-ignore.json
- legacybridge/src-tauri/target/debug/.fingerprint/image-0490fb49142f5d42/lib-image.json
- legacybridge/src-tauri/target/debug/.fingerprint/indexmap-1343b779f29be0d2/lib-indexmap.json
- legacybridge/src-tauri/target/debug/.fingerprint/indexmap-185e61acf5ee28dc/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/indexmap-1dc03b6235ac421d/lib-indexmap.json
- legacybridge/src-tauri/target/debug/.fingerprint/indexmap-32d267d9f742b814/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/indexmap-3dd5f498375b71dc/lib-indexmap.json
- legacybridge/src-tauri/target/debug/.fingerprint/indexmap-3e0b6701933e443b/lib-indexmap.json
- legacybridge/src-tauri/target/debug/.fingerprint/indexmap-889c4f020f4bb4ae/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/indexmap-e996facc31a75bd2/lib-indexmap.json
- legacybridge/src-tauri/target/debug/.fingerprint/infer-1f575d245a29172d/lib-infer.json
- legacybridge/src-tauri/target/debug/.fingerprint/infer-55fb20a49af16867/lib-infer.json
- legacybridge/src-tauri/target/debug/.fingerprint/infer-7c4eb5c6318c4455/lib-infer.json
- legacybridge/src-tauri/target/debug/.fingerprint/instant-3c27b86060ad4dc9/lib-instant.json
- legacybridge/src-tauri/target/debug/.fingerprint/itoa-08c0ac77a7f9c5e1/lib-itoa.json
- legacybridge/src-tauri/target/debug/.fingerprint/itoa-09a02d2f7a267e4c/lib-itoa.json
- legacybridge/src-tauri/target/debug/.fingerprint/itoa-3319deb1e2767d8c/lib-itoa.json
- legacybridge/src-tauri/target/debug/.fingerprint/itoa-a3b339ff7e2fb47f/lib-itoa.json
- legacybridge/src-tauri/target/debug/.fingerprint/javascriptcore-rs-04e2c00a05d5cc9e/lib-javascriptcore.json
- legacybridge/src-tauri/target/debug/.fingerprint/javascriptcore-rs-8f515d3dc8142588/lib-javascriptcore.json
- legacybridge/src-tauri/target/debug/.fingerprint/javascriptcore-rs-sys-0537ee940c0a007d/lib-javascriptcore_rs_sys.json
- legacybridge/src-tauri/target/debug/.fingerprint/javascriptcore-rs-sys-71b03e0e6499c7f0/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/javascriptcore-rs-sys-a1fdce96018fedf1/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/json-patch-18e5351ca73d56b0/lib-json_patch.json
- legacybridge/src-tauri/target/debug/.fingerprint/json-patch-bbba3d0056899f63/lib-json_patch.json
- legacybridge/src-tauri/target/debug/.fingerprint/json-patch-e8fe892187cc0289/lib-json_patch.json
- legacybridge/src-tauri/target/debug/.fingerprint/jsonptr-1c946bcc102b26bd/lib-jsonptr.json
- legacybridge/src-tauri/target/debug/.fingerprint/jsonptr-5a34dfbd1f2d7525/lib-jsonptr.json
- legacybridge/src-tauri/target/debug/.fingerprint/jsonptr-b076a6de1e5a6421/lib-jsonptr.json
- legacybridge/src-tauri/target/debug/.fingerprint/kuchikiki-80d0e5f53f8dbb54/lib-kuchikiki.json
- legacybridge/src-tauri/target/debug/.fingerprint/kuchikiki-8881c31e9738b653/lib-kuchikiki.json
- legacybridge/src-tauri/target/debug/.fingerprint/kuchikiki-d597c1bd4126858b/lib-kuchikiki.json
- legacybridge/src-tauri/target/debug/.fingerprint/kuchikiki-eb6c7208ea5cf3bc/lib-kuchikiki.json
- legacybridge/src-tauri/target/debug/.fingerprint/lazy_static-0b43ed0abed31a7b/lib-lazy_static.json
- legacybridge/src-tauri/target/debug/.fingerprint/legacybridge-73e98ba10c9e697b/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/legacybridge-84017c341ea2704c/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/legacybridge-f3b2e5585b01e975/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/legacybridge-fe4568b69958cf9d/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/libappindicator-9d4d06fa63c4a792/lib-libappindicator.json
- legacybridge/src-tauri/target/debug/.fingerprint/libappindicator-sys-399fdb190fca0895/lib-libappindicator_sys.json
- legacybridge/src-tauri/target/debug/.fingerprint/libc-46cba43152271a15/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/libc-6b3d6fdfe0f41b70/lib-libc.json
- legacybridge/src-tauri/target/debug/.fingerprint/libc-9d68f4abca27c9af/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/libc-b95e77ed68474f44/lib-libc.json
- legacybridge/src-tauri/target/debug/.fingerprint/libloading-6a650b7558b1b4f9/lib-libloading.json
- legacybridge/src-tauri/target/debug/.fingerprint/linux-raw-sys-39d79d0f85925b3c/lib-linux_raw_sys.json
- legacybridge/src-tauri/target/debug/.fingerprint/linux-raw-sys-789ead2d9c1e45d3/lib-linux_raw_sys.json
- legacybridge/src-tauri/target/debug/.fingerprint/litemap-18a5c0d79183effd/lib-litemap.json
- legacybridge/src-tauri/target/debug/.fingerprint/litemap-99f568b3a9a26fa6/lib-litemap.json
- legacybridge/src-tauri/target/debug/.fingerprint/lock_api-0375b16e9183e098/lib-lock_api.json
- legacybridge/src-tauri/target/debug/.fingerprint/lock_api-91537e545019d5b9/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/lock_api-96adb08174198570/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/lock_api-abdffdb57d5f17ae/lib-lock_api.json
- legacybridge/src-tauri/target/debug/.fingerprint/log-9457cdfbb6cf2e49/lib-log.json
- legacybridge/src-tauri/target/debug/.fingerprint/log-f0d82592a119a008/lib-log.json
- legacybridge/src-tauri/target/debug/.fingerprint/mac-7e574ecd81117964/lib-mac.json
- legacybridge/src-tauri/target/debug/.fingerprint/mac-a3996de2bb1d5e9b/lib-mac.json
- legacybridge/src-tauri/target/debug/.fingerprint/markup5ever-2a2d7ef01b2bc166/lib-markup5ever.json
- legacybridge/src-tauri/target/debug/.fingerprint/markup5ever-41d3d79db9061ea7/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/markup5ever-8eb356f8420c41f2/lib-markup5ever.json
- legacybridge/src-tauri/target/debug/.fingerprint/markup5ever-a08d44b5d0e7ad23/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/markup5ever-bb555c5e36016e22/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/markup5ever-df89e981ffa1a7d2/lib-markup5ever.json
- legacybridge/src-tauri/target/debug/.fingerprint/markup5ever-ee88bb371cab775f/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/markup5ever-f376c770e237e793/lib-markup5ever.json
- legacybridge/src-tauri/target/debug/.fingerprint/matches-3bd284ac216e41c5/lib-matches.json
- legacybridge/src-tauri/target/debug/.fingerprint/matches-a221264a8fc5d402/lib-matches.json
- legacybridge/src-tauri/target/debug/.fingerprint/memchr-0325c9e8c520c841/lib-memchr.json
- legacybridge/src-tauri/target/debug/.fingerprint/memchr-3b4e17255c9a44e9/lib-memchr.json
- legacybridge/src-tauri/target/debug/.fingerprint/memoffset-d32ab160bd8e6022/lib-memoffset.json
- legacybridge/src-tauri/target/debug/.fingerprint/memoffset-d9645b3521a71f66/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/memoffset-f4a6c90d4e3f18eb/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/minimal-lexical-7ab26ee8385d3391/lib-minimal_lexical.json
- legacybridge/src-tauri/target/debug/.fingerprint/miniz_oxide-34f0ce6f87a5096b/lib-miniz_oxide.json
- legacybridge/src-tauri/target/debug/.fingerprint/miniz_oxide-fa1fa3118d70d1b8/lib-miniz_oxide.json
- legacybridge/src-tauri/target/debug/.fingerprint/new_debug_unreachable-5c3350bd7cc9be26/lib-debug_unreachable.json
- legacybridge/src-tauri/target/debug/.fingerprint/new_debug_unreachable-c8e9799986ec0197/lib-debug_unreachable.json
- legacybridge/src-tauri/target/debug/.fingerprint/nodrop-2ea557c569710dfc/lib-nodrop.json
- legacybridge/src-tauri/target/debug/.fingerprint/nodrop-aed711551b9f4ff9/lib-nodrop.json
- legacybridge/src-tauri/target/debug/.fingerprint/nom-4e6e7f87c2907fc5/lib-nom.json
- legacybridge/src-tauri/target/debug/.fingerprint/num-traits-4bd4620eb102112a/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/num-traits-6966f275cbe22961/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/num-traits-b08923692296710d/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/num-traits-d70d457423f640f7/lib-num_traits.json
- legacybridge/src-tauri/target/debug/.fingerprint/num-traits-db33cf0e2b124d41/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/num-traits-e82b41a37b4a2fe5/lib-num_traits.json
- legacybridge/src-tauri/target/debug/.fingerprint/num_cpus-b64a6908218f4c45/lib-num_cpus.json
- legacybridge/src-tauri/target/debug/.fingerprint/once_cell-63d5b3f27b4e174c/lib-once_cell.json
- legacybridge/src-tauri/target/debug/.fingerprint/once_cell-ea3f104e8162aad3/lib-once_cell.json
- legacybridge/src-tauri/target/debug/.fingerprint/open-4a4c6299e6318176/lib-open.json
- legacybridge/src-tauri/target/debug/.fingerprint/os_pipe-e8c5c35c242ce730/lib-os_pipe.json
- legacybridge/src-tauri/target/debug/.fingerprint/pango-426b63f401d97969/lib-pango.json
- legacybridge/src-tauri/target/debug/.fingerprint/pango-5542b240cae805f6/lib-pango.json
- legacybridge/src-tauri/target/debug/.fingerprint/pango-sys-43f89b1ff9cfcaae/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/pango-sys-afb7f942e2de8063/lib-pango_sys.json
- legacybridge/src-tauri/target/debug/.fingerprint/pango-sys-c0aa0ff9c7982ef4/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/parking_lot-215c39a719801fa8/lib-parking_lot.json
- legacybridge/src-tauri/target/debug/.fingerprint/parking_lot-30ea085ccdded2c7/lib-parking_lot.json
- legacybridge/src-tauri/target/debug/.fingerprint/parking_lot-af0b2c90f0e4a530/lib-parking_lot.json
- legacybridge/src-tauri/target/debug/.fingerprint/parking_lot-cd0010173e2be4b2/lib-parking_lot.json
- legacybridge/src-tauri/target/debug/.fingerprint/parking_lot_core-0c31aada1f014a1c/lib-parking_lot_core.json
- legacybridge/src-tauri/target/debug/.fingerprint/parking_lot_core-45dfc77ead6300d1/lib-parking_lot_core.json
- legacybridge/src-tauri/target/debug/.fingerprint/parking_lot_core-4cc00a66235023fb/lib-parking_lot_core.json
- legacybridge/src-tauri/target/debug/.fingerprint/parking_lot_core-970a786237060aee/lib-parking_lot_core.json
- legacybridge/src-tauri/target/debug/.fingerprint/parking_lot_core-999b582fa35630d3/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/parking_lot_core-d5ba795b49a036a8/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/pathdiff-f5bc5022ceb77a2f/lib-pathdiff.json
- legacybridge/src-tauri/target/debug/.fingerprint/percent-encoding-28f54ea3166949e7/lib-percent_encoding.json
- legacybridge/src-tauri/target/debug/.fingerprint/percent-encoding-4a9e4925548880d4/lib-percent_encoding.json
- legacybridge/src-tauri/target/debug/.fingerprint/petgraph-cfce3afc64b506bd/lib-petgraph.json
- legacybridge/src-tauri/target/debug/.fingerprint/phf-557e35be9e1d84c7/lib-phf.json
- legacybridge/src-tauri/target/debug/.fingerprint/phf-782212fd6680b692/lib-phf.json
- legacybridge/src-tauri/target/debug/.fingerprint/phf-85b8a87378c6fcbf/lib-phf.json
- legacybridge/src-tauri/target/debug/.fingerprint/phf-a0979f4b4cfe5535/lib-phf.json
- legacybridge/src-tauri/target/debug/.fingerprint/phf-b3b4306c1bcdac54/lib-phf.json
- legacybridge/src-tauri/target/debug/.fingerprint/phf-cbb1aeba7ca32c67/lib-phf.json
- legacybridge/src-tauri/target/debug/.fingerprint/phf-e9cfa0cf44479a5b/lib-phf.json
- legacybridge/src-tauri/target/debug/.fingerprint/phf-f2564e2331517187/lib-phf.json
- legacybridge/src-tauri/target/debug/.fingerprint/phf_codegen-a9cd7449d9056e1b/lib-phf_codegen.json
- legacybridge/src-tauri/target/debug/.fingerprint/phf_codegen-dbfd6ca1e27b5727/lib-phf_codegen.json
- legacybridge/src-tauri/target/debug/.fingerprint/phf_generator-706a8a191cd82077/lib-phf_generator.json
- legacybridge/src-tauri/target/debug/.fingerprint/phf_generator-8fb01cce3ea05038/lib-phf_generator.json
- legacybridge/src-tauri/target/debug/.fingerprint/phf_generator-bd2755f94b2d463e/lib-phf_generator.json
- legacybridge/src-tauri/target/debug/.fingerprint/phf_macros-424f895def5c58c1/lib-phf_macros.json
- legacybridge/src-tauri/target/debug/.fingerprint/phf_macros-7843641ae9cf5246/lib-phf_macros.json
- legacybridge/src-tauri/target/debug/.fingerprint/phf_macros-98d7c92ac2be6498/lib-phf_macros.json
- legacybridge/src-tauri/target/debug/.fingerprint/phf_macros-cfc7d01cc8c1cc0b/lib-phf_macros.json
- legacybridge/src-tauri/target/debug/.fingerprint/phf_shared-04b7d42474c9fa4d/lib-phf_shared.json
- legacybridge/src-tauri/target/debug/.fingerprint/phf_shared-05597d41fc3202e2/lib-phf_shared.json
- legacybridge/src-tauri/target/debug/.fingerprint/phf_shared-37f3dc82dc2f84d8/lib-phf_shared.json
- legacybridge/src-tauri/target/debug/.fingerprint/phf_shared-3a9c079c77a48e41/lib-phf_shared.json
- legacybridge/src-tauri/target/debug/.fingerprint/phf_shared-5c8e456e5076966f/lib-phf_shared.json
- legacybridge/src-tauri/target/debug/.fingerprint/phf_shared-8a35e3de8b7da46b/lib-phf_shared.json
- legacybridge/src-tauri/target/debug/.fingerprint/pin-project-lite-39d1c3577857fa1a/lib-pin_project_lite.json
- legacybridge/src-tauri/target/debug/.fingerprint/pin-utils-41b432c87f555421/lib-pin_utils.json
- legacybridge/src-tauri/target/debug/.fingerprint/pkg-config-318096eedab39bc1/lib-pkg_config.json
- legacybridge/src-tauri/target/debug/.fingerprint/png-279240e2369a899b/lib-png.json
- legacybridge/src-tauri/target/debug/.fingerprint/png-985e70070a142690/lib-png.json
- legacybridge/src-tauri/target/debug/.fingerprint/potential_utf-10f451bfcbcbc677/lib-potential_utf.json
- legacybridge/src-tauri/target/debug/.fingerprint/potential_utf-912e90817180cd94/lib-potential_utf.json
- legacybridge/src-tauri/target/debug/.fingerprint/potential_utf-d88a9487a43a8587/lib-potential_utf.json
- legacybridge/src-tauri/target/debug/.fingerprint/ppv-lite86-0ba84a8c16a66d1f/lib-ppv_lite86.json
- legacybridge/src-tauri/target/debug/.fingerprint/ppv-lite86-9c53b44efc885e9a/lib-ppv_lite86.json
- legacybridge/src-tauri/target/debug/.fingerprint/precomputed-hash-1a50fe4e03dd4de1/lib-precomputed_hash.json
- legacybridge/src-tauri/target/debug/.fingerprint/precomputed-hash-6285410d23fb2201/lib-precomputed_hash.json
- legacybridge/src-tauri/target/debug/.fingerprint/proc-macro-crate-c99a93ab47ef8d17/lib-proc_macro_crate.json
- legacybridge/src-tauri/target/debug/.fingerprint/proc-macro-error-045b50110a06d8fb/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/proc-macro-error-7d573fda02fedde1/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/proc-macro-error-7fbd9199c146acb7/lib-proc_macro_error.json
- legacybridge/src-tauri/target/debug/.fingerprint/proc-macro-error-attr-c125fabd42371746/lib-proc_macro_error_attr.json
- legacybridge/src-tauri/target/debug/.fingerprint/proc-macro-error-attr-ea676c37298f4260/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/proc-macro-error-attr-fcc5e60b7bca9861/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/proc-macro-hack-6f3ecb64266a5810/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/proc-macro-hack-9c6365aaf3d0c4b6/lib-proc_macro_hack.json
- legacybridge/src-tauri/target/debug/.fingerprint/proc-macro-hack-b024f556a64a9047/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/proc-macro2-12bae66f386eed49/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/proc-macro2-6c7c911819aa3c74/lib-proc_macro2.json
- legacybridge/src-tauri/target/debug/.fingerprint/proc-macro2-7d300f594aa36db4/lib-proc_macro2.json
- legacybridge/src-tauri/target/debug/.fingerprint/proc-macro2-88c32e371799c1f7/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/proc-macro2-d63cf8e0318ab4e4/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/proc-macro2-fdcb222da4373f07/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/pulldown-cmark-09a56c69468adfc6/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/pulldown-cmark-229827d60c1e6779/lib-pulldown_cmark.json
- legacybridge/src-tauri/target/debug/.fingerprint/pulldown-cmark-698f540c2f6f45a6/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/pulldown-cmark-7e955867e8316be9/lib-pulldown_cmark.json
- legacybridge/src-tauri/target/debug/.fingerprint/quick-xml-19d6b4b6c77eb721/lib-quick_xml.json
- legacybridge/src-tauri/target/debug/.fingerprint/quote-982a968c3358e50a/lib-quote.json
- legacybridge/src-tauri/target/debug/.fingerprint/quote-a7cb90ce804cf0c0/lib-quote.json
- legacybridge/src-tauri/target/debug/.fingerprint/rand-a977d438add2bd72/lib-rand.json
- legacybridge/src-tauri/target/debug/.fingerprint/rand-ed43e628842f5c57/lib-rand.json
- legacybridge/src-tauri/target/debug/.fingerprint/rand-ef7d32f381487369/lib-rand.json
- legacybridge/src-tauri/target/debug/.fingerprint/rand_chacha-6e37cc665ac14646/lib-rand_chacha.json
- legacybridge/src-tauri/target/debug/.fingerprint/rand_chacha-c8cab7d7ca6d0745/lib-rand_chacha.json
- legacybridge/src-tauri/target/debug/.fingerprint/rand_chacha-dd587978cdcbf24b/lib-rand_chacha.json
- legacybridge/src-tauri/target/debug/.fingerprint/rand_core-a1610036feeba558/lib-rand_core.json
- legacybridge/src-tauri/target/debug/.fingerprint/rand_core-df0e3f5638eeb3a1/lib-rand_core.json
- legacybridge/src-tauri/target/debug/.fingerprint/rand_core-ee8cd4a9d579b65f/lib-rand_core.json
- legacybridge/src-tauri/target/debug/.fingerprint/rand_pcg-f022e90efd419447/lib-rand_pcg.json
- legacybridge/src-tauri/target/debug/.fingerprint/raw-window-handle-046e5733cac2f3f8/lib-raw_window_handle.json
- legacybridge/src-tauri/target/debug/.fingerprint/rayon-4ff8ca5fb762da10/lib-rayon.json
- legacybridge/src-tauri/target/debug/.fingerprint/rayon-core-0d96fe85b924d093/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/rayon-core-63e32c33ac28b7b8/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/rayon-core-d553ee6d6c2c4e0e/lib-rayon_core.json
- legacybridge/src-tauri/target/debug/.fingerprint/regex-27fecffdccb9e593/lib-regex.json
- legacybridge/src-tauri/target/debug/.fingerprint/regex-95b335c4a21cf765/lib-regex.json
- legacybridge/src-tauri/target/debug/.fingerprint/regex-automata-4d1a9295726817b8/lib-regex_automata.json
- legacybridge/src-tauri/target/debug/.fingerprint/regex-automata-db1ff6a0db0546ec/lib-regex_automata.json
- legacybridge/src-tauri/target/debug/.fingerprint/regex-automata-ee1d7d0ca6d4e489/lib-regex_automata.json
- legacybridge/src-tauri/target/debug/.fingerprint/regex-c9535a90ea986019/lib-regex.json
- legacybridge/src-tauri/target/debug/.fingerprint/regex-syntax-7628c20512df11bb/lib-regex_syntax.json
- legacybridge/src-tauri/target/debug/.fingerprint/regex-syntax-c606a884509bc77b/lib-regex_syntax.json
- legacybridge/src-tauri/target/debug/.fingerprint/rfd-2a45c030e0cd2e6a/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/rfd-542c2017fb1a2aa7/lib-rfd.json
- legacybridge/src-tauri/target/debug/.fingerprint/rfd-c0da96b575a0cec3/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/rustc_version-d23e013d1f9c7a62/lib-rustc_version.json
- legacybridge/src-tauri/target/debug/.fingerprint/rustix-05a5f3a2434b7163/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/rustix-79a26db31cf3d350/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/rustix-9b229370c65bc4c7/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/rustix-acd08c3f4f9a9139/lib-rustix.json
- legacybridge/src-tauri/target/debug/.fingerprint/rustix-e2908b7dec96fb6a/lib-rustix.json
- legacybridge/src-tauri/target/debug/.fingerprint/rustix-fec6bff42e61eb60/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/ryu-1d74fbb8396b52fe/lib-ryu.json
- legacybridge/src-tauri/target/debug/.fingerprint/ryu-43e3057b26bcc6dd/lib-ryu.json
- legacybridge/src-tauri/target/debug/.fingerprint/same-file-28d000125fa0aea8/lib-same_file.json
- legacybridge/src-tauri/target/debug/.fingerprint/same-file-d14afa9973feedb9/lib-same_file.json
- legacybridge/src-tauri/target/debug/.fingerprint/scopeguard-25b1508fc49a71ee/lib-scopeguard.json
- legacybridge/src-tauri/target/debug/.fingerprint/scopeguard-5790c81990cfeb1b/lib-scopeguard.json
- legacybridge/src-tauri/target/debug/.fingerprint/selectors-0144884d70b7c88c/lib-selectors.json
- legacybridge/src-tauri/target/debug/.fingerprint/selectors-32595c54a8bcceaf/lib-selectors.json
- legacybridge/src-tauri/target/debug/.fingerprint/selectors-3875c1a106da96ed/lib-selectors.json
- legacybridge/src-tauri/target/debug/.fingerprint/selectors-5b214e196cb51d0b/lib-selectors.json
- legacybridge/src-tauri/target/debug/.fingerprint/selectors-8c318d3d80208862/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/selectors-a0c8ccd0c0e321b4/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/selectors-c395210e02157667/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/semver-24aa839688b4f383/lib-semver.json
- legacybridge/src-tauri/target/debug/.fingerprint/semver-43b6b9e48f33dd86/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/semver-b6aac3c7cceb0190/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/semver-c67fc956ffbece98/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/semver-f105bdaea43e2301/lib-semver.json
- legacybridge/src-tauri/target/debug/.fingerprint/semver-f8d0bc77d8a9dbf1/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/serde-394bff2edf939c0f/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/serde-3fef77406ebf2f96/lib-serde.json
- legacybridge/src-tauri/target/debug/.fingerprint/serde-6690732fba37d144/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/serde-95bb9af3f7b260f8/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/serde-a4e39656d26e57b4/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/serde-c89fbae7b14c091d/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/serde-c99e24849cf3748c/lib-serde.json
- legacybridge/src-tauri/target/debug/.fingerprint/serde-e96d90d04b165566/lib-serde.json
- legacybridge/src-tauri/target/debug/.fingerprint/serde-f4cf87bffee051d5/lib-serde.json
- legacybridge/src-tauri/target/debug/.fingerprint/serde_derive-281f88a588553224/lib-serde_derive.json
- legacybridge/src-tauri/target/debug/.fingerprint/serde_derive-392a73cc017fe0a7/lib-serde_derive.json
- legacybridge/src-tauri/target/debug/.fingerprint/serde_json-2154163ccde9513b/lib-serde_json.json
- legacybridge/src-tauri/target/debug/.fingerprint/serde_json-27c1303ac890f4ff/lib-serde_json.json
- legacybridge/src-tauri/target/debug/.fingerprint/serde_json-29f41cacaf7fe98d/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/serde_json-2d92a556342f16ef/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/serde_json-4d2e51ba4d42886f/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/serde_json-5e3be07eb5e12d7d/lib-serde_json.json
- legacybridge/src-tauri/target/debug/.fingerprint/serde_json-834fa8642aba7ab0/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/serde_json-a735741d7d7ddd74/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/serde_json-c3c0acf42029b402/lib-serde_json.json
- legacybridge/src-tauri/target/debug/.fingerprint/serde_json-f56be8f41abff05d/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/serde_repr-dc43083ab15736f5/lib-serde_repr.json
- legacybridge/src-tauri/target/debug/.fingerprint/serde_spanned-bd64a156e0656d15/lib-serde_spanned.json
- legacybridge/src-tauri/target/debug/.fingerprint/serde_spanned-d132382893a2b4af/lib-serde_spanned.json
- legacybridge/src-tauri/target/debug/.fingerprint/serde_with-83a2a6a3691e272f/lib-serde_with.json
- legacybridge/src-tauri/target/debug/.fingerprint/serde_with-9dc027f0cd04ae9b/lib-serde_with.json
- legacybridge/src-tauri/target/debug/.fingerprint/serde_with-a63c79eeb6da9c84/lib-serde_with.json
- legacybridge/src-tauri/target/debug/.fingerprint/serde_with_macros-77030300b3a3cfda/lib-serde_with_macros.json
- legacybridge/src-tauri/target/debug/.fingerprint/serde_with_macros-8c6bfe63a8ff87e3/lib-serde_with_macros.json
- legacybridge/src-tauri/target/debug/.fingerprint/serialize-to-javascript-438bb97297560e03/lib-serialize_to_javascript.json
- legacybridge/src-tauri/target/debug/.fingerprint/serialize-to-javascript-impl-22f7d25bcce3ce98/lib-serialize_to_javascript_impl.json
- legacybridge/src-tauri/target/debug/.fingerprint/servo_arc-4b63f3cd6688cad2/lib-servo_arc.json
- legacybridge/src-tauri/target/debug/.fingerprint/servo_arc-5fd47fb1fe5ee3b2/lib-servo_arc.json
- legacybridge/src-tauri/target/debug/.fingerprint/sha2-03052023fb561358/lib-sha2.json
- legacybridge/src-tauri/target/debug/.fingerprint/shlex-83c7e0da01d13271/lib-shlex.json
- legacybridge/src-tauri/target/debug/.fingerprint/simd-adler32-869c65d5c51bf93d/lib-simd_adler32.json
- legacybridge/src-tauri/target/debug/.fingerprint/simd-adler32-ce98ab6204197f68/lib-simd_adler32.json
- legacybridge/src-tauri/target/debug/.fingerprint/siphasher-5b4b36d10fee9677/lib-siphasher.json
- legacybridge/src-tauri/target/debug/.fingerprint/siphasher-93671a6e331badf8/lib-siphasher.json
- legacybridge/src-tauri/target/debug/.fingerprint/siphasher-9687c34e6cce449f/lib-siphasher.json
- legacybridge/src-tauri/target/debug/.fingerprint/siphasher-c79b637dfcfe2c39/lib-siphasher.json
- legacybridge/src-tauri/target/debug/.fingerprint/slab-a3361e62d1c2857b/lib-slab.json
- legacybridge/src-tauri/target/debug/.fingerprint/smallvec-4a26b102a8527c64/lib-smallvec.json
- legacybridge/src-tauri/target/debug/.fingerprint/smallvec-8a9450a91f868832/lib-smallvec.json
- legacybridge/src-tauri/target/debug/.fingerprint/smallvec-9c208101372f6873/lib-smallvec.json
- legacybridge/src-tauri/target/debug/.fingerprint/smallvec-f208072056c429a0/lib-smallvec.json
- legacybridge/src-tauri/target/debug/.fingerprint/soup2-336f44ecc7a89312/lib-soup.json
- legacybridge/src-tauri/target/debug/.fingerprint/soup2-c29945bdd27a3d93/lib-soup.json
- legacybridge/src-tauri/target/debug/.fingerprint/soup2-sys-1aa37dfd64a28155/lib-soup_sys.json
- legacybridge/src-tauri/target/debug/.fingerprint/soup2-sys-41e3429c24b4a160/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/soup2-sys-46d10370304639cb/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/stable_deref_trait-3717159d49df65e5/lib-stable_deref_trait.json
- legacybridge/src-tauri/target/debug/.fingerprint/stable_deref_trait-4e2768f260013686/lib-stable_deref_trait.json
- legacybridge/src-tauri/target/debug/.fingerprint/state-bff724df82a157df/lib-state.json
- legacybridge/src-tauri/target/debug/.fingerprint/string_cache-003cfbe84d119a8d/lib-string_cache.json
- legacybridge/src-tauri/target/debug/.fingerprint/string_cache-44e29131a1cb7340/lib-string_cache.json
- legacybridge/src-tauri/target/debug/.fingerprint/string_cache-9c036f419cfdc1cb/lib-string_cache.json
- legacybridge/src-tauri/target/debug/.fingerprint/string_cache-f918233a2e90a2d1/lib-string_cache.json
- legacybridge/src-tauri/target/debug/.fingerprint/string_cache_codegen-14b86c010a3ec6bd/lib-string_cache_codegen.json
- legacybridge/src-tauri/target/debug/.fingerprint/string_cache_codegen-5facf8369149c7f2/lib-string_cache_codegen.json
- legacybridge/src-tauri/target/debug/.fingerprint/strsim-ad112818830f0ac6/lib-strsim.json
- legacybridge/src-tauri/target/debug/.fingerprint/syn-1233b9ee4d953c70/lib-syn.json
- legacybridge/src-tauri/target/debug/.fingerprint/syn-384b6654963be71b/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/syn-4409d60084ea512e/lib-syn.json
- legacybridge/src-tauri/target/debug/.fingerprint/syn-5ad6df5f04a8f46f/lib-syn.json
- legacybridge/src-tauri/target/debug/.fingerprint/syn-762d8b95d2e6ad09/lib-syn.json
- legacybridge/src-tauri/target/debug/.fingerprint/syn-d96af843025de00a/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/synstructure-1668ddc22700b3a0/lib-synstructure.json
- legacybridge/src-tauri/target/debug/.fingerprint/synstructure-2158bcfbc69ed1f3/lib-synstructure.json
- legacybridge/src-tauri/target/debug/.fingerprint/system-deps-94e2b0ab04cb20fb/lib-system_deps.json
- legacybridge/src-tauri/target/debug/.fingerprint/system-deps-c8cd8817a6327f4d/lib-system_deps.json
- legacybridge/src-tauri/target/debug/.fingerprint/tao-4e65b66ed37deb6c/lib-tao.json
- legacybridge/src-tauri/target/debug/.fingerprint/tao-6d1fb0caca8e0039/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/tao-727d8647765f1e17/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/tao-b8d827cdef94e42f/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/tao-d713ccbffa2fd477/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/tao-f54fc9fcafea3152/lib-tao.json
- legacybridge/src-tauri/target/debug/.fingerprint/tar-3c35b4d9e317258b/lib-tar.json
- legacybridge/src-tauri/target/debug/.fingerprint/target-lexicon-368e7e9394533810/lib-target_lexicon.json
- legacybridge/src-tauri/target/debug/.fingerprint/target-lexicon-9871ee972bb88a70/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/target-lexicon-b34e3cb066da1e41/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/tauri-1777ffe8ce33ef6a/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/tauri-32e9f5f33774a095/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/tauri-33a84912da55141a/lib-tauri.json
- legacybridge/src-tauri/target/debug/.fingerprint/tauri-6943cdc1f04c4c2c/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/tauri-b23c3b826e31b8ea/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/tauri-build-34d5c43a116849b4/lib-tauri_build.json
- legacybridge/src-tauri/target/debug/.fingerprint/tauri-build-7bdda479549a2aa4/lib-tauri_build.json
- legacybridge/src-tauri/target/debug/.fingerprint/tauri-codegen-7260a3d4843f1944/lib-tauri_codegen.json
- legacybridge/src-tauri/target/debug/.fingerprint/tauri-codegen-f87155710e538233/lib-tauri_codegen.json
- legacybridge/src-tauri/target/debug/.fingerprint/tauri-e93d3e5c393801be/lib-tauri.json
- legacybridge/src-tauri/target/debug/.fingerprint/tauri-macros-b757892cc66b6d91/lib-tauri_macros.json
- legacybridge/src-tauri/target/debug/.fingerprint/tauri-macros-eae02565a8dc94a7/lib-tauri_macros.json
- legacybridge/src-tauri/target/debug/.fingerprint/tauri-runtime-127a224666fe82e4/lib-tauri_runtime.json
- legacybridge/src-tauri/target/debug/.fingerprint/tauri-runtime-504002fd7d13a8b9/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/tauri-runtime-66e529e659f0d906/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/tauri-runtime-d0f8dc584e348c86/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/tauri-runtime-dfa30c3175974610/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/tauri-runtime-e4eb080d6972ccad/lib-tauri_runtime.json
- legacybridge/src-tauri/target/debug/.fingerprint/tauri-runtime-wry-286fd72d5d54766f/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/tauri-runtime-wry-3b62762b32008ccd/lib-tauri_runtime_wry.json
- legacybridge/src-tauri/target/debug/.fingerprint/tauri-runtime-wry-8dc617a955faa6ae/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/tauri-runtime-wry-a477f906092cacf6/lib-tauri_runtime_wry.json
- legacybridge/src-tauri/target/debug/.fingerprint/tauri-runtime-wry-b55ed302205f0c3b/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/tauri-runtime-wry-fa371952ca2e16d2/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/tauri-utils-40a8aa9d9c482aca/lib-tauri_utils.json
- legacybridge/src-tauri/target/debug/.fingerprint/tauri-utils-d7ffa07d4c76685b/lib-tauri_utils.json
- legacybridge/src-tauri/target/debug/.fingerprint/tauri-utils-db8ebe99edb55ca0/lib-tauri_utils.json
- legacybridge/src-tauri/target/debug/.fingerprint/tauri-utils-f84d6c0781d4416f/lib-tauri_utils.json
- legacybridge/src-tauri/target/debug/.fingerprint/tauri-winres-a34ab1b5bb96b3c7/lib-tauri_winres.json
- legacybridge/src-tauri/target/debug/.fingerprint/tauri-winres-b28f53dd3f61a6bc/lib-tauri_winres.json
- legacybridge/src-tauri/target/debug/.fingerprint/tempfile-a710880ba830f7cc/lib-tempfile.json
- legacybridge/src-tauri/target/debug/.fingerprint/tendril-cb4473ef262c370e/lib-tendril.json
- legacybridge/src-tauri/target/debug/.fingerprint/tendril-cb7ed33bafb67207/lib-tendril.json
- legacybridge/src-tauri/target/debug/.fingerprint/thin-slice-0b0329056f8d6cf0/lib-thin_slice.json
- legacybridge/src-tauri/target/debug/.fingerprint/thin-slice-f613f3809e40789f/lib-thin_slice.json
- legacybridge/src-tauri/target/debug/.fingerprint/thiserror-04cf86ab6b5dbce9/lib-thiserror.json
- legacybridge/src-tauri/target/debug/.fingerprint/thiserror-0ab41f997ece6c55/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/thiserror-1fe595b50b76ce20/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/thiserror-266dde517fbb4842/lib-thiserror.json
- legacybridge/src-tauri/target/debug/.fingerprint/thiserror-289b827818904376/lib-thiserror.json
- legacybridge/src-tauri/target/debug/.fingerprint/thiserror-5c7e835cbcf3ecb5/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/thiserror-92e70e684d292252/lib-thiserror.json
- legacybridge/src-tauri/target/debug/.fingerprint/thiserror-b8cd02a6a03190c6/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/thiserror-bdbfac39577c5cd4/lib-thiserror.json
- legacybridge/src-tauri/target/debug/.fingerprint/thiserror-impl-1664de7404b21c0a/lib-thiserror_impl.json
- legacybridge/src-tauri/target/debug/.fingerprint/thiserror-impl-27211b4cd95daa00/lib-thiserror_impl.json
- legacybridge/src-tauri/target/debug/.fingerprint/thiserror-impl-fc678168f34aa711/lib-thiserror_impl.json
- legacybridge/src-tauri/target/debug/.fingerprint/tinystr-2ba9200e65d18b03/lib-tinystr.json
- legacybridge/src-tauri/target/debug/.fingerprint/tinystr-7b2c5abfeda1fff7/lib-tinystr.json
- legacybridge/src-tauri/target/debug/.fingerprint/tinystr-ef00727dbb52e76f/lib-tinystr.json
- legacybridge/src-tauri/target/debug/.fingerprint/tokio-40fa631170786a68/lib-tokio.json
- legacybridge/src-tauri/target/debug/.fingerprint/toml-13881c10f631aa32/lib-toml.json
- legacybridge/src-tauri/target/debug/.fingerprint/toml-22471f32276f934a/lib-toml.json
- legacybridge/src-tauri/target/debug/.fingerprint/toml-a84b5c7877881904/lib-toml.json
- legacybridge/src-tauri/target/debug/.fingerprint/toml-cc992c1c97b7f052/lib-toml.json
- legacybridge/src-tauri/target/debug/.fingerprint/toml-d1be389684c83748/lib-toml.json
- legacybridge/src-tauri/target/debug/.fingerprint/toml_datetime-b5890dd8435f76f1/lib-toml_datetime.json
- legacybridge/src-tauri/target/debug/.fingerprint/toml_datetime-d5b242ee6e3a93e3/lib-toml_datetime.json
- legacybridge/src-tauri/target/debug/.fingerprint/toml_edit-575364a8340a8990/lib-toml_edit.json
- legacybridge/src-tauri/target/debug/.fingerprint/toml_edit-cb36e74beb56eb12/lib-toml_edit.json
- legacybridge/src-tauri/target/debug/.fingerprint/toml_edit-d39b485af756cc37/lib-toml_edit.json
- legacybridge/src-tauri/target/debug/.fingerprint/toml_edit-e63d148cb13f6765/lib-toml_edit.json
- legacybridge/src-tauri/target/debug/.fingerprint/toml_write-1a1891766be87659/lib-toml_write.json
- legacybridge/src-tauri/target/debug/.fingerprint/tree_magic_mini-0248fc753b73ae78/lib-tree_magic_mini.json
- legacybridge/src-tauri/target/debug/.fingerprint/typenum-1f282336f2788d1c/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/typenum-4d9d080de023a2a4/lib-typenum.json
- legacybridge/src-tauri/target/debug/.fingerprint/typenum-911c3e43dcb6b56a/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/unicase-3e5176e8129d926b/lib-unicase.json
- legacybridge/src-tauri/target/debug/.fingerprint/unicode-ident-27b708a2ea44f53b/lib-unicode_ident.json
- legacybridge/src-tauri/target/debug/.fingerprint/unicode-segmentation-012cbb8a3d8a0967/lib-unicode_segmentation.json
- legacybridge/src-tauri/target/debug/.fingerprint/unicode-width-6f55947009843c19/lib-unicode_width.json
- legacybridge/src-tauri/target/debug/.fingerprint/url-287541da38bfcf8c/lib-url.json
- legacybridge/src-tauri/target/debug/.fingerprint/url-58050325867a0bd3/lib-url.json
- legacybridge/src-tauri/target/debug/.fingerprint/url-632f596c4ecba1a3/lib-url.json
- legacybridge/src-tauri/target/debug/.fingerprint/url-6b298b356df5d5e5/lib-url.json
- legacybridge/src-tauri/target/debug/.fingerprint/utf-8-45e4cfda574298fe/lib-utf8.json
- legacybridge/src-tauri/target/debug/.fingerprint/utf-8-8ea22e4a3188d679/lib-utf8.json
- legacybridge/src-tauri/target/debug/.fingerprint/utf8_iter-7c80e090d74f7ee1/lib-utf8_iter.json
- legacybridge/src-tauri/target/debug/.fingerprint/utf8_iter-9e5e896a9851b53d/lib-utf8_iter.json
- legacybridge/src-tauri/target/debug/.fingerprint/uuid-809285d5e29b3095/lib-uuid.json
- legacybridge/src-tauri/target/debug/.fingerprint/uuid-888a5c9eadd380ac/lib-uuid.json
- legacybridge/src-tauri/target/debug/.fingerprint/uuid-c9a2ce954872bd50/lib-uuid.json
- legacybridge/src-tauri/target/debug/.fingerprint/version-compare-25d67372a93d2eb8/lib-version_compare.json
- legacybridge/src-tauri/target/debug/.fingerprint/version-compare-2ec694ebe4912d49/lib-version_compare.json
- legacybridge/src-tauri/target/debug/.fingerprint/version_check-a216429c4cbdde93/lib-version_check.json
- legacybridge/src-tauri/target/debug/.fingerprint/walkdir-7293ebc2d93a27a7/lib-walkdir.json
- legacybridge/src-tauri/target/debug/.fingerprint/walkdir-ffb13534afc8db8b/lib-walkdir.json
- legacybridge/src-tauri/target/debug/.fingerprint/wayland-backend-9ef3fb108227bcff/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/wayland-backend-ae5c8d64c1a3fe4d/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/wayland-backend-c327b52dee9924e5/lib-wayland_backend.json
- legacybridge/src-tauri/target/debug/.fingerprint/wayland-client-05d0eb12d301d742/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/wayland-client-2e81408e4254c0ae/lib-wayland_client.json
- legacybridge/src-tauri/target/debug/.fingerprint/wayland-client-d1f84827c830d3d1/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/wayland-protocols-e37e2dae757a872c/lib-wayland_protocols.json
- legacybridge/src-tauri/target/debug/.fingerprint/wayland-protocols-wlr-5a951eedeca0f3a4/lib-wayland_protocols_wlr.json
- legacybridge/src-tauri/target/debug/.fingerprint/wayland-scanner-f2954c72db4ef0f4/lib-wayland_scanner.json
- legacybridge/src-tauri/target/debug/.fingerprint/wayland-sys-11a813dbc5c7fdb6/lib-wayland_sys.json
- legacybridge/src-tauri/target/debug/.fingerprint/wayland-sys-7af962876794c246/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/wayland-sys-b7062937efdab2c0/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/webkit2gtk-7c522570f96801fd/lib-webkit2gtk.json
- legacybridge/src-tauri/target/debug/.fingerprint/webkit2gtk-e20e3f1a1d2fe00a/lib-webkit2gtk.json
- legacybridge/src-tauri/target/debug/.fingerprint/webkit2gtk-sys-32e328ce824c440c/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/webkit2gtk-sys-758f3b700d47f2fe/lib-webkit2gtk_sys.json
- legacybridge/src-tauri/target/debug/.fingerprint/webkit2gtk-sys-8a6d4c0c4144260e/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/winnow-c3069a2d8d8dfc76/lib-winnow.json
- legacybridge/src-tauri/target/debug/.fingerprint/winnow-d9fd3bd1bb11ad5b/lib-winnow.json
- legacybridge/src-tauri/target/debug/.fingerprint/wl-clipboard-rs-6f3c215f03c66714/lib-wl_clipboard_rs.json
- legacybridge/src-tauri/target/debug/.fingerprint/writeable-927aa235dd70e018/lib-writeable.json
- legacybridge/src-tauri/target/debug/.fingerprint/writeable-e8805dc05e359f4d/lib-writeable.json
- legacybridge/src-tauri/target/debug/.fingerprint/wry-2f65f9c38e581cc0/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/wry-3f887aee2ed728e8/lib-wry.json
- legacybridge/src-tauri/target/debug/.fingerprint/wry-57c9a056781d47d8/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/wry-7a892162b9f9219c/lib-wry.json
- legacybridge/src-tauri/target/debug/.fingerprint/wry-a2ec94b195dbbfc3/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/wry-b749d6d7cfa96352/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/x11-378b8389689223d2/lib-x11.json
- legacybridge/src-tauri/target/debug/.fingerprint/x11-962d9ecac511850d/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/x11-ae5c226dbfb8b393/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/x11-dl-1ae1c979ee0aa1e0/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/x11-dl-2ef534180b3c5e31/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/x11-dl-804e219dd3d38085/lib-x11_dl.json
- legacybridge/src-tauri/target/debug/.fingerprint/x11rb-8144c698ce8a865a/lib-x11rb.json
- legacybridge/src-tauri/target/debug/.fingerprint/x11rb-protocol-a363fb236dfc2723/lib-x11rb_protocol.json
- legacybridge/src-tauri/target/debug/.fingerprint/xattr-003ef50764fb681b/lib-xattr.json
- legacybridge/src-tauri/target/debug/.fingerprint/yoke-0b2c8275a11c05b0/lib-yoke.json
- legacybridge/src-tauri/target/debug/.fingerprint/yoke-3f39bb52069919f6/lib-yoke.json
- legacybridge/src-tauri/target/debug/.fingerprint/yoke-ceacf2f93206944d/lib-yoke.json
- legacybridge/src-tauri/target/debug/.fingerprint/yoke-derive-5b8c7c9941f40c51/lib-yoke_derive.json
- legacybridge/src-tauri/target/debug/.fingerprint/yoke-derive-b45abec104a6265f/lib-yoke_derive.json
- legacybridge/src-tauri/target/debug/.fingerprint/zerocopy-a171ac0550851143/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/zerocopy-ae6198574733b8e5/lib-zerocopy.json
- legacybridge/src-tauri/target/debug/.fingerprint/zerocopy-ce37cc0dffdc199b/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/zerocopy-ea79c9b68300a1a8/lib-zerocopy.json
- legacybridge/src-tauri/target/debug/.fingerprint/zerofrom-2461729f00074af3/lib-zerofrom.json
- legacybridge/src-tauri/target/debug/.fingerprint/zerofrom-3d62c126cbf5db1f/lib-zerofrom.json
- legacybridge/src-tauri/target/debug/.fingerprint/zerofrom-4b6480e30d4419ff/lib-zerofrom.json
- legacybridge/src-tauri/target/debug/.fingerprint/zerofrom-derive-1f86de79071eab8c/lib-zerofrom_derive.json
- legacybridge/src-tauri/target/debug/.fingerprint/zerofrom-derive-e84b45e34e3903cb/lib-zerofrom_derive.json
- legacybridge/src-tauri/target/debug/.fingerprint/zerotrie-2259762100781dc5/lib-zerotrie.json
- legacybridge/src-tauri/target/debug/.fingerprint/zerotrie-381fe9f7e71eeec3/lib-zerotrie.json
- legacybridge/src-tauri/target/debug/.fingerprint/zerotrie-ec07e89ecb7c08cc/lib-zerotrie.json
- legacybridge/src-tauri/target/debug/.fingerprint/zerovec-0661778d855e18da/lib-zerovec.json
- legacybridge/src-tauri/target/debug/.fingerprint/zerovec-1365f8a3c8991cdd/lib-zerovec.json
- legacybridge/src-tauri/target/debug/.fingerprint/zerovec-derive-18f340d5fccb9a98/lib-zerovec_derive.json
- legacybridge/src-tauri/target/debug/.fingerprint/zerovec-derive-c6e46e8abab7a50d/lib-zerovec_derive.json
- legacybridge/src-tauri/target/debug/.fingerprint/zerovec-e99df776b125e26b/lib-zerovec.json
- legacybridge/src-tauri/tauri.conf.json
- legacybridge/test_32bit_compatibility.c
- legacybridge/test_dll.c
- legacybridge/test_dll.py
- legacybridge/tests/accessibility/accessibility-tests.spec.ts
- legacybridge/tests/accessibility/component-accessibility.test.tsx
- legacybridge/tests/chaos/chaos-engineering-tests.spec.ts
- legacybridge/tests/framework/test-automation-framework.ts
- legacybridge/tests/framework/types.ts
- legacybridge/tests/integration/file-conversion-flow.test.tsx
- legacybridge/tests/load/k6-load-test.js
- legacybridge/tests/mocks/fileMock.js
- legacybridge/tests/mocks/styleMock.js
- legacybridge/tests/performance/performance-regression.test.ts
- legacybridge/tests/run-all-tests.js
- legacybridge/tests/security/security-test-suite.ts
- legacybridge/tests/setup/global-setup.ts
- legacybridge/tests/setup/global-teardown.ts
- legacybridge/tests/setup/jest.setup.js
- legacybridge/tests/unit/components/ConversionProgress.test.tsx
- legacybridge/tests/unit/components/DragDropZone.test.tsx
- legacybridge/tests/unit/components/ErrorBoundary.test.tsx
- legacybridge/tests/unit/components/MarkdownPreview.test.tsx
- legacybridge/tests/unit/components/MonitoringDashboard.test.tsx
- legacybridge/tests/unit/stores/files.test.ts
- legacybridge/tests/visual-regression/visual-regression-tests.spec.ts
- legacybridge/tsconfig.json
- tests/load/k6-load-test.js
- validate_performance_claims.py


## Code Changes (2025-07-26 20:39:06)

### Added Files
- .github/workflows/ci.yml
- COMPREHENSIVE_SECURITY_ANALYSIS.json
- docker-compose.yml
- docs/api/interactive-explorer/index.html
- docs/troubleshooting/interactive-wizard/index.html
- helm/legacybridge/Chart.yaml
- helm/legacybridge/values-production.yaml
- helm/legacybridge/values.yaml
- k8s/configmap.yaml
- k8s/deployment-optimized.yaml
- k8s/deployment.yaml
- k8s/ingress.yaml
- k8s/monitoring-stack.yaml
- k8s/monitoring.yaml
- legacybridge/.eslintrc.json
- legacybridge/ENTERPRISE_PACKAGE/MANIFEST.json
- legacybridge/ENTERPRISE_PACKAGE/examples/other/test_dll.c
- legacybridge/ENTERPRISE_PACKAGE/examples/other/test_dll.py
- legacybridge/ENTERPRISE_PACKAGE/include/legacybridge.h
- legacybridge/components.json
- legacybridge/coverage/base.css
- legacybridge/coverage/block-navigation.js
- legacybridge/coverage/coverage-final.json
- legacybridge/coverage/index.html
- legacybridge/coverage/lcov-report/base.css
- legacybridge/coverage/lcov-report/block-navigation.js
- legacybridge/coverage/lcov-report/index.html
- legacybridge/coverage/lcov-report/prettify.css
- legacybridge/coverage/lcov-report/prettify.js
- legacybridge/coverage/lcov-report/sorter.js
- legacybridge/coverage/lcov-report/src/app/api/monitoring/alerts/index.html
- legacybridge/coverage/lcov-report/src/app/api/monitoring/alerts/route.ts.html
- legacybridge/coverage/lcov-report/src/app/api/monitoring/alerts/slack/index.html
- legacybridge/coverage/lcov-report/src/app/api/monitoring/alerts/slack/route.ts.html
- legacybridge/coverage/lcov-report/src/app/api/monitoring/metrics/index.html
- legacybridge/coverage/lcov-report/src/app/api/monitoring/metrics/route.ts.html
- legacybridge/coverage/lcov-report/src/app/api/monitoring/prometheus/index.html
- legacybridge/coverage/lcov-report/src/app/api/monitoring/prometheus/route.ts.html
- legacybridge/coverage/lcov-report/src/app/demo/index.html
- legacybridge/coverage/lcov-report/src/app/demo/page.tsx.html
- legacybridge/coverage/lcov-report/src/app/home-page.tsx.html
- legacybridge/coverage/lcov-report/src/app/index.html
- legacybridge/coverage/lcov-report/src/app/layout.tsx.html
- legacybridge/coverage/lcov-report/src/app/monitoring/index.html
- legacybridge/coverage/lcov-report/src/app/monitoring/page.tsx.html
- legacybridge/coverage/lcov-report/src/app/page.tsx.html
- legacybridge/coverage/lcov-report/src/components/ConversionProgress.tsx.html
- legacybridge/coverage/lcov-report/src/components/DiffView.tsx.html
- legacybridge/coverage/lcov-report/src/components/DownloadManager.tsx.html
- legacybridge/coverage/lcov-report/src/components/DragDropZone.tsx.html
- legacybridge/coverage/lcov-report/src/components/ErrorBoundary.tsx.html
- legacybridge/coverage/lcov-report/src/components/ErrorDisplay.tsx.html
- legacybridge/coverage/lcov-report/src/components/ErrorLogViewer.tsx.html
- legacybridge/coverage/lcov-report/src/components/LoadingAnimation.tsx.html
- legacybridge/coverage/lcov-report/src/components/MarkdownPreview.tsx.html
- legacybridge/coverage/lcov-report/src/components/PreviewPanel.tsx.html
- legacybridge/coverage/lcov-report/src/components/SuccessAnimation.tsx.html
- legacybridge/coverage/lcov-report/src/components/SyntaxHighlighter.tsx.html
- legacybridge/coverage/lcov-report/src/components/ThemeProvider.tsx.html
- legacybridge/coverage/lcov-report/src/components/UnifiedErrorDisplay.tsx.html
- legacybridge/coverage/lcov-report/src/components/index.html
- legacybridge/coverage/lcov-report/src/components/layout/Footer.tsx.html
- legacybridge/coverage/lcov-report/src/components/layout/Header.tsx.html
- legacybridge/coverage/lcov-report/src/components/layout/MainLayout.tsx.html
- legacybridge/coverage/lcov-report/src/components/layout/index.html
- legacybridge/coverage/lcov-report/src/components/monitoring/BuildProgressRing.tsx.html
- legacybridge/coverage/lcov-report/src/components/monitoring/EnhancedFunctionCallMatrix.tsx.html
- legacybridge/coverage/lcov-report/src/components/monitoring/FunctionCallMatrix.tsx.html
- legacybridge/coverage/lcov-report/src/components/monitoring/LogStreamViewer.tsx.html
- legacybridge/coverage/lcov-report/src/components/monitoring/MonitoringDashboard.tsx.html
- legacybridge/coverage/lcov-report/src/components/monitoring/PerformanceChart.tsx.html
- legacybridge/coverage/lcov-report/src/components/monitoring/SystemHealthCard.tsx.html
- legacybridge/coverage/lcov-report/src/components/monitoring/index.html
- legacybridge/coverage/lcov-report/src/components/ui/alert-dialog.tsx.html
- legacybridge/coverage/lcov-report/src/components/ui/badge.tsx.html
- legacybridge/coverage/lcov-report/src/components/ui/button.tsx.html
- legacybridge/coverage/lcov-report/src/components/ui/card.tsx.html
- legacybridge/coverage/lcov-report/src/components/ui/dropdown-menu.tsx.html
- legacybridge/coverage/lcov-report/src/components/ui/index.html
- legacybridge/coverage/lcov-report/src/components/ui/input.tsx.html
- legacybridge/coverage/lcov-report/src/components/ui/label.tsx.html
- legacybridge/coverage/lcov-report/src/components/ui/progress.tsx.html
- legacybridge/coverage/lcov-report/src/components/ui/separator.tsx.html
- legacybridge/coverage/lcov-report/src/components/ui/switch.tsx.html
- legacybridge/coverage/lcov-report/src/components/ui/tabs.tsx.html
- legacybridge/coverage/lcov-report/src/components/ui/tooltip.tsx.html
- legacybridge/coverage/lcov-report/src/enterprise/admin/AdminDashboard.tsx.html
- legacybridge/coverage/lcov-report/src/enterprise/admin/index.html
- legacybridge/coverage/lcov-report/src/enterprise/api/enterprise-api.ts.html
- legacybridge/coverage/lcov-report/src/enterprise/api/index.html
- legacybridge/coverage/lcov-report/src/enterprise/audit/audit-logger.ts.html
- legacybridge/coverage/lcov-report/src/enterprise/audit/index.html
- legacybridge/coverage/lcov-report/src/enterprise/auth/authentication.ts.html
- legacybridge/coverage/lcov-report/src/enterprise/auth/index.html
- legacybridge/coverage/lcov-report/src/enterprise/auth/rbac.ts.html
- legacybridge/coverage/lcov-report/src/enterprise/services/index.html
- legacybridge/coverage/lcov-report/src/enterprise/services/tenant-conversion-service.ts.html
- legacybridge/coverage/lcov-report/src/enterprise/tenancy/index.html
- legacybridge/coverage/lcov-report/src/enterprise/tenancy/tenant-context.ts.html
- legacybridge/coverage/lcov-report/src/hooks/index.html
- legacybridge/coverage/lcov-report/src/hooks/useDebounce.ts.html
- legacybridge/coverage/lcov-report/src/hooks/useMonitoringWebSocket.ts.html
- legacybridge/coverage/lcov-report/src/index.html
- legacybridge/coverage/lcov-report/src/lib/download-service.ts.html
- legacybridge/coverage/lcov-report/src/lib/error-logger.ts.html
- legacybridge/coverage/lcov-report/src/lib/index.html
- legacybridge/coverage/lcov-report/src/lib/monitoring/alert-manager.ts.html
- legacybridge/coverage/lcov-report/src/lib/monitoring/index.html
- legacybridge/coverage/lcov-report/src/lib/monitoring/websocket-server.ts.html
- legacybridge/coverage/lcov-report/src/lib/sanitizer.ts.html
- legacybridge/coverage/lcov-report/src/lib/stores/files.ts.html
- legacybridge/coverage/lcov-report/src/lib/stores/index.html
- legacybridge/coverage/lcov-report/src/lib/tauri-api.ts.html
- legacybridge/coverage/lcov-report/src/lib/unified-error-api.ts.html
- legacybridge/coverage/lcov-report/src/lib/utils.ts.html
- legacybridge/coverage/lcov-report/src/middleware.ts.html
- legacybridge/coverage/prettify.css
- legacybridge/coverage/prettify.js
- legacybridge/coverage/sorter.js
- legacybridge/coverage/src/app/api/monitoring/alerts/index.html
- legacybridge/coverage/src/app/api/monitoring/alerts/route.ts.html
- legacybridge/coverage/src/app/api/monitoring/alerts/slack/index.html
- legacybridge/coverage/src/app/api/monitoring/alerts/slack/route.ts.html
- legacybridge/coverage/src/app/api/monitoring/metrics/index.html
- legacybridge/coverage/src/app/api/monitoring/metrics/route.ts.html
- legacybridge/coverage/src/app/api/monitoring/prometheus/index.html
- legacybridge/coverage/src/app/api/monitoring/prometheus/route.ts.html
- legacybridge/coverage/src/app/demo/index.html
- legacybridge/coverage/src/app/demo/page.tsx.html
- legacybridge/coverage/src/app/home-page.tsx.html
- legacybridge/coverage/src/app/index.html
- legacybridge/coverage/src/app/layout.tsx.html
- legacybridge/coverage/src/app/monitoring/index.html
- legacybridge/coverage/src/app/monitoring/page.tsx.html
- legacybridge/coverage/src/app/page.tsx.html
- legacybridge/coverage/src/components/ConversionProgress.tsx.html
- legacybridge/coverage/src/components/DiffView.tsx.html
- legacybridge/coverage/src/components/DownloadManager.tsx.html
- legacybridge/coverage/src/components/DragDropZone.tsx.html
- legacybridge/coverage/src/components/ErrorBoundary.tsx.html
- legacybridge/coverage/src/components/ErrorDisplay.tsx.html
- legacybridge/coverage/src/components/ErrorLogViewer.tsx.html
- legacybridge/coverage/src/components/LoadingAnimation.tsx.html
- legacybridge/coverage/src/components/MarkdownPreview.tsx.html
- legacybridge/coverage/src/components/PreviewPanel.tsx.html
- legacybridge/coverage/src/components/SuccessAnimation.tsx.html
- legacybridge/coverage/src/components/SyntaxHighlighter.tsx.html
- legacybridge/coverage/src/components/ThemeProvider.tsx.html
- legacybridge/coverage/src/components/UnifiedErrorDisplay.tsx.html
- legacybridge/coverage/src/components/index.html
- legacybridge/coverage/src/components/layout/Footer.tsx.html
- legacybridge/coverage/src/components/layout/Header.tsx.html
- legacybridge/coverage/src/components/layout/MainLayout.tsx.html
- legacybridge/coverage/src/components/layout/index.html
- legacybridge/coverage/src/components/monitoring/BuildProgressRing.tsx.html
- legacybridge/coverage/src/components/monitoring/EnhancedFunctionCallMatrix.tsx.html
- legacybridge/coverage/src/components/monitoring/FunctionCallMatrix.tsx.html
- legacybridge/coverage/src/components/monitoring/LogStreamViewer.tsx.html
- legacybridge/coverage/src/components/monitoring/MonitoringDashboard.tsx.html
- legacybridge/coverage/src/components/monitoring/PerformanceChart.tsx.html
- legacybridge/coverage/src/components/monitoring/SystemHealthCard.tsx.html
- legacybridge/coverage/src/components/monitoring/index.html
- legacybridge/coverage/src/components/ui/alert-dialog.tsx.html
- legacybridge/coverage/src/components/ui/badge.tsx.html
- legacybridge/coverage/src/components/ui/button.tsx.html
- legacybridge/coverage/src/components/ui/card.tsx.html
- legacybridge/coverage/src/components/ui/dropdown-menu.tsx.html
- legacybridge/coverage/src/components/ui/index.html
- legacybridge/coverage/src/components/ui/input.tsx.html
- legacybridge/coverage/src/components/ui/label.tsx.html
- legacybridge/coverage/src/components/ui/progress.tsx.html
- legacybridge/coverage/src/components/ui/separator.tsx.html
- legacybridge/coverage/src/components/ui/switch.tsx.html
- legacybridge/coverage/src/components/ui/tabs.tsx.html
- legacybridge/coverage/src/components/ui/tooltip.tsx.html
- legacybridge/coverage/src/enterprise/admin/AdminDashboard.tsx.html
- legacybridge/coverage/src/enterprise/admin/index.html
- legacybridge/coverage/src/enterprise/api/enterprise-api.ts.html
- legacybridge/coverage/src/enterprise/api/index.html
- legacybridge/coverage/src/enterprise/audit/audit-logger.ts.html
- legacybridge/coverage/src/enterprise/audit/index.html
- legacybridge/coverage/src/enterprise/auth/authentication.ts.html
- legacybridge/coverage/src/enterprise/auth/index.html
- legacybridge/coverage/src/enterprise/auth/rbac.ts.html
- legacybridge/coverage/src/enterprise/services/index.html
- legacybridge/coverage/src/enterprise/services/tenant-conversion-service.ts.html
- legacybridge/coverage/src/enterprise/tenancy/index.html
- legacybridge/coverage/src/enterprise/tenancy/tenant-context.ts.html
- legacybridge/coverage/src/hooks/index.html
- legacybridge/coverage/src/hooks/useDebounce.ts.html
- legacybridge/coverage/src/hooks/useMonitoringWebSocket.ts.html
- legacybridge/coverage/src/index.html
- legacybridge/coverage/src/lib/download-service.ts.html
- legacybridge/coverage/src/lib/error-logger.ts.html
- legacybridge/coverage/src/lib/index.html
- legacybridge/coverage/src/lib/monitoring/alert-manager.ts.html
- legacybridge/coverage/src/lib/monitoring/index.html
- legacybridge/coverage/src/lib/monitoring/websocket-server.ts.html
- legacybridge/coverage/src/lib/sanitizer.ts.html
- legacybridge/coverage/src/lib/stores/files.ts.html
- legacybridge/coverage/src/lib/stores/index.html
- legacybridge/coverage/src/lib/tauri-api.ts.html
- legacybridge/coverage/src/lib/unified-error-api.ts.html
- legacybridge/coverage/src/lib/utils.ts.html
- legacybridge/coverage/src/middleware.ts.html
- legacybridge/deployment_package/include/legacybridge.h
- legacybridge/frontend-integration-example.ts
- legacybridge/include/legacybridge.h
- legacybridge/jest.config.js
- legacybridge/next.config.ts
- legacybridge/package-lock.json
- legacybridge/package.json
- legacybridge/perf_test.c
- legacybridge/playwright.config.ts
- legacybridge/src/__tests__/security/xss.test.tsx
- legacybridge/src/app/api/monitoring/alerts/route.ts
- legacybridge/src/app/api/monitoring/alerts/slack/route.ts
- legacybridge/src/app/api/monitoring/metrics/route.ts
- legacybridge/src/app/api/monitoring/prometheus/route.ts
- legacybridge/src/app/demo/page.tsx
- legacybridge/src/app/globals-backup.css
- legacybridge/src/app/globals.css
- legacybridge/src/app/home-page.tsx
- legacybridge/src/app/layout.tsx
- legacybridge/src/app/monitoring/page.tsx
- legacybridge/src/app/page.tsx
- legacybridge/src/components/ConversionProgress.tsx
- legacybridge/src/components/DiffView.tsx
- legacybridge/src/components/DownloadManager.tsx
- legacybridge/src/components/DragDropZone.tsx
- legacybridge/src/components/ErrorBoundary.tsx
- legacybridge/src/components/ErrorDisplay.tsx
- legacybridge/src/components/ErrorLogViewer.tsx
- legacybridge/src/components/LoadingAnimation.tsx
- legacybridge/src/components/MarkdownPreview.tsx
- legacybridge/src/components/PreviewPanel.tsx
- legacybridge/src/components/SuccessAnimation.tsx
- legacybridge/src/components/SyntaxHighlighter.tsx
- legacybridge/src/components/ThemeProvider.tsx
- legacybridge/src/components/UnifiedErrorDisplay.tsx
- legacybridge/src/components/layout/Footer.tsx
- legacybridge/src/components/layout/Header.tsx
- legacybridge/src/components/layout/MainLayout.tsx
- legacybridge/src/components/monitoring/BuildProgressRing.tsx
- legacybridge/src/components/monitoring/EnhancedFunctionCallMatrix.tsx
- legacybridge/src/components/monitoring/FunctionCallMatrix.tsx
- legacybridge/src/components/monitoring/LogStreamViewer.tsx
- legacybridge/src/components/monitoring/MonitoringDashboard.tsx
- legacybridge/src/components/monitoring/PerformanceChart.tsx
- legacybridge/src/components/monitoring/SystemHealthCard.tsx
- legacybridge/src/components/monitoring/index.ts
- legacybridge/src/components/ui/alert-dialog.tsx
- legacybridge/src/components/ui/badge.tsx
- legacybridge/src/components/ui/button.tsx
- legacybridge/src/components/ui/card.tsx
- legacybridge/src/components/ui/dropdown-menu.tsx
- legacybridge/src/components/ui/input.tsx
- legacybridge/src/components/ui/label.tsx
- legacybridge/src/components/ui/progress.tsx
- legacybridge/src/components/ui/separator.tsx
- legacybridge/src/components/ui/switch.tsx
- legacybridge/src/components/ui/tabs.tsx
- legacybridge/src/components/ui/tooltip.tsx
- legacybridge/src/enterprise/admin/AdminDashboard.tsx
- legacybridge/src/enterprise/api/enterprise-api.ts
- legacybridge/src/enterprise/audit/audit-logger.ts
- legacybridge/src/enterprise/auth/authentication.ts
- legacybridge/src/enterprise/auth/rbac.ts
- legacybridge/src/enterprise/config/kubernetes-deployment.yaml
- legacybridge/src/enterprise/services/tenant-conversion-service.ts
- legacybridge/src/enterprise/tenancy/tenant-context.ts
- legacybridge/src/hooks/useDebounce.ts
- legacybridge/src/hooks/useMonitoringWebSocket.ts
- legacybridge/src/lib/download-service.ts
- legacybridge/src/lib/error-logger.ts
- legacybridge/src/lib/monitoring/alert-manager.ts
- legacybridge/src/lib/monitoring/websocket-server.ts
- legacybridge/src/lib/sanitizer.ts
- legacybridge/src/lib/stores/files.ts
- legacybridge/src/lib/tauri-api.ts
- legacybridge/src/lib/unified-error-api.ts
- legacybridge/src/lib/utils.ts
- legacybridge/src/middleware.ts
- legacybridge/src/styles/monitoring.css
- legacybridge/src/types/errors.ts
- legacybridge/src/types/index.ts
- legacybridge/src/types/monitoring.ts
- legacybridge/src-tauri/security.config.json
- legacybridge/src-tauri/target/.rustc_info.json
- legacybridge/src-tauri/target/debug/.fingerprint/autocfg-9280f9c2f723f569/lib-autocfg.json
- legacybridge/src-tauri/target/debug/.fingerprint/cfg-expr-67dc737b391e60b1/lib-cfg_expr.json
- legacybridge/src-tauri/target/debug/.fingerprint/cfg-if-37c365409298422e/lib-cfg_if.json
- legacybridge/src-tauri/target/debug/.fingerprint/displaydoc-f09e92e998eb8f82/lib-displaydoc.json
- legacybridge/src-tauri/target/debug/.fingerprint/equivalent-9ff5cbb44f5b198f/lib-equivalent.json
- legacybridge/src-tauri/target/debug/.fingerprint/glib-sys-6f0a0032b775b3c5/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/hashbrown-d0f10f6dd8e8a795/lib-hashbrown.json
- legacybridge/src-tauri/target/debug/.fingerprint/heck-a7d8637379091bcc/lib-heck.json
- legacybridge/src-tauri/target/debug/.fingerprint/indexmap-1dc03b6235ac421d/lib-indexmap.json
- legacybridge/src-tauri/target/debug/.fingerprint/libc-46cba43152271a15/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/libc-6b3d6fdfe0f41b70/lib-libc.json
- legacybridge/src-tauri/target/debug/.fingerprint/libc-9d68f4abca27c9af/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/libc-b95e77ed68474f44/lib-libc.json
- legacybridge/src-tauri/target/debug/.fingerprint/pkg-config-318096eedab39bc1/lib-pkg_config.json
- legacybridge/src-tauri/target/debug/.fingerprint/ppv-lite86-9c53b44efc885e9a/lib-ppv_lite86.json
- legacybridge/src-tauri/target/debug/.fingerprint/proc-macro2-12bae66f386eed49/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/proc-macro2-6c7c911819aa3c74/lib-proc_macro2.json
- legacybridge/src-tauri/target/debug/.fingerprint/proc-macro2-88c32e371799c1f7/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/quote-982a968c3358e50a/lib-quote.json
- legacybridge/src-tauri/target/debug/.fingerprint/serde-394bff2edf939c0f/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/serde-a4e39656d26e57b4/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/serde-c99e24849cf3748c/lib-serde.json
- legacybridge/src-tauri/target/debug/.fingerprint/serde_derive-281f88a588553224/lib-serde_derive.json
- legacybridge/src-tauri/target/debug/.fingerprint/serde_spanned-d132382893a2b4af/lib-serde_spanned.json
- legacybridge/src-tauri/target/debug/.fingerprint/siphasher-93671a6e331badf8/lib-siphasher.json
- legacybridge/src-tauri/target/debug/.fingerprint/smallvec-4a26b102a8527c64/lib-smallvec.json
- legacybridge/src-tauri/target/debug/.fingerprint/syn-384b6654963be71b/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/syn-4409d60084ea512e/lib-syn.json
- legacybridge/src-tauri/target/debug/.fingerprint/syn-5ad6df5f04a8f46f/lib-syn.json
- legacybridge/src-tauri/target/debug/.fingerprint/syn-d96af843025de00a/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/synstructure-2158bcfbc69ed1f3/lib-synstructure.json
- legacybridge/src-tauri/target/debug/.fingerprint/system-deps-c8cd8817a6327f4d/lib-system_deps.json
- legacybridge/src-tauri/target/debug/.fingerprint/target-lexicon-368e7e9394533810/lib-target_lexicon.json
- legacybridge/src-tauri/target/debug/.fingerprint/target-lexicon-9871ee972bb88a70/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/target-lexicon-b34e3cb066da1e41/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/toml-22471f32276f934a/lib-toml.json
- legacybridge/src-tauri/target/debug/.fingerprint/toml_datetime-b5890dd8435f76f1/lib-toml_datetime.json
- legacybridge/src-tauri/target/debug/.fingerprint/toml_edit-d39b485af756cc37/lib-toml_edit.json
- legacybridge/src-tauri/target/debug/.fingerprint/toml_write-1a1891766be87659/lib-toml_write.json
- legacybridge/src-tauri/target/debug/.fingerprint/unicode-ident-27b708a2ea44f53b/lib-unicode_ident.json
- legacybridge/src-tauri/target/debug/.fingerprint/version-compare-2ec694ebe4912d49/lib-version_compare.json
- legacybridge/src-tauri/target/debug/.fingerprint/version_check-a216429c4cbdde93/lib-version_check.json
- legacybridge/src-tauri/target/debug/.fingerprint/winnow-d9fd3bd1bb11ad5b/lib-winnow.json
- legacybridge/src-tauri/target/debug/.fingerprint/yoke-derive-5b8c7c9941f40c51/lib-yoke_derive.json
- legacybridge/src-tauri/target/debug/.fingerprint/zerocopy-a171ac0550851143/run-build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/zerocopy-ce37cc0dffdc199b/build-script-build-script-build.json
- legacybridge/src-tauri/target/debug/.fingerprint/zerocopy-ea79c9b68300a1a8/lib-zerocopy.json
- legacybridge/src-tauri/target/debug/.fingerprint/zerofrom-derive-1f86de79071eab8c/lib-zerofrom_derive.json
- legacybridge/src-tauri/target/debug/.fingerprint/zerovec-derive-c6e46e8abab7a50d/lib-zerovec_derive.json
- legacybridge/src-tauri/tauri.conf.json
- legacybridge/test_32bit_compatibility.c
- legacybridge/test_dll.c
- legacybridge/test_dll.py
- legacybridge/tests/accessibility/accessibility-tests.spec.ts
- legacybridge/tests/accessibility/component-accessibility.test.tsx
- legacybridge/tests/chaos/chaos-engineering-tests.spec.ts
- legacybridge/tests/framework/test-automation-framework.ts
- legacybridge/tests/framework/types.ts
- legacybridge/tests/integration/file-conversion-flow.test.tsx
- legacybridge/tests/load/k6-load-test.js
- legacybridge/tests/mocks/fileMock.js
- legacybridge/tests/mocks/styleMock.js
- legacybridge/tests/performance/performance-regression.test.ts
- legacybridge/tests/run-all-tests.js
- legacybridge/tests/security/security-test-suite.ts
- legacybridge/tests/setup/global-setup.ts
- legacybridge/tests/setup/global-teardown.ts
- legacybridge/tests/setup/jest.setup.js
- legacybridge/tests/unit/components/ConversionProgress.test.tsx
- legacybridge/tests/unit/components/DragDropZone.test.tsx
- legacybridge/tests/unit/components/ErrorBoundary.test.tsx
- legacybridge/tests/unit/components/MarkdownPreview.test.tsx
- legacybridge/tests/unit/components/MonitoringDashboard.test.tsx
- legacybridge/tests/unit/stores/files.test.ts
- legacybridge/tests/visual-regression/visual-regression-tests.spec.ts
- legacybridge/tsconfig.json
- tests/load/k6-load-test.js
- validate_performance_claims.py


## Code Changes (2025-07-26 20:28:17)

### Added Files
- .github/workflows/ci.yml
- COMPREHENSIVE_SECURITY_ANALYSIS.json
- docker-compose.yml
- docs/api/interactive-explorer/index.html
- docs/troubleshooting/interactive-wizard/index.html
- helm/legacybridge/Chart.yaml
- helm/legacybridge/values-production.yaml
- helm/legacybridge/values.yaml
- k8s/configmap.yaml
- k8s/deployment-optimized.yaml
- k8s/deployment.yaml
- k8s/ingress.yaml
- k8s/monitoring-stack.yaml
- k8s/monitoring.yaml
- legacybridge/.eslintrc.json
- legacybridge/ENTERPRISE_PACKAGE/MANIFEST.json
- legacybridge/ENTERPRISE_PACKAGE/examples/other/test_dll.c
- legacybridge/ENTERPRISE_PACKAGE/examples/other/test_dll.py
- legacybridge/ENTERPRISE_PACKAGE/include/legacybridge.h
- legacybridge/components.json
- legacybridge/deployment_package/include/legacybridge.h
- legacybridge/frontend-integration-example.ts
- legacybridge/include/legacybridge.h
- legacybridge/jest.config.js
- legacybridge/next.config.ts
- legacybridge/package-lock.json
- legacybridge/package.json
- legacybridge/perf_test.c
- legacybridge/playwright.config.ts
- legacybridge/src/__tests__/security/xss.test.tsx
- legacybridge/src/app/api/monitoring/alerts/route.ts
- legacybridge/src/app/api/monitoring/alerts/slack/route.ts
- legacybridge/src/app/api/monitoring/metrics/route.ts
- legacybridge/src/app/api/monitoring/prometheus/route.ts
- legacybridge/src/app/demo/page.tsx
- legacybridge/src/app/globals-backup.css
- legacybridge/src/app/globals.css
- legacybridge/src/app/home-page.tsx
- legacybridge/src/app/layout.tsx
- legacybridge/src/app/monitoring/page.tsx
- legacybridge/src/app/page.tsx
- legacybridge/src/components/ConversionProgress.tsx
- legacybridge/src/components/DiffView.tsx
- legacybridge/src/components/DownloadManager.tsx
- legacybridge/src/components/DragDropZone.tsx
- legacybridge/src/components/ErrorBoundary.tsx
- legacybridge/src/components/ErrorDisplay.tsx
- legacybridge/src/components/ErrorLogViewer.tsx
- legacybridge/src/components/LoadingAnimation.tsx
- legacybridge/src/components/MarkdownPreview.tsx
- legacybridge/src/components/PreviewPanel.tsx
- legacybridge/src/components/SuccessAnimation.tsx
- legacybridge/src/components/SyntaxHighlighter.tsx
- legacybridge/src/components/ThemeProvider.tsx
- legacybridge/src/components/UnifiedErrorDisplay.tsx
- legacybridge/src/components/layout/Footer.tsx
- legacybridge/src/components/layout/Header.tsx
- legacybridge/src/components/layout/MainLayout.tsx
- legacybridge/src/components/monitoring/BuildProgressRing.tsx
- legacybridge/src/components/monitoring/EnhancedFunctionCallMatrix.tsx
- legacybridge/src/components/monitoring/FunctionCallMatrix.tsx
- legacybridge/src/components/monitoring/LogStreamViewer.tsx
- legacybridge/src/components/monitoring/MonitoringDashboard.tsx
- legacybridge/src/components/monitoring/PerformanceChart.tsx
- legacybridge/src/components/monitoring/SystemHealthCard.tsx
- legacybridge/src/components/monitoring/index.ts
- legacybridge/src/components/ui/alert-dialog.tsx
- legacybridge/src/components/ui/badge.tsx
- legacybridge/src/components/ui/button.tsx
- legacybridge/src/components/ui/card.tsx
- legacybridge/src/components/ui/dropdown-menu.tsx
- legacybridge/src/components/ui/input.tsx
- legacybridge/src/components/ui/label.tsx
- legacybridge/src/components/ui/progress.tsx
- legacybridge/src/components/ui/separator.tsx
- legacybridge/src/components/ui/switch.tsx
- legacybridge/src/components/ui/tabs.tsx
- legacybridge/src/components/ui/tooltip.tsx
- legacybridge/src/enterprise/admin/AdminDashboard.tsx
- legacybridge/src/enterprise/api/enterprise-api.ts
- legacybridge/src/enterprise/audit/audit-logger.ts
- legacybridge/src/enterprise/auth/authentication.ts
- legacybridge/src/enterprise/auth/rbac.ts
- legacybridge/src/enterprise/config/kubernetes-deployment.yaml
- legacybridge/src/enterprise/services/tenant-conversion-service.ts
- legacybridge/src/enterprise/tenancy/tenant-context.ts
- legacybridge/src/hooks/useDebounce.ts
- legacybridge/src/hooks/useMonitoringWebSocket.ts
- legacybridge/src/lib/download-service.ts
- legacybridge/src/lib/error-logger.ts
- legacybridge/src/lib/monitoring/alert-manager.ts
- legacybridge/src/lib/monitoring/websocket-server.ts
- legacybridge/src/lib/sanitizer.ts
- legacybridge/src/lib/stores/files.ts
- legacybridge/src/lib/tauri-api.ts
- legacybridge/src/lib/unified-error-api.ts
- legacybridge/src/lib/utils.ts
- legacybridge/src/middleware.ts
- legacybridge/src/styles/monitoring.css
- legacybridge/src/types/errors.ts
- legacybridge/src/types/index.ts
- legacybridge/src/types/monitoring.ts
- legacybridge/src-tauri/security.config.json
- legacybridge/src-tauri/tauri.conf.json
- legacybridge/test_32bit_compatibility.c
- legacybridge/test_dll.c
- legacybridge/test_dll.py
- legacybridge/tests/accessibility/accessibility-tests.spec.ts
- legacybridge/tests/accessibility/component-accessibility.test.tsx
- legacybridge/tests/chaos/chaos-engineering-tests.spec.ts
- legacybridge/tests/framework/test-automation-framework.ts
- legacybridge/tests/framework/types.ts
- legacybridge/tests/integration/file-conversion-flow.test.tsx
- legacybridge/tests/load/k6-load-test.js
- legacybridge/tests/mocks/fileMock.js
- legacybridge/tests/mocks/styleMock.js
- legacybridge/tests/performance/performance-regression.test.ts
- legacybridge/tests/run-all-tests.js
- legacybridge/tests/security/security-test-suite.ts
- legacybridge/tests/setup/global-setup.ts
- legacybridge/tests/setup/global-teardown.ts
- legacybridge/tests/setup/jest.setup.js
- legacybridge/tests/unit/components/ConversionProgress.test.tsx
- legacybridge/tests/unit/components/DragDropZone.test.tsx
- legacybridge/tests/unit/components/ErrorBoundary.test.tsx
- legacybridge/tests/unit/components/MarkdownPreview.test.tsx
- legacybridge/tests/unit/components/MonitoringDashboard.test.tsx
- legacybridge/tests/unit/stores/files.test.ts
- legacybridge/tests/visual-regression/visual-regression-tests.spec.ts
- legacybridge/tsconfig.json
- tests/load/k6-load-test.js
- validate_performance_claims.py


## Code Changes (2025-07-26 19:00:07)

### Added Files
- .github/workflows/ci.yml
- COMPREHENSIVE_SECURITY_ANALYSIS.json
- docker-compose.yml
- docs/api/interactive-explorer/index.html
- docs/troubleshooting/interactive-wizard/index.html
- helm/legacybridge/Chart.yaml
- helm/legacybridge/values-production.yaml
- helm/legacybridge/values.yaml
- k8s/configmap.yaml
- k8s/deployment-optimized.yaml
- k8s/deployment.yaml
- k8s/ingress.yaml
- k8s/monitoring-stack.yaml
- k8s/monitoring.yaml
- legacybridge/.eslintrc.json
- legacybridge/ENTERPRISE_PACKAGE/MANIFEST.json
- legacybridge/ENTERPRISE_PACKAGE/examples/other/test_dll.c
- legacybridge/ENTERPRISE_PACKAGE/examples/other/test_dll.py
- legacybridge/ENTERPRISE_PACKAGE/include/legacybridge.h
- legacybridge/components.json
- legacybridge/deployment_package/include/legacybridge.h
- legacybridge/frontend-integration-example.ts
- legacybridge/include/legacybridge.h
- legacybridge/jest.config.js
- legacybridge/next.config.ts
- legacybridge/package-lock.json
- legacybridge/package.json
- legacybridge/perf_test.c
- legacybridge/playwright.config.ts
- legacybridge/src/__tests__/security/xss.test.tsx
- legacybridge/src/app/api/monitoring/alerts/route.ts
- legacybridge/src/app/api/monitoring/alerts/slack/route.ts
- legacybridge/src/app/api/monitoring/metrics/route.ts
- legacybridge/src/app/api/monitoring/prometheus/route.ts
- legacybridge/src/app/demo/page.tsx
- legacybridge/src/app/globals-backup.css
- legacybridge/src/app/globals.css
- legacybridge/src/app/home-page.tsx
- legacybridge/src/app/layout.tsx
- legacybridge/src/app/monitoring/page.tsx
- legacybridge/src/app/page.tsx
- legacybridge/src/components/ConversionProgress.tsx
- legacybridge/src/components/DiffView.tsx
- legacybridge/src/components/DownloadManager.tsx
- legacybridge/src/components/DragDropZone.tsx
- legacybridge/src/components/ErrorBoundary.tsx
- legacybridge/src/components/ErrorDisplay.tsx
- legacybridge/src/components/ErrorLogViewer.tsx
- legacybridge/src/components/LoadingAnimation.tsx
- legacybridge/src/components/MarkdownPreview.tsx
- legacybridge/src/components/PreviewPanel.tsx
- legacybridge/src/components/SuccessAnimation.tsx
- legacybridge/src/components/SyntaxHighlighter.tsx
- legacybridge/src/components/ThemeProvider.tsx
- legacybridge/src/components/UnifiedErrorDisplay.tsx
- legacybridge/src/components/layout/Footer.tsx
- legacybridge/src/components/layout/Header.tsx
- legacybridge/src/components/layout/MainLayout.tsx
- legacybridge/src/components/monitoring/BuildProgressRing.tsx
- legacybridge/src/components/monitoring/EnhancedFunctionCallMatrix.tsx
- legacybridge/src/components/monitoring/FunctionCallMatrix.tsx
- legacybridge/src/components/monitoring/LogStreamViewer.tsx
- legacybridge/src/components/monitoring/MonitoringDashboard.tsx
- legacybridge/src/components/monitoring/PerformanceChart.tsx
- legacybridge/src/components/monitoring/SystemHealthCard.tsx
- legacybridge/src/components/monitoring/index.ts
- legacybridge/src/components/ui/alert-dialog.tsx
- legacybridge/src/components/ui/badge.tsx
- legacybridge/src/components/ui/button.tsx
- legacybridge/src/components/ui/card.tsx
- legacybridge/src/components/ui/dropdown-menu.tsx
- legacybridge/src/components/ui/input.tsx
- legacybridge/src/components/ui/label.tsx
- legacybridge/src/components/ui/progress.tsx
- legacybridge/src/components/ui/separator.tsx
- legacybridge/src/components/ui/switch.tsx
- legacybridge/src/components/ui/tabs.tsx
- legacybridge/src/components/ui/tooltip.tsx
- legacybridge/src/enterprise/admin/AdminDashboard.tsx
- legacybridge/src/enterprise/api/enterprise-api.ts
- legacybridge/src/enterprise/audit/audit-logger.ts
- legacybridge/src/enterprise/auth/authentication.ts
- legacybridge/src/enterprise/auth/rbac.ts
- legacybridge/src/enterprise/config/kubernetes-deployment.yaml
- legacybridge/src/enterprise/services/tenant-conversion-service.ts
- legacybridge/src/enterprise/tenancy/tenant-context.ts
- legacybridge/src/hooks/useDebounce.ts
- legacybridge/src/hooks/useMonitoringWebSocket.ts
- legacybridge/src/lib/download-service.ts
- legacybridge/src/lib/error-logger.ts
- legacybridge/src/lib/monitoring/alert-manager.ts
- legacybridge/src/lib/monitoring/websocket-server.ts
- legacybridge/src/lib/sanitizer.ts
- legacybridge/src/lib/stores/files.ts
- legacybridge/src/lib/tauri-api.ts
- legacybridge/src/lib/unified-error-api.ts
- legacybridge/src/lib/utils.ts
- legacybridge/src/middleware.ts
- legacybridge/src/styles/monitoring.css
- legacybridge/src/types/errors.ts
- legacybridge/src/types/index.ts
- legacybridge/src/types/monitoring.ts
- legacybridge/src-tauri/security.config.json
- legacybridge/src-tauri/tauri.conf.json
- legacybridge/test_32bit_compatibility.c
- legacybridge/test_dll.c
- legacybridge/test_dll.py
- legacybridge/tests/accessibility/accessibility-tests.spec.ts
- legacybridge/tests/accessibility/component-accessibility.test.tsx
- legacybridge/tests/chaos/chaos-engineering-tests.spec.ts
- legacybridge/tests/framework/test-automation-framework.ts
- legacybridge/tests/framework/types.ts
- legacybridge/tests/integration/file-conversion-flow.test.tsx
- legacybridge/tests/load/k6-load-test.js
- legacybridge/tests/mocks/fileMock.js
- legacybridge/tests/mocks/styleMock.js
- legacybridge/tests/performance/performance-regression.test.ts
- legacybridge/tests/run-all-tests.js
- legacybridge/tests/security/security-test-suite.ts
- legacybridge/tests/setup/global-setup.ts
- legacybridge/tests/setup/global-teardown.ts
- legacybridge/tests/setup/jest.setup.js
- legacybridge/tests/unit/components/ConversionProgress.test.tsx
- legacybridge/tests/unit/components/DragDropZone.test.tsx
- legacybridge/tests/unit/components/ErrorBoundary.test.tsx
- legacybridge/tests/unit/components/MarkdownPreview.test.tsx
- legacybridge/tests/unit/components/MonitoringDashboard.test.tsx
- legacybridge/tests/unit/stores/files.test.ts
- legacybridge/tests/visual-regression/visual-regression-tests.spec.ts
- legacybridge/tsconfig.json
- tests/load/k6-load-test.js
- validate_performance_claims.py


## Code Changes (2025-07-26 18:58:30)

### Added Files
- .github/workflows/ci.yml
- COMPREHENSIVE_SECURITY_ANALYSIS.json
- docker-compose.yml
- docs/api/interactive-explorer/index.html
- docs/troubleshooting/interactive-wizard/index.html
- helm/legacybridge/Chart.yaml
- helm/legacybridge/values-production.yaml
- helm/legacybridge/values.yaml
- k8s/configmap.yaml
- k8s/deployment-optimized.yaml
- k8s/deployment.yaml
- k8s/ingress.yaml
- k8s/monitoring-stack.yaml
- k8s/monitoring.yaml
- legacybridge/.eslintrc.json
- legacybridge/ENTERPRISE_PACKAGE/MANIFEST.json
- legacybridge/ENTERPRISE_PACKAGE/examples/other/test_dll.c
- legacybridge/ENTERPRISE_PACKAGE/examples/other/test_dll.py
- legacybridge/ENTERPRISE_PACKAGE/include/legacybridge.h
- legacybridge/components.json
- legacybridge/deployment_package/include/legacybridge.h
- legacybridge/frontend-integration-example.ts
- legacybridge/include/legacybridge.h
- legacybridge/jest.config.js
- legacybridge/next.config.ts
- legacybridge/package-lock.json
- legacybridge/package.json
- legacybridge/perf_test.c
- legacybridge/playwright.config.ts
- legacybridge/src/__tests__/security/xss.test.tsx
- legacybridge/src/app/api/monitoring/alerts/route.ts
- legacybridge/src/app/api/monitoring/alerts/slack/route.ts
- legacybridge/src/app/api/monitoring/metrics/route.ts
- legacybridge/src/app/api/monitoring/prometheus/route.ts
- legacybridge/src/app/demo/page.tsx
- legacybridge/src/app/globals-backup.css
- legacybridge/src/app/globals.css
- legacybridge/src/app/home-page.tsx
- legacybridge/src/app/layout.tsx
- legacybridge/src/app/monitoring/page.tsx
- legacybridge/src/app/page.tsx
- legacybridge/src/components/ConversionProgress.tsx
- legacybridge/src/components/DiffView.tsx
- legacybridge/src/components/DownloadManager.tsx
- legacybridge/src/components/DragDropZone.tsx
- legacybridge/src/components/ErrorBoundary.tsx
- legacybridge/src/components/ErrorDisplay.tsx
- legacybridge/src/components/ErrorLogViewer.tsx
- legacybridge/src/components/LoadingAnimation.tsx
- legacybridge/src/components/MarkdownPreview.tsx
- legacybridge/src/components/PreviewPanel.tsx
- legacybridge/src/components/SuccessAnimation.tsx
- legacybridge/src/components/SyntaxHighlighter.tsx
- legacybridge/src/components/ThemeProvider.tsx
- legacybridge/src/components/UnifiedErrorDisplay.tsx
- legacybridge/src/components/layout/Footer.tsx
- legacybridge/src/components/layout/Header.tsx
- legacybridge/src/components/layout/MainLayout.tsx
- legacybridge/src/components/monitoring/BuildProgressRing.tsx
- legacybridge/src/components/monitoring/EnhancedFunctionCallMatrix.tsx
- legacybridge/src/components/monitoring/FunctionCallMatrix.tsx
- legacybridge/src/components/monitoring/LogStreamViewer.tsx
- legacybridge/src/components/monitoring/MonitoringDashboard.tsx
- legacybridge/src/components/monitoring/PerformanceChart.tsx
- legacybridge/src/components/monitoring/SystemHealthCard.tsx
- legacybridge/src/components/monitoring/index.ts
- legacybridge/src/components/ui/alert-dialog.tsx
- legacybridge/src/components/ui/badge.tsx
- legacybridge/src/components/ui/button.tsx
- legacybridge/src/components/ui/card.tsx
- legacybridge/src/components/ui/dropdown-menu.tsx
- legacybridge/src/components/ui/input.tsx
- legacybridge/src/components/ui/label.tsx
- legacybridge/src/components/ui/progress.tsx
- legacybridge/src/components/ui/separator.tsx
- legacybridge/src/components/ui/switch.tsx
- legacybridge/src/components/ui/tabs.tsx
- legacybridge/src/components/ui/tooltip.tsx
- legacybridge/src/enterprise/admin/AdminDashboard.tsx
- legacybridge/src/enterprise/api/enterprise-api.ts
- legacybridge/src/enterprise/audit/audit-logger.ts
- legacybridge/src/enterprise/auth/authentication.ts
- legacybridge/src/enterprise/auth/rbac.ts
- legacybridge/src/enterprise/config/kubernetes-deployment.yaml
- legacybridge/src/enterprise/services/tenant-conversion-service.ts
- legacybridge/src/enterprise/tenancy/tenant-context.ts
- legacybridge/src/hooks/useDebounce.ts
- legacybridge/src/hooks/useMonitoringWebSocket.ts
- legacybridge/src/lib/download-service.ts
- legacybridge/src/lib/error-logger.ts
- legacybridge/src/lib/monitoring/alert-manager.ts
- legacybridge/src/lib/monitoring/websocket-server.ts
- legacybridge/src/lib/sanitizer.ts
- legacybridge/src/lib/stores/files.ts
- legacybridge/src/lib/tauri-api.ts
- legacybridge/src/lib/unified-error-api.ts
- legacybridge/src/lib/utils.ts
- legacybridge/src/middleware.ts
- legacybridge/src/styles/monitoring.css
- legacybridge/src/types/errors.ts
- legacybridge/src/types/index.ts
- legacybridge/src/types/monitoring.ts
- legacybridge/src-tauri/security.config.json
- legacybridge/src-tauri/tauri.conf.json
- legacybridge/test_32bit_compatibility.c
- legacybridge/test_dll.c
- legacybridge/test_dll.py
- legacybridge/tests/accessibility/accessibility-tests.spec.ts
- legacybridge/tests/accessibility/component-accessibility.test.tsx
- legacybridge/tests/chaos/chaos-engineering-tests.spec.ts
- legacybridge/tests/framework/test-automation-framework.ts
- legacybridge/tests/framework/types.ts
- legacybridge/tests/integration/file-conversion-flow.test.tsx
- legacybridge/tests/load/k6-load-test.js
- legacybridge/tests/mocks/fileMock.js
- legacybridge/tests/mocks/styleMock.js
- legacybridge/tests/performance/performance-regression.test.ts
- legacybridge/tests/run-all-tests.js
- legacybridge/tests/security/security-test-suite.ts
- legacybridge/tests/setup/global-setup.ts
- legacybridge/tests/setup/global-teardown.ts
- legacybridge/tests/setup/jest.setup.js
- legacybridge/tests/unit/components/ConversionProgress.test.tsx
- legacybridge/tests/unit/components/DragDropZone.test.tsx
- legacybridge/tests/unit/components/ErrorBoundary.test.tsx
- legacybridge/tests/unit/components/MarkdownPreview.test.tsx
- legacybridge/tests/unit/components/MonitoringDashboard.test.tsx
- legacybridge/tests/unit/stores/files.test.ts
- legacybridge/tests/visual-regression/visual-regression-tests.spec.ts
- legacybridge/tsconfig.json
- tests/load/k6-load-test.js
- validate_performance_claims.py


## Code Changes (2025-07-26 18:51:14)

### Added Files
- .github/workflows/ci.yml
- COMPREHENSIVE_SECURITY_ANALYSIS.json
- docker-compose.yml
- docs/api/interactive-explorer/index.html
- docs/troubleshooting/interactive-wizard/index.html
- helm/legacybridge/Chart.yaml
- helm/legacybridge/values-production.yaml
- helm/legacybridge/values.yaml
- k8s/configmap.yaml
- k8s/deployment-optimized.yaml
- k8s/deployment.yaml
- k8s/ingress.yaml
- k8s/monitoring-stack.yaml
- k8s/monitoring.yaml
- legacybridge/.eslintrc.json
- legacybridge/ENTERPRISE_PACKAGE/MANIFEST.json
- legacybridge/ENTERPRISE_PACKAGE/examples/other/test_dll.c
- legacybridge/ENTERPRISE_PACKAGE/examples/other/test_dll.py
- legacybridge/ENTERPRISE_PACKAGE/include/legacybridge.h
- legacybridge/components.json
- legacybridge/deployment_package/include/legacybridge.h
- legacybridge/frontend-integration-example.ts
- legacybridge/include/legacybridge.h
- legacybridge/jest.config.js
- legacybridge/next.config.ts
- legacybridge/package-lock.json
- legacybridge/package.json
- legacybridge/perf_test.c
- legacybridge/playwright.config.ts
- legacybridge/src/__tests__/security/xss.test.tsx
- legacybridge/src/app/api/monitoring/alerts/route.ts
- legacybridge/src/app/api/monitoring/alerts/slack/route.ts
- legacybridge/src/app/api/monitoring/metrics/route.ts
- legacybridge/src/app/api/monitoring/prometheus/route.ts
- legacybridge/src/app/demo/page.tsx
- legacybridge/src/app/globals-backup.css
- legacybridge/src/app/globals.css
- legacybridge/src/app/home-page.tsx
- legacybridge/src/app/layout.tsx
- legacybridge/src/app/monitoring/page.tsx
- legacybridge/src/app/page.tsx
- legacybridge/src/components/ConversionProgress.tsx
- legacybridge/src/components/DiffView.tsx
- legacybridge/src/components/DownloadManager.tsx
- legacybridge/src/components/DragDropZone.tsx
- legacybridge/src/components/ErrorBoundary.tsx
- legacybridge/src/components/ErrorDisplay.tsx
- legacybridge/src/components/ErrorLogViewer.tsx
- legacybridge/src/components/LoadingAnimation.tsx
- legacybridge/src/components/MarkdownPreview.tsx
- legacybridge/src/components/PreviewPanel.tsx
- legacybridge/src/components/SuccessAnimation.tsx
- legacybridge/src/components/SyntaxHighlighter.tsx
- legacybridge/src/components/ThemeProvider.tsx
- legacybridge/src/components/UnifiedErrorDisplay.tsx
- legacybridge/src/components/layout/Footer.tsx
- legacybridge/src/components/layout/Header.tsx
- legacybridge/src/components/layout/MainLayout.tsx
- legacybridge/src/components/monitoring/BuildProgressRing.tsx
- legacybridge/src/components/monitoring/EnhancedFunctionCallMatrix.tsx
- legacybridge/src/components/monitoring/FunctionCallMatrix.tsx
- legacybridge/src/components/monitoring/LogStreamViewer.tsx
- legacybridge/src/components/monitoring/MonitoringDashboard.tsx
- legacybridge/src/components/monitoring/PerformanceChart.tsx
- legacybridge/src/components/monitoring/SystemHealthCard.tsx
- legacybridge/src/components/monitoring/index.ts
- legacybridge/src/components/ui/alert-dialog.tsx
- legacybridge/src/components/ui/badge.tsx
- legacybridge/src/components/ui/button.tsx
- legacybridge/src/components/ui/card.tsx
- legacybridge/src/components/ui/dropdown-menu.tsx
- legacybridge/src/components/ui/input.tsx
- legacybridge/src/components/ui/label.tsx
- legacybridge/src/components/ui/progress.tsx
- legacybridge/src/components/ui/separator.tsx
- legacybridge/src/components/ui/switch.tsx
- legacybridge/src/components/ui/tabs.tsx
- legacybridge/src/components/ui/tooltip.tsx
- legacybridge/src/enterprise/admin/AdminDashboard.tsx
- legacybridge/src/enterprise/api/enterprise-api.ts
- legacybridge/src/enterprise/audit/audit-logger.ts
- legacybridge/src/enterprise/auth/authentication.ts
- legacybridge/src/enterprise/auth/rbac.ts
- legacybridge/src/enterprise/config/kubernetes-deployment.yaml
- legacybridge/src/enterprise/services/tenant-conversion-service.ts
- legacybridge/src/enterprise/tenancy/tenant-context.ts
- legacybridge/src/hooks/useDebounce.ts
- legacybridge/src/hooks/useMonitoringWebSocket.ts
- legacybridge/src/lib/download-service.ts
- legacybridge/src/lib/error-logger.ts
- legacybridge/src/lib/monitoring/alert-manager.ts
- legacybridge/src/lib/monitoring/websocket-server.ts
- legacybridge/src/lib/sanitizer.ts
- legacybridge/src/lib/stores/files.ts
- legacybridge/src/lib/tauri-api.ts
- legacybridge/src/lib/unified-error-api.ts
- legacybridge/src/lib/utils.ts
- legacybridge/src/middleware.ts
- legacybridge/src/styles/monitoring.css
- legacybridge/src/types/errors.ts
- legacybridge/src/types/index.ts
- legacybridge/src/types/monitoring.ts
- legacybridge/src-tauri/security.config.json
- legacybridge/src-tauri/tauri.conf.json
- legacybridge/test_32bit_compatibility.c
- legacybridge/test_dll.c
- legacybridge/test_dll.py
- legacybridge/tests/accessibility/accessibility-tests.spec.ts
- legacybridge/tests/accessibility/component-accessibility.test.tsx
- legacybridge/tests/chaos/chaos-engineering-tests.spec.ts
- legacybridge/tests/framework/test-automation-framework.ts
- legacybridge/tests/framework/types.ts
- legacybridge/tests/integration/file-conversion-flow.test.tsx
- legacybridge/tests/load/k6-load-test.js
- legacybridge/tests/mocks/fileMock.js
- legacybridge/tests/mocks/styleMock.js
- legacybridge/tests/performance/performance-regression.test.ts
- legacybridge/tests/run-all-tests.js
- legacybridge/tests/security/security-test-suite.ts
- legacybridge/tests/setup/global-setup.ts
- legacybridge/tests/setup/global-teardown.ts
- legacybridge/tests/setup/jest.setup.js
- legacybridge/tests/unit/components/ConversionProgress.test.tsx
- legacybridge/tests/unit/components/DragDropZone.test.tsx
- legacybridge/tests/unit/components/ErrorBoundary.test.tsx
- legacybridge/tests/unit/components/MarkdownPreview.test.tsx
- legacybridge/tests/unit/components/MonitoringDashboard.test.tsx
- legacybridge/tests/unit/stores/files.test.ts
- legacybridge/tests/visual-regression/visual-regression-tests.spec.ts
- legacybridge/tsconfig.json
- tests/load/k6-load-test.js
- validate_performance_claims.py


## Code Changes (2025-07-26 18:46:45)

### Added Files
- .github/workflows/ci.yml
- COMPREHENSIVE_SECURITY_ANALYSIS.json
- docker-compose.yml
- docs/api/interactive-explorer/index.html
- docs/troubleshooting/interactive-wizard/index.html
- helm/legacybridge/Chart.yaml
- helm/legacybridge/values-production.yaml
- helm/legacybridge/values.yaml
- k8s/configmap.yaml
- k8s/deployment-optimized.yaml
- k8s/deployment.yaml
- k8s/ingress.yaml
- k8s/monitoring-stack.yaml
- k8s/monitoring.yaml
- legacybridge/.eslintrc.json
- legacybridge/ENTERPRISE_PACKAGE/MANIFEST.json
- legacybridge/ENTERPRISE_PACKAGE/examples/other/test_dll.c
- legacybridge/ENTERPRISE_PACKAGE/examples/other/test_dll.py
- legacybridge/ENTERPRISE_PACKAGE/include/legacybridge.h
- legacybridge/components.json
- legacybridge/deployment_package/include/legacybridge.h
- legacybridge/frontend-integration-example.ts
- legacybridge/include/legacybridge.h
- legacybridge/jest.config.js
- legacybridge/next.config.ts
- legacybridge/package-lock.json
- legacybridge/package.json
- legacybridge/perf_test.c
- legacybridge/playwright.config.ts
- legacybridge/src/__tests__/security/xss.test.tsx
- legacybridge/src/app/api/monitoring/alerts/route.ts
- legacybridge/src/app/api/monitoring/alerts/slack/route.ts
- legacybridge/src/app/api/monitoring/metrics/route.ts
- legacybridge/src/app/api/monitoring/prometheus/route.ts
- legacybridge/src/app/demo/page.tsx
- legacybridge/src/app/globals-backup.css
- legacybridge/src/app/globals.css
- legacybridge/src/app/home-page.tsx
- legacybridge/src/app/layout.tsx
- legacybridge/src/app/monitoring/page.tsx
- legacybridge/src/app/page.tsx
- legacybridge/src/components/ConversionProgress.tsx
- legacybridge/src/components/DiffView.tsx
- legacybridge/src/components/DownloadManager.tsx
- legacybridge/src/components/DragDropZone.tsx
- legacybridge/src/components/ErrorBoundary.tsx
- legacybridge/src/components/ErrorDisplay.tsx
- legacybridge/src/components/ErrorLogViewer.tsx
- legacybridge/src/components/LoadingAnimation.tsx
- legacybridge/src/components/MarkdownPreview.tsx
- legacybridge/src/components/PreviewPanel.tsx
- legacybridge/src/components/SuccessAnimation.tsx
- legacybridge/src/components/SyntaxHighlighter.tsx
- legacybridge/src/components/ThemeProvider.tsx
- legacybridge/src/components/UnifiedErrorDisplay.tsx
- legacybridge/src/components/layout/Footer.tsx
- legacybridge/src/components/layout/Header.tsx
- legacybridge/src/components/layout/MainLayout.tsx
- legacybridge/src/components/monitoring/BuildProgressRing.tsx
- legacybridge/src/components/monitoring/EnhancedFunctionCallMatrix.tsx
- legacybridge/src/components/monitoring/FunctionCallMatrix.tsx
- legacybridge/src/components/monitoring/LogStreamViewer.tsx
- legacybridge/src/components/monitoring/MonitoringDashboard.tsx
- legacybridge/src/components/monitoring/PerformanceChart.tsx
- legacybridge/src/components/monitoring/SystemHealthCard.tsx
- legacybridge/src/components/monitoring/index.ts
- legacybridge/src/components/ui/alert-dialog.tsx
- legacybridge/src/components/ui/badge.tsx
- legacybridge/src/components/ui/button.tsx
- legacybridge/src/components/ui/card.tsx
- legacybridge/src/components/ui/dropdown-menu.tsx
- legacybridge/src/components/ui/input.tsx
- legacybridge/src/components/ui/label.tsx
- legacybridge/src/components/ui/progress.tsx
- legacybridge/src/components/ui/separator.tsx
- legacybridge/src/components/ui/switch.tsx
- legacybridge/src/components/ui/tabs.tsx
- legacybridge/src/components/ui/tooltip.tsx
- legacybridge/src/enterprise/admin/AdminDashboard.tsx
- legacybridge/src/enterprise/api/enterprise-api.ts
- legacybridge/src/enterprise/audit/audit-logger.ts
- legacybridge/src/enterprise/auth/authentication.ts
- legacybridge/src/enterprise/auth/rbac.ts
- legacybridge/src/enterprise/config/kubernetes-deployment.yaml
- legacybridge/src/enterprise/services/tenant-conversion-service.ts
- legacybridge/src/enterprise/tenancy/tenant-context.ts
- legacybridge/src/hooks/useDebounce.ts
- legacybridge/src/hooks/useMonitoringWebSocket.ts
- legacybridge/src/lib/download-service.ts
- legacybridge/src/lib/error-logger.ts
- legacybridge/src/lib/monitoring/alert-manager.ts
- legacybridge/src/lib/monitoring/websocket-server.ts
- legacybridge/src/lib/sanitizer.ts
- legacybridge/src/lib/stores/files.ts
- legacybridge/src/lib/tauri-api.ts
- legacybridge/src/lib/unified-error-api.ts
- legacybridge/src/lib/utils.ts
- legacybridge/src/middleware.ts
- legacybridge/src/styles/monitoring.css
- legacybridge/src/types/errors.ts
- legacybridge/src/types/index.ts
- legacybridge/src/types/monitoring.ts
- legacybridge/src-tauri/security.config.json
- legacybridge/src-tauri/tauri.conf.json
- legacybridge/test_32bit_compatibility.c
- legacybridge/test_dll.c
- legacybridge/test_dll.py
- legacybridge/tests/accessibility/accessibility-tests.spec.ts
- legacybridge/tests/accessibility/component-accessibility.test.tsx
- legacybridge/tests/chaos/chaos-engineering-tests.spec.ts
- legacybridge/tests/framework/test-automation-framework.ts
- legacybridge/tests/framework/types.ts
- legacybridge/tests/integration/file-conversion-flow.test.tsx
- legacybridge/tests/load/k6-load-test.js
- legacybridge/tests/mocks/fileMock.js
- legacybridge/tests/mocks/styleMock.js
- legacybridge/tests/performance/performance-regression.test.ts
- legacybridge/tests/run-all-tests.js
- legacybridge/tests/security/security-test-suite.ts
- legacybridge/tests/setup/global-setup.ts
- legacybridge/tests/setup/global-teardown.ts
- legacybridge/tests/setup/jest.setup.js
- legacybridge/tests/unit/components/ConversionProgress.test.tsx
- legacybridge/tests/unit/components/DragDropZone.test.tsx
- legacybridge/tests/unit/components/ErrorBoundary.test.tsx
- legacybridge/tests/unit/components/MarkdownPreview.test.tsx
- legacybridge/tests/unit/components/MonitoringDashboard.test.tsx
- legacybridge/tests/unit/stores/files.test.ts
- legacybridge/tests/visual-regression/visual-regression-tests.spec.ts
- legacybridge/tsconfig.json
- tests/load/k6-load-test.js
- validate_performance_claims.py


## Code Changes (2025-07-26 05:57:57)

### Added Files
- .github/workflows/ci.yml
- COMPREHENSIVE_SECURITY_ANALYSIS.json
- docker-compose.yml
- docs/api/interactive-explorer/index.html
- docs/troubleshooting/interactive-wizard/index.html
- helm/legacybridge/Chart.yaml
- helm/legacybridge/values-production.yaml
- helm/legacybridge/values.yaml
- k8s/configmap.yaml
- k8s/deployment-optimized.yaml
- k8s/deployment.yaml
- k8s/ingress.yaml
- k8s/monitoring-stack.yaml
- k8s/monitoring.yaml
- legacybridge/.eslintrc.json
- legacybridge/ENTERPRISE_PACKAGE/MANIFEST.json
- legacybridge/ENTERPRISE_PACKAGE/examples/other/test_dll.c
- legacybridge/ENTERPRISE_PACKAGE/examples/other/test_dll.py
- legacybridge/ENTERPRISE_PACKAGE/include/legacybridge.h
- legacybridge/components.json
- legacybridge/deployment_package/include/legacybridge.h
- legacybridge/frontend-integration-example.ts
- legacybridge/include/legacybridge.h
- legacybridge/jest.config.js
- legacybridge/next.config.ts
- legacybridge/package-lock.json
- legacybridge/package.json
- legacybridge/perf_test.c
- legacybridge/playwright.config.ts
- legacybridge/src/__tests__/security/xss.test.tsx
- legacybridge/src/app/api/monitoring/alerts/route.ts
- legacybridge/src/app/api/monitoring/alerts/slack/route.ts
- legacybridge/src/app/api/monitoring/metrics/route.ts
- legacybridge/src/app/api/monitoring/prometheus/route.ts
- legacybridge/src/app/demo/page.tsx
- legacybridge/src/app/globals-backup.css
- legacybridge/src/app/globals.css
- legacybridge/src/app/home-page.tsx
- legacybridge/src/app/layout.tsx
- legacybridge/src/app/monitoring/page.tsx
- legacybridge/src/app/page.tsx
- legacybridge/src/components/ConversionProgress.tsx
- legacybridge/src/components/DiffView.tsx
- legacybridge/src/components/DownloadManager.tsx
- legacybridge/src/components/DragDropZone.tsx
- legacybridge/src/components/ErrorBoundary.tsx
- legacybridge/src/components/ErrorDisplay.tsx
- legacybridge/src/components/ErrorLogViewer.tsx
- legacybridge/src/components/LoadingAnimation.tsx
- legacybridge/src/components/MarkdownPreview.tsx
- legacybridge/src/components/PreviewPanel.tsx
- legacybridge/src/components/SuccessAnimation.tsx
- legacybridge/src/components/SyntaxHighlighter.tsx
- legacybridge/src/components/ThemeProvider.tsx
- legacybridge/src/components/UnifiedErrorDisplay.tsx
- legacybridge/src/components/layout/Footer.tsx
- legacybridge/src/components/layout/Header.tsx
- legacybridge/src/components/layout/MainLayout.tsx
- legacybridge/src/components/monitoring/BuildProgressRing.tsx
- legacybridge/src/components/monitoring/EnhancedFunctionCallMatrix.tsx
- legacybridge/src/components/monitoring/FunctionCallMatrix.tsx
- legacybridge/src/components/monitoring/LogStreamViewer.tsx
- legacybridge/src/components/monitoring/MonitoringDashboard.tsx
- legacybridge/src/components/monitoring/PerformanceChart.tsx
- legacybridge/src/components/monitoring/SystemHealthCard.tsx
- legacybridge/src/components/monitoring/index.ts
- legacybridge/src/components/ui/alert-dialog.tsx
- legacybridge/src/components/ui/badge.tsx
- legacybridge/src/components/ui/button.tsx
- legacybridge/src/components/ui/card.tsx
- legacybridge/src/components/ui/dropdown-menu.tsx
- legacybridge/src/components/ui/input.tsx
- legacybridge/src/components/ui/label.tsx
- legacybridge/src/components/ui/progress.tsx
- legacybridge/src/components/ui/separator.tsx
- legacybridge/src/components/ui/switch.tsx
- legacybridge/src/components/ui/tabs.tsx
- legacybridge/src/components/ui/tooltip.tsx
- legacybridge/src/enterprise/admin/AdminDashboard.tsx
- legacybridge/src/enterprise/api/enterprise-api.ts
- legacybridge/src/enterprise/audit/audit-logger.ts
- legacybridge/src/enterprise/auth/authentication.ts
- legacybridge/src/enterprise/auth/rbac.ts
- legacybridge/src/enterprise/config/kubernetes-deployment.yaml
- legacybridge/src/enterprise/services/tenant-conversion-service.ts
- legacybridge/src/enterprise/tenancy/tenant-context.ts
- legacybridge/src/hooks/useDebounce.ts
- legacybridge/src/hooks/useMonitoringWebSocket.ts
- legacybridge/src/lib/download-service.ts
- legacybridge/src/lib/error-logger.ts
- legacybridge/src/lib/monitoring/alert-manager.ts
- legacybridge/src/lib/monitoring/websocket-server.ts
- legacybridge/src/lib/sanitizer.ts
- legacybridge/src/lib/stores/files.ts
- legacybridge/src/lib/tauri-api.ts
- legacybridge/src/lib/unified-error-api.ts
- legacybridge/src/lib/utils.ts
- legacybridge/src/middleware.ts
- legacybridge/src/styles/monitoring.css
- legacybridge/src/types/errors.ts
- legacybridge/src/types/index.ts
- legacybridge/src/types/monitoring.ts
- legacybridge/src-tauri/security.config.json
- legacybridge/src-tauri/tauri.conf.json
- legacybridge/test_32bit_compatibility.c
- legacybridge/test_dll.c
- legacybridge/test_dll.py
- legacybridge/tests/accessibility/accessibility-tests.spec.ts
- legacybridge/tests/accessibility/component-accessibility.test.tsx
- legacybridge/tests/chaos/chaos-engineering-tests.spec.ts
- legacybridge/tests/framework/test-automation-framework.ts
- legacybridge/tests/framework/types.ts
- legacybridge/tests/integration/file-conversion-flow.test.tsx
- legacybridge/tests/load/k6-load-test.js
- legacybridge/tests/mocks/fileMock.js
- legacybridge/tests/mocks/styleMock.js
- legacybridge/tests/performance/performance-regression.test.ts
- legacybridge/tests/run-all-tests.js
- legacybridge/tests/security/security-test-suite.ts
- legacybridge/tests/setup/global-setup.ts
- legacybridge/tests/setup/global-teardown.ts
- legacybridge/tests/setup/jest.setup.js
- legacybridge/tests/unit/components/ConversionProgress.test.tsx
- legacybridge/tests/unit/components/DragDropZone.test.tsx
- legacybridge/tests/unit/components/ErrorBoundary.test.tsx
- legacybridge/tests/unit/components/MarkdownPreview.test.tsx
- legacybridge/tests/unit/components/MonitoringDashboard.test.tsx
- legacybridge/tests/unit/stores/files.test.ts
- legacybridge/tests/visual-regression/visual-regression-tests.spec.ts
- legacybridge/tsconfig.json
- tests/load/k6-load-test.js
- validate_performance_claims.py


## Code Changes (2025-07-26 01:14:13)

### Added Files
- .github/workflows/ci.yml
- COMPREHENSIVE_SECURITY_ANALYSIS.json
- docker-compose.yml
- docs/api/interactive-explorer/index.html
- docs/troubleshooting/interactive-wizard/index.html
- helm/legacybridge/Chart.yaml
- helm/legacybridge/values-production.yaml
- helm/legacybridge/values.yaml
- k8s/configmap.yaml
- k8s/deployment-optimized.yaml
- k8s/deployment.yaml
- k8s/ingress.yaml
- k8s/monitoring-stack.yaml
- k8s/monitoring.yaml
- legacybridge/.eslintrc.json
- legacybridge/ENTERPRISE_PACKAGE/MANIFEST.json
- legacybridge/ENTERPRISE_PACKAGE/examples/other/test_dll.c
- legacybridge/ENTERPRISE_PACKAGE/examples/other/test_dll.py
- legacybridge/ENTERPRISE_PACKAGE/include/legacybridge.h
- legacybridge/components.json
- legacybridge/deployment_package/include/legacybridge.h
- legacybridge/frontend-integration-example.ts
- legacybridge/include/legacybridge.h
- legacybridge/jest.config.js
- legacybridge/next.config.ts
- legacybridge/package-lock.json
- legacybridge/package.json
- legacybridge/perf_test.c
- legacybridge/playwright.config.ts
- legacybridge/src/__tests__/security/xss.test.tsx
- legacybridge/src/app/api/monitoring/alerts/route.ts
- legacybridge/src/app/api/monitoring/alerts/slack/route.ts
- legacybridge/src/app/api/monitoring/metrics/route.ts
- legacybridge/src/app/api/monitoring/prometheus/route.ts
- legacybridge/src/app/demo/page.tsx
- legacybridge/src/app/globals-backup.css
- legacybridge/src/app/globals.css
- legacybridge/src/app/home-page.tsx
- legacybridge/src/app/layout.tsx
- legacybridge/src/app/monitoring/page.tsx
- legacybridge/src/app/page.tsx
- legacybridge/src/components/ConversionProgress.tsx
- legacybridge/src/components/DiffView.tsx
- legacybridge/src/components/DownloadManager.tsx
- legacybridge/src/components/DragDropZone.tsx
- legacybridge/src/components/ErrorBoundary.tsx
- legacybridge/src/components/ErrorDisplay.tsx
- legacybridge/src/components/ErrorLogViewer.tsx
- legacybridge/src/components/LoadingAnimation.tsx
- legacybridge/src/components/MarkdownPreview.tsx
- legacybridge/src/components/PreviewPanel.tsx
- legacybridge/src/components/SuccessAnimation.tsx
- legacybridge/src/components/SyntaxHighlighter.tsx
- legacybridge/src/components/ThemeProvider.tsx
- legacybridge/src/components/UnifiedErrorDisplay.tsx
- legacybridge/src/components/layout/Footer.tsx
- legacybridge/src/components/layout/Header.tsx
- legacybridge/src/components/layout/MainLayout.tsx
- legacybridge/src/components/monitoring/BuildProgressRing.tsx
- legacybridge/src/components/monitoring/EnhancedFunctionCallMatrix.tsx
- legacybridge/src/components/monitoring/FunctionCallMatrix.tsx
- legacybridge/src/components/monitoring/LogStreamViewer.tsx
- legacybridge/src/components/monitoring/MonitoringDashboard.tsx
- legacybridge/src/components/monitoring/PerformanceChart.tsx
- legacybridge/src/components/monitoring/SystemHealthCard.tsx
- legacybridge/src/components/monitoring/index.ts
- legacybridge/src/components/ui/alert-dialog.tsx
- legacybridge/src/components/ui/badge.tsx
- legacybridge/src/components/ui/button.tsx
- legacybridge/src/components/ui/card.tsx
- legacybridge/src/components/ui/dropdown-menu.tsx
- legacybridge/src/components/ui/input.tsx
- legacybridge/src/components/ui/label.tsx
- legacybridge/src/components/ui/progress.tsx
- legacybridge/src/components/ui/separator.tsx
- legacybridge/src/components/ui/switch.tsx
- legacybridge/src/components/ui/tabs.tsx
- legacybridge/src/components/ui/tooltip.tsx
- legacybridge/src/enterprise/admin/AdminDashboard.tsx
- legacybridge/src/enterprise/api/enterprise-api.ts
- legacybridge/src/enterprise/audit/audit-logger.ts
- legacybridge/src/enterprise/auth/authentication.ts
- legacybridge/src/enterprise/auth/rbac.ts
- legacybridge/src/enterprise/config/kubernetes-deployment.yaml
- legacybridge/src/enterprise/services/tenant-conversion-service.ts
- legacybridge/src/enterprise/tenancy/tenant-context.ts
- legacybridge/src/hooks/useDebounce.ts
- legacybridge/src/hooks/useMonitoringWebSocket.ts
- legacybridge/src/lib/download-service.ts
- legacybridge/src/lib/error-logger.ts
- legacybridge/src/lib/monitoring/alert-manager.ts
- legacybridge/src/lib/monitoring/websocket-server.ts
- legacybridge/src/lib/sanitizer.ts
- legacybridge/src/lib/stores/files.ts
- legacybridge/src/lib/tauri-api.ts
- legacybridge/src/lib/unified-error-api.ts
- legacybridge/src/lib/utils.ts
- legacybridge/src/middleware.ts
- legacybridge/src/styles/monitoring.css
- legacybridge/src/types/errors.ts
- legacybridge/src/types/index.ts
- legacybridge/src/types/monitoring.ts
- legacybridge/src-tauri/security.config.json
- legacybridge/src-tauri/tauri.conf.json
- legacybridge/test_32bit_compatibility.c
- legacybridge/test_dll.c
- legacybridge/test_dll.py
- legacybridge/tests/accessibility/accessibility-tests.spec.ts
- legacybridge/tests/accessibility/component-accessibility.test.tsx
- legacybridge/tests/chaos/chaos-engineering-tests.spec.ts
- legacybridge/tests/framework/test-automation-framework.ts
- legacybridge/tests/framework/types.ts
- legacybridge/tests/integration/file-conversion-flow.test.tsx
- legacybridge/tests/load/k6-load-test.js
- legacybridge/tests/mocks/fileMock.js
- legacybridge/tests/mocks/styleMock.js
- legacybridge/tests/performance/performance-regression.test.ts
- legacybridge/tests/run-all-tests.js
- legacybridge/tests/security/security-test-suite.ts
- legacybridge/tests/setup/global-setup.ts
- legacybridge/tests/setup/global-teardown.ts
- legacybridge/tests/setup/jest.setup.js
- legacybridge/tests/unit/components/ConversionProgress.test.tsx
- legacybridge/tests/unit/components/DragDropZone.test.tsx
- legacybridge/tests/unit/components/ErrorBoundary.test.tsx
- legacybridge/tests/unit/components/MarkdownPreview.test.tsx
- legacybridge/tests/unit/components/MonitoringDashboard.test.tsx
- legacybridge/tests/unit/stores/files.test.ts
- legacybridge/tests/visual-regression/visual-regression-tests.spec.ts
- legacybridge/tsconfig.json
- tests/load/k6-load-test.js
- validate_performance_claims.py


## Code Changes (2025-07-26 01:05:53)

### Added Files
- .github/workflows/ci.yml
- COMPREHENSIVE_SECURITY_ANALYSIS.json
- docker-compose.yml
- docs/api/interactive-explorer/index.html
- docs/troubleshooting/interactive-wizard/index.html
- helm/legacybridge/Chart.yaml
- helm/legacybridge/values-production.yaml
- helm/legacybridge/values.yaml
- k8s/configmap.yaml
- k8s/deployment-optimized.yaml
- k8s/deployment.yaml
- k8s/ingress.yaml
- k8s/monitoring-stack.yaml
- k8s/monitoring.yaml
- legacybridge/.eslintrc.json
- legacybridge/ENTERPRISE_PACKAGE/MANIFEST.json
- legacybridge/ENTERPRISE_PACKAGE/examples/other/test_dll.c
- legacybridge/ENTERPRISE_PACKAGE/examples/other/test_dll.py
- legacybridge/ENTERPRISE_PACKAGE/include/legacybridge.h
- legacybridge/components.json
- legacybridge/deployment_package/include/legacybridge.h
- legacybridge/frontend-integration-example.ts
- legacybridge/include/legacybridge.h
- legacybridge/jest.config.js
- legacybridge/next.config.ts
- legacybridge/package-lock.json
- legacybridge/package.json
- legacybridge/perf_test.c
- legacybridge/playwright.config.ts
- legacybridge/src/__tests__/security/xss.test.tsx
- legacybridge/src/app/api/monitoring/alerts/route.ts
- legacybridge/src/app/api/monitoring/alerts/slack/route.ts
- legacybridge/src/app/api/monitoring/metrics/route.ts
- legacybridge/src/app/api/monitoring/prometheus/route.ts
- legacybridge/src/app/demo/page.tsx
- legacybridge/src/app/globals-backup.css
- legacybridge/src/app/globals.css
- legacybridge/src/app/home-page.tsx
- legacybridge/src/app/layout.tsx
- legacybridge/src/app/monitoring/page.tsx
- legacybridge/src/app/page.tsx
- legacybridge/src/components/ConversionProgress.tsx
- legacybridge/src/components/DiffView.tsx
- legacybridge/src/components/DownloadManager.tsx
- legacybridge/src/components/DragDropZone.tsx
- legacybridge/src/components/ErrorBoundary.tsx
- legacybridge/src/components/ErrorDisplay.tsx
- legacybridge/src/components/ErrorLogViewer.tsx
- legacybridge/src/components/LoadingAnimation.tsx
- legacybridge/src/components/MarkdownPreview.tsx
- legacybridge/src/components/PreviewPanel.tsx
- legacybridge/src/components/SuccessAnimation.tsx
- legacybridge/src/components/SyntaxHighlighter.tsx
- legacybridge/src/components/ThemeProvider.tsx
- legacybridge/src/components/UnifiedErrorDisplay.tsx
- legacybridge/src/components/layout/Footer.tsx
- legacybridge/src/components/layout/Header.tsx
- legacybridge/src/components/layout/MainLayout.tsx
- legacybridge/src/components/monitoring/BuildProgressRing.tsx
- legacybridge/src/components/monitoring/EnhancedFunctionCallMatrix.tsx
- legacybridge/src/components/monitoring/FunctionCallMatrix.tsx
- legacybridge/src/components/monitoring/LogStreamViewer.tsx
- legacybridge/src/components/monitoring/MonitoringDashboard.tsx
- legacybridge/src/components/monitoring/PerformanceChart.tsx
- legacybridge/src/components/monitoring/SystemHealthCard.tsx
- legacybridge/src/components/monitoring/index.ts
- legacybridge/src/components/ui/alert-dialog.tsx
- legacybridge/src/components/ui/badge.tsx
- legacybridge/src/components/ui/button.tsx
- legacybridge/src/components/ui/card.tsx
- legacybridge/src/components/ui/dropdown-menu.tsx
- legacybridge/src/components/ui/input.tsx
- legacybridge/src/components/ui/label.tsx
- legacybridge/src/components/ui/progress.tsx
- legacybridge/src/components/ui/separator.tsx
- legacybridge/src/components/ui/switch.tsx
- legacybridge/src/components/ui/tabs.tsx
- legacybridge/src/components/ui/tooltip.tsx
- legacybridge/src/enterprise/admin/AdminDashboard.tsx
- legacybridge/src/enterprise/api/enterprise-api.ts
- legacybridge/src/enterprise/audit/audit-logger.ts
- legacybridge/src/enterprise/auth/authentication.ts
- legacybridge/src/enterprise/auth/rbac.ts
- legacybridge/src/enterprise/config/kubernetes-deployment.yaml
- legacybridge/src/enterprise/services/tenant-conversion-service.ts
- legacybridge/src/enterprise/tenancy/tenant-context.ts
- legacybridge/src/hooks/useDebounce.ts
- legacybridge/src/hooks/useMonitoringWebSocket.ts
- legacybridge/src/lib/download-service.ts
- legacybridge/src/lib/error-logger.ts
- legacybridge/src/lib/monitoring/alert-manager.ts
- legacybridge/src/lib/monitoring/websocket-server.ts
- legacybridge/src/lib/sanitizer.ts
- legacybridge/src/lib/stores/files.ts
- legacybridge/src/lib/tauri-api.ts
- legacybridge/src/lib/unified-error-api.ts
- legacybridge/src/lib/utils.ts
- legacybridge/src/middleware.ts
- legacybridge/src/styles/monitoring.css
- legacybridge/src/types/errors.ts
- legacybridge/src/types/index.ts
- legacybridge/src/types/monitoring.ts
- legacybridge/src-tauri/security.config.json
- legacybridge/src-tauri/tauri.conf.json
- legacybridge/test_32bit_compatibility.c
- legacybridge/test_dll.c
- legacybridge/test_dll.py
- legacybridge/tests/accessibility/accessibility-tests.spec.ts
- legacybridge/tests/accessibility/component-accessibility.test.tsx
- legacybridge/tests/chaos/chaos-engineering-tests.spec.ts
- legacybridge/tests/framework/test-automation-framework.ts
- legacybridge/tests/framework/types.ts
- legacybridge/tests/integration/file-conversion-flow.test.tsx
- legacybridge/tests/load/k6-load-test.js
- legacybridge/tests/mocks/fileMock.js
- legacybridge/tests/mocks/styleMock.js
- legacybridge/tests/performance/performance-regression.test.ts
- legacybridge/tests/run-all-tests.js
- legacybridge/tests/security/security-test-suite.ts
- legacybridge/tests/setup/global-setup.ts
- legacybridge/tests/setup/global-teardown.ts
- legacybridge/tests/setup/jest.setup.js
- legacybridge/tests/unit/components/ConversionProgress.test.tsx
- legacybridge/tests/unit/components/DragDropZone.test.tsx
- legacybridge/tests/unit/components/ErrorBoundary.test.tsx
- legacybridge/tests/unit/components/MarkdownPreview.test.tsx
- legacybridge/tests/unit/components/MonitoringDashboard.test.tsx
- legacybridge/tests/unit/stores/files.test.ts
- legacybridge/tests/visual-regression/visual-regression-tests.spec.ts
- legacybridge/tsconfig.json
- tests/load/k6-load-test.js
- validate_performance_claims.py


## Code Changes (2025-07-26 00:56:11)

### Added Files
- .github/workflows/ci.yml
- COMPREHENSIVE_SECURITY_ANALYSIS.json
- docker-compose.yml
- docs/api/interactive-explorer/index.html
- docs/troubleshooting/interactive-wizard/index.html
- helm/legacybridge/Chart.yaml
- helm/legacybridge/values-production.yaml
- helm/legacybridge/values.yaml
- k8s/configmap.yaml
- k8s/deployment-optimized.yaml
- k8s/deployment.yaml
- k8s/ingress.yaml
- k8s/monitoring-stack.yaml
- k8s/monitoring.yaml
- legacybridge/.eslintrc.json
- legacybridge/ENTERPRISE_PACKAGE/MANIFEST.json
- legacybridge/ENTERPRISE_PACKAGE/examples/other/test_dll.c
- legacybridge/ENTERPRISE_PACKAGE/examples/other/test_dll.py
- legacybridge/ENTERPRISE_PACKAGE/include/legacybridge.h
- legacybridge/components.json
- legacybridge/deployment_package/include/legacybridge.h
- legacybridge/frontend-integration-example.ts
- legacybridge/include/legacybridge.h
- legacybridge/jest.config.js
- legacybridge/next.config.ts
- legacybridge/package-lock.json
- legacybridge/package.json
- legacybridge/perf_test.c
- legacybridge/playwright.config.ts
- legacybridge/src/__tests__/security/xss.test.tsx
- legacybridge/src/app/api/monitoring/alerts/route.ts
- legacybridge/src/app/api/monitoring/alerts/slack/route.ts
- legacybridge/src/app/api/monitoring/metrics/route.ts
- legacybridge/src/app/api/monitoring/prometheus/route.ts
- legacybridge/src/app/demo/page.tsx
- legacybridge/src/app/globals-backup.css
- legacybridge/src/app/globals.css
- legacybridge/src/app/home-page.tsx
- legacybridge/src/app/layout.tsx
- legacybridge/src/app/monitoring/page.tsx
- legacybridge/src/app/page.tsx
- legacybridge/src/components/ConversionProgress.tsx
- legacybridge/src/components/DiffView.tsx
- legacybridge/src/components/DownloadManager.tsx
- legacybridge/src/components/DragDropZone.tsx
- legacybridge/src/components/ErrorBoundary.tsx
- legacybridge/src/components/ErrorDisplay.tsx
- legacybridge/src/components/ErrorLogViewer.tsx
- legacybridge/src/components/LoadingAnimation.tsx
- legacybridge/src/components/MarkdownPreview.tsx
- legacybridge/src/components/PreviewPanel.tsx
- legacybridge/src/components/SuccessAnimation.tsx
- legacybridge/src/components/SyntaxHighlighter.tsx
- legacybridge/src/components/ThemeProvider.tsx
- legacybridge/src/components/UnifiedErrorDisplay.tsx
- legacybridge/src/components/layout/Footer.tsx
- legacybridge/src/components/layout/Header.tsx
- legacybridge/src/components/layout/MainLayout.tsx
- legacybridge/src/components/monitoring/BuildProgressRing.tsx
- legacybridge/src/components/monitoring/EnhancedFunctionCallMatrix.tsx
- legacybridge/src/components/monitoring/FunctionCallMatrix.tsx
- legacybridge/src/components/monitoring/LogStreamViewer.tsx
- legacybridge/src/components/monitoring/MonitoringDashboard.tsx
- legacybridge/src/components/monitoring/PerformanceChart.tsx
- legacybridge/src/components/monitoring/SystemHealthCard.tsx
- legacybridge/src/components/monitoring/index.ts
- legacybridge/src/components/ui/alert-dialog.tsx
- legacybridge/src/components/ui/badge.tsx
- legacybridge/src/components/ui/button.tsx
- legacybridge/src/components/ui/card.tsx
- legacybridge/src/components/ui/dropdown-menu.tsx
- legacybridge/src/components/ui/input.tsx
- legacybridge/src/components/ui/label.tsx
- legacybridge/src/components/ui/progress.tsx
- legacybridge/src/components/ui/separator.tsx
- legacybridge/src/components/ui/switch.tsx
- legacybridge/src/components/ui/tabs.tsx
- legacybridge/src/components/ui/tooltip.tsx
- legacybridge/src/enterprise/admin/AdminDashboard.tsx
- legacybridge/src/enterprise/api/enterprise-api.ts
- legacybridge/src/enterprise/audit/audit-logger.ts
- legacybridge/src/enterprise/auth/authentication.ts
- legacybridge/src/enterprise/auth/rbac.ts
- legacybridge/src/enterprise/config/kubernetes-deployment.yaml
- legacybridge/src/enterprise/services/tenant-conversion-service.ts
- legacybridge/src/enterprise/tenancy/tenant-context.ts
- legacybridge/src/hooks/useDebounce.ts
- legacybridge/src/hooks/useMonitoringWebSocket.ts
- legacybridge/src/lib/download-service.ts
- legacybridge/src/lib/error-logger.ts
- legacybridge/src/lib/monitoring/alert-manager.ts
- legacybridge/src/lib/monitoring/websocket-server.ts
- legacybridge/src/lib/sanitizer.ts
- legacybridge/src/lib/stores/files.ts
- legacybridge/src/lib/tauri-api.ts
- legacybridge/src/lib/unified-error-api.ts
- legacybridge/src/lib/utils.ts
- legacybridge/src/middleware.ts
- legacybridge/src/styles/monitoring.css
- legacybridge/src/types/errors.ts
- legacybridge/src/types/index.ts
- legacybridge/src/types/monitoring.ts
- legacybridge/src-tauri/security.config.json
- legacybridge/src-tauri/tauri.conf.json
- legacybridge/test_32bit_compatibility.c
- legacybridge/test_dll.c
- legacybridge/test_dll.py
- legacybridge/tests/accessibility/accessibility-tests.spec.ts
- legacybridge/tests/accessibility/component-accessibility.test.tsx
- legacybridge/tests/chaos/chaos-engineering-tests.spec.ts
- legacybridge/tests/framework/test-automation-framework.ts
- legacybridge/tests/framework/types.ts
- legacybridge/tests/integration/file-conversion-flow.test.tsx
- legacybridge/tests/load/k6-load-test.js
- legacybridge/tests/mocks/fileMock.js
- legacybridge/tests/mocks/styleMock.js
- legacybridge/tests/performance/performance-regression.test.ts
- legacybridge/tests/run-all-tests.js
- legacybridge/tests/security/security-test-suite.ts
- legacybridge/tests/setup/global-setup.ts
- legacybridge/tests/setup/global-teardown.ts
- legacybridge/tests/setup/jest.setup.js
- legacybridge/tests/unit/components/ConversionProgress.test.tsx
- legacybridge/tests/unit/components/DragDropZone.test.tsx
- legacybridge/tests/unit/components/ErrorBoundary.test.tsx
- legacybridge/tests/unit/components/MarkdownPreview.test.tsx
- legacybridge/tests/unit/components/MonitoringDashboard.test.tsx
- legacybridge/tests/unit/stores/files.test.ts
- legacybridge/tests/visual-regression/visual-regression-tests.spec.ts
- legacybridge/tsconfig.json
- tests/load/k6-load-test.js
- validate_performance_claims.py


## Code Changes (2025-07-26 00:53:39)

### Added Files
- .github/workflows/ci.yml
- COMPREHENSIVE_SECURITY_ANALYSIS.json
- docker-compose.yml
- docs/api/interactive-explorer/index.html
- docs/troubleshooting/interactive-wizard/index.html
- helm/legacybridge/Chart.yaml
- helm/legacybridge/values-production.yaml
- helm/legacybridge/values.yaml
- k8s/configmap.yaml
- k8s/deployment-optimized.yaml
- k8s/deployment.yaml
- k8s/ingress.yaml
- k8s/monitoring-stack.yaml
- k8s/monitoring.yaml
- legacybridge/.eslintrc.json
- legacybridge/ENTERPRISE_PACKAGE/MANIFEST.json
- legacybridge/ENTERPRISE_PACKAGE/examples/other/test_dll.c
- legacybridge/ENTERPRISE_PACKAGE/examples/other/test_dll.py
- legacybridge/ENTERPRISE_PACKAGE/include/legacybridge.h
- legacybridge/components.json
- legacybridge/deployment_package/include/legacybridge.h
- legacybridge/frontend-integration-example.ts
- legacybridge/include/legacybridge.h
- legacybridge/jest.config.js
- legacybridge/next.config.ts
- legacybridge/package-lock.json
- legacybridge/package.json
- legacybridge/perf_test.c
- legacybridge/playwright.config.ts
- legacybridge/src/__tests__/security/xss.test.tsx
- legacybridge/src/app/api/monitoring/alerts/route.ts
- legacybridge/src/app/api/monitoring/alerts/slack/route.ts
- legacybridge/src/app/api/monitoring/metrics/route.ts
- legacybridge/src/app/api/monitoring/prometheus/route.ts
- legacybridge/src/app/demo/page.tsx
- legacybridge/src/app/globals-backup.css
- legacybridge/src/app/globals.css
- legacybridge/src/app/home-page.tsx
- legacybridge/src/app/layout.tsx
- legacybridge/src/app/monitoring/page.tsx
- legacybridge/src/app/page.tsx
- legacybridge/src/components/ConversionProgress.tsx
- legacybridge/src/components/DiffView.tsx
- legacybridge/src/components/DownloadManager.tsx
- legacybridge/src/components/DragDropZone.tsx
- legacybridge/src/components/ErrorBoundary.tsx
- legacybridge/src/components/ErrorDisplay.tsx
- legacybridge/src/components/ErrorLogViewer.tsx
- legacybridge/src/components/LoadingAnimation.tsx
- legacybridge/src/components/MarkdownPreview.tsx
- legacybridge/src/components/PreviewPanel.tsx
- legacybridge/src/components/SuccessAnimation.tsx
- legacybridge/src/components/SyntaxHighlighter.tsx
- legacybridge/src/components/ThemeProvider.tsx
- legacybridge/src/components/UnifiedErrorDisplay.tsx
- legacybridge/src/components/layout/Footer.tsx
- legacybridge/src/components/layout/Header.tsx
- legacybridge/src/components/layout/MainLayout.tsx
- legacybridge/src/components/monitoring/BuildProgressRing.tsx
- legacybridge/src/components/monitoring/EnhancedFunctionCallMatrix.tsx
- legacybridge/src/components/monitoring/FunctionCallMatrix.tsx
- legacybridge/src/components/monitoring/LogStreamViewer.tsx
- legacybridge/src/components/monitoring/MonitoringDashboard.tsx
- legacybridge/src/components/monitoring/PerformanceChart.tsx
- legacybridge/src/components/monitoring/SystemHealthCard.tsx
- legacybridge/src/components/monitoring/index.ts
- legacybridge/src/components/ui/alert-dialog.tsx
- legacybridge/src/components/ui/badge.tsx
- legacybridge/src/components/ui/button.tsx
- legacybridge/src/components/ui/card.tsx
- legacybridge/src/components/ui/dropdown-menu.tsx
- legacybridge/src/components/ui/input.tsx
- legacybridge/src/components/ui/label.tsx
- legacybridge/src/components/ui/progress.tsx
- legacybridge/src/components/ui/separator.tsx
- legacybridge/src/components/ui/switch.tsx
- legacybridge/src/components/ui/tabs.tsx
- legacybridge/src/components/ui/tooltip.tsx
- legacybridge/src/enterprise/admin/AdminDashboard.tsx
- legacybridge/src/enterprise/api/enterprise-api.ts
- legacybridge/src/enterprise/audit/audit-logger.ts
- legacybridge/src/enterprise/auth/authentication.ts
- legacybridge/src/enterprise/auth/rbac.ts
- legacybridge/src/enterprise/config/kubernetes-deployment.yaml
- legacybridge/src/enterprise/services/tenant-conversion-service.ts
- legacybridge/src/enterprise/tenancy/tenant-context.ts
- legacybridge/src/hooks/useDebounce.ts
- legacybridge/src/hooks/useMonitoringWebSocket.ts
- legacybridge/src/lib/download-service.ts
- legacybridge/src/lib/error-logger.ts
- legacybridge/src/lib/monitoring/alert-manager.ts
- legacybridge/src/lib/monitoring/websocket-server.ts
- legacybridge/src/lib/sanitizer.ts
- legacybridge/src/lib/stores/files.ts
- legacybridge/src/lib/tauri-api.ts
- legacybridge/src/lib/unified-error-api.ts
- legacybridge/src/lib/utils.ts
- legacybridge/src/middleware.ts
- legacybridge/src/styles/monitoring.css
- legacybridge/src/types/errors.ts
- legacybridge/src/types/index.ts
- legacybridge/src/types/monitoring.ts
- legacybridge/src-tauri/security.config.json
- legacybridge/src-tauri/tauri.conf.json
- legacybridge/test_32bit_compatibility.c
- legacybridge/test_dll.c
- legacybridge/test_dll.py
- legacybridge/tests/accessibility/accessibility-tests.spec.ts
- legacybridge/tests/accessibility/component-accessibility.test.tsx
- legacybridge/tests/chaos/chaos-engineering-tests.spec.ts
- legacybridge/tests/framework/test-automation-framework.ts
- legacybridge/tests/framework/types.ts
- legacybridge/tests/integration/file-conversion-flow.test.tsx
- legacybridge/tests/load/k6-load-test.js
- legacybridge/tests/mocks/fileMock.js
- legacybridge/tests/mocks/styleMock.js
- legacybridge/tests/performance/performance-regression.test.ts
- legacybridge/tests/run-all-tests.js
- legacybridge/tests/security/security-test-suite.ts
- legacybridge/tests/setup/global-setup.ts
- legacybridge/tests/setup/global-teardown.ts
- legacybridge/tests/setup/jest.setup.js
- legacybridge/tests/unit/components/ConversionProgress.test.tsx
- legacybridge/tests/unit/components/DragDropZone.test.tsx
- legacybridge/tests/unit/components/ErrorBoundary.test.tsx
- legacybridge/tests/unit/components/MarkdownPreview.test.tsx
- legacybridge/tests/unit/components/MonitoringDashboard.test.tsx
- legacybridge/tests/unit/stores/files.test.ts
- legacybridge/tests/visual-regression/visual-regression-tests.spec.ts
- legacybridge/tsconfig.json
- tests/load/k6-load-test.js
- validate_performance_claims.py


## Code Changes (2025-07-26 00:50:19)

### Added Files
- .github/workflows/ci.yml
- COMPREHENSIVE_SECURITY_ANALYSIS.json
- docker-compose.yml
- docs/api/interactive-explorer/index.html
- docs/troubleshooting/interactive-wizard/index.html
- helm/legacybridge/Chart.yaml
- helm/legacybridge/values-production.yaml
- helm/legacybridge/values.yaml
- k8s/configmap.yaml
- k8s/deployment-optimized.yaml
- k8s/deployment.yaml
- k8s/ingress.yaml
- k8s/monitoring-stack.yaml
- k8s/monitoring.yaml
- legacybridge/.eslintrc.json
- legacybridge/ENTERPRISE_PACKAGE/MANIFEST.json
- legacybridge/ENTERPRISE_PACKAGE/examples/other/test_dll.c
- legacybridge/ENTERPRISE_PACKAGE/examples/other/test_dll.py
- legacybridge/ENTERPRISE_PACKAGE/include/legacybridge.h
- legacybridge/components.json
- legacybridge/deployment_package/include/legacybridge.h
- legacybridge/frontend-integration-example.ts
- legacybridge/include/legacybridge.h
- legacybridge/jest.config.js
- legacybridge/next.config.ts
- legacybridge/package-lock.json
- legacybridge/package.json
- legacybridge/perf_test.c
- legacybridge/playwright.config.ts
- legacybridge/src/__tests__/security/xss.test.tsx
- legacybridge/src/app/api/monitoring/alerts/route.ts
- legacybridge/src/app/api/monitoring/alerts/slack/route.ts
- legacybridge/src/app/api/monitoring/metrics/route.ts
- legacybridge/src/app/api/monitoring/prometheus/route.ts
- legacybridge/src/app/demo/page.tsx
- legacybridge/src/app/globals-backup.css
- legacybridge/src/app/globals.css
- legacybridge/src/app/home-page.tsx
- legacybridge/src/app/layout.tsx
- legacybridge/src/app/monitoring/page.tsx
- legacybridge/src/app/page.tsx
- legacybridge/src/components/ConversionProgress.tsx
- legacybridge/src/components/DiffView.tsx
- legacybridge/src/components/DownloadManager.tsx
- legacybridge/src/components/DragDropZone.tsx
- legacybridge/src/components/ErrorBoundary.tsx
- legacybridge/src/components/ErrorDisplay.tsx
- legacybridge/src/components/ErrorLogViewer.tsx
- legacybridge/src/components/LoadingAnimation.tsx
- legacybridge/src/components/MarkdownPreview.tsx
- legacybridge/src/components/PreviewPanel.tsx
- legacybridge/src/components/SuccessAnimation.tsx
- legacybridge/src/components/SyntaxHighlighter.tsx
- legacybridge/src/components/ThemeProvider.tsx
- legacybridge/src/components/UnifiedErrorDisplay.tsx
- legacybridge/src/components/layout/Footer.tsx
- legacybridge/src/components/layout/Header.tsx
- legacybridge/src/components/layout/MainLayout.tsx
- legacybridge/src/components/monitoring/BuildProgressRing.tsx
- legacybridge/src/components/monitoring/EnhancedFunctionCallMatrix.tsx
- legacybridge/src/components/monitoring/FunctionCallMatrix.tsx
- legacybridge/src/components/monitoring/LogStreamViewer.tsx
- legacybridge/src/components/monitoring/MonitoringDashboard.tsx
- legacybridge/src/components/monitoring/PerformanceChart.tsx
- legacybridge/src/components/monitoring/SystemHealthCard.tsx
- legacybridge/src/components/monitoring/index.ts
- legacybridge/src/components/ui/alert-dialog.tsx
- legacybridge/src/components/ui/badge.tsx
- legacybridge/src/components/ui/button.tsx
- legacybridge/src/components/ui/card.tsx
- legacybridge/src/components/ui/dropdown-menu.tsx
- legacybridge/src/components/ui/input.tsx
- legacybridge/src/components/ui/label.tsx
- legacybridge/src/components/ui/progress.tsx
- legacybridge/src/components/ui/separator.tsx
- legacybridge/src/components/ui/switch.tsx
- legacybridge/src/components/ui/tabs.tsx
- legacybridge/src/components/ui/tooltip.tsx
- legacybridge/src/enterprise/admin/AdminDashboard.tsx
- legacybridge/src/enterprise/api/enterprise-api.ts
- legacybridge/src/enterprise/audit/audit-logger.ts
- legacybridge/src/enterprise/auth/authentication.ts
- legacybridge/src/enterprise/auth/rbac.ts
- legacybridge/src/enterprise/config/kubernetes-deployment.yaml
- legacybridge/src/enterprise/services/tenant-conversion-service.ts
- legacybridge/src/enterprise/tenancy/tenant-context.ts
- legacybridge/src/hooks/useDebounce.ts
- legacybridge/src/hooks/useMonitoringWebSocket.ts
- legacybridge/src/lib/download-service.ts
- legacybridge/src/lib/error-logger.ts
- legacybridge/src/lib/monitoring/alert-manager.ts
- legacybridge/src/lib/monitoring/websocket-server.ts
- legacybridge/src/lib/sanitizer.ts
- legacybridge/src/lib/stores/files.ts
- legacybridge/src/lib/tauri-api.ts
- legacybridge/src/lib/unified-error-api.ts
- legacybridge/src/lib/utils.ts
- legacybridge/src/middleware.ts
- legacybridge/src/styles/monitoring.css
- legacybridge/src/types/errors.ts
- legacybridge/src/types/index.ts
- legacybridge/src/types/monitoring.ts
- legacybridge/src-tauri/security.config.json
- legacybridge/src-tauri/tauri.conf.json
- legacybridge/test_32bit_compatibility.c
- legacybridge/test_dll.c
- legacybridge/test_dll.py
- legacybridge/tests/accessibility/accessibility-tests.spec.ts
- legacybridge/tests/accessibility/component-accessibility.test.tsx
- legacybridge/tests/chaos/chaos-engineering-tests.spec.ts
- legacybridge/tests/framework/test-automation-framework.ts
- legacybridge/tests/framework/types.ts
- legacybridge/tests/integration/file-conversion-flow.test.tsx
- legacybridge/tests/load/k6-load-test.js
- legacybridge/tests/mocks/fileMock.js
- legacybridge/tests/mocks/styleMock.js
- legacybridge/tests/performance/performance-regression.test.ts
- legacybridge/tests/run-all-tests.js
- legacybridge/tests/security/security-test-suite.ts
- legacybridge/tests/setup/global-setup.ts
- legacybridge/tests/setup/global-teardown.ts
- legacybridge/tests/setup/jest.setup.js
- legacybridge/tests/unit/components/ConversionProgress.test.tsx
- legacybridge/tests/unit/components/DragDropZone.test.tsx
- legacybridge/tests/unit/components/ErrorBoundary.test.tsx
- legacybridge/tests/unit/components/MarkdownPreview.test.tsx
- legacybridge/tests/unit/components/MonitoringDashboard.test.tsx
- legacybridge/tests/unit/stores/files.test.ts
- legacybridge/tests/visual-regression/visual-regression-tests.spec.ts
- legacybridge/tsconfig.json
- tests/load/k6-load-test.js
- validate_performance_claims.py


## Code Changes (2025-07-26 00:44:38)

### Added Files
- .github/workflows/ci.yml
- COMPREHENSIVE_SECURITY_ANALYSIS.json
- docker-compose.yml
- docs/api/interactive-explorer/index.html
- docs/troubleshooting/interactive-wizard/index.html
- helm/legacybridge/Chart.yaml
- helm/legacybridge/values-production.yaml
- helm/legacybridge/values.yaml
- k8s/configmap.yaml
- k8s/deployment-optimized.yaml
- k8s/deployment.yaml
- k8s/ingress.yaml
- k8s/monitoring-stack.yaml
- k8s/monitoring.yaml
- legacybridge/.eslintrc.json
- legacybridge/ENTERPRISE_PACKAGE/MANIFEST.json
- legacybridge/ENTERPRISE_PACKAGE/examples/other/test_dll.c
- legacybridge/ENTERPRISE_PACKAGE/examples/other/test_dll.py
- legacybridge/ENTERPRISE_PACKAGE/include/legacybridge.h
- legacybridge/components.json
- legacybridge/deployment_package/include/legacybridge.h
- legacybridge/frontend-integration-example.ts
- legacybridge/include/legacybridge.h
- legacybridge/jest.config.js
- legacybridge/next.config.ts
- legacybridge/package-lock.json
- legacybridge/package.json
- legacybridge/perf_test.c
- legacybridge/playwright.config.ts
- legacybridge/src/__tests__/security/xss.test.tsx
- legacybridge/src/app/api/monitoring/alerts/route.ts
- legacybridge/src/app/api/monitoring/alerts/slack/route.ts
- legacybridge/src/app/api/monitoring/metrics/route.ts
- legacybridge/src/app/api/monitoring/prometheus/route.ts
- legacybridge/src/app/demo/page.tsx
- legacybridge/src/app/globals-backup.css
- legacybridge/src/app/globals.css
- legacybridge/src/app/home-page.tsx
- legacybridge/src/app/layout.tsx
- legacybridge/src/app/monitoring/page.tsx
- legacybridge/src/app/page.tsx
- legacybridge/src/components/ConversionProgress.tsx
- legacybridge/src/components/DiffView.tsx
- legacybridge/src/components/DownloadManager.tsx
- legacybridge/src/components/DragDropZone.tsx
- legacybridge/src/components/ErrorBoundary.tsx
- legacybridge/src/components/ErrorDisplay.tsx
- legacybridge/src/components/ErrorLogViewer.tsx
- legacybridge/src/components/LoadingAnimation.tsx
- legacybridge/src/components/MarkdownPreview.tsx
- legacybridge/src/components/PreviewPanel.tsx
- legacybridge/src/components/SuccessAnimation.tsx
- legacybridge/src/components/SyntaxHighlighter.tsx
- legacybridge/src/components/ThemeProvider.tsx
- legacybridge/src/components/UnifiedErrorDisplay.tsx
- legacybridge/src/components/layout/Footer.tsx
- legacybridge/src/components/layout/Header.tsx
- legacybridge/src/components/layout/MainLayout.tsx
- legacybridge/src/components/monitoring/BuildProgressRing.tsx
- legacybridge/src/components/monitoring/EnhancedFunctionCallMatrix.tsx
- legacybridge/src/components/monitoring/FunctionCallMatrix.tsx
- legacybridge/src/components/monitoring/LogStreamViewer.tsx
- legacybridge/src/components/monitoring/MonitoringDashboard.tsx
- legacybridge/src/components/monitoring/PerformanceChart.tsx
- legacybridge/src/components/monitoring/SystemHealthCard.tsx
- legacybridge/src/components/monitoring/index.ts
- legacybridge/src/components/ui/alert-dialog.tsx
- legacybridge/src/components/ui/badge.tsx
- legacybridge/src/components/ui/button.tsx
- legacybridge/src/components/ui/card.tsx
- legacybridge/src/components/ui/dropdown-menu.tsx
- legacybridge/src/components/ui/input.tsx
- legacybridge/src/components/ui/label.tsx
- legacybridge/src/components/ui/progress.tsx
- legacybridge/src/components/ui/separator.tsx
- legacybridge/src/components/ui/switch.tsx
- legacybridge/src/components/ui/tabs.tsx
- legacybridge/src/components/ui/tooltip.tsx
- legacybridge/src/enterprise/admin/AdminDashboard.tsx
- legacybridge/src/enterprise/api/enterprise-api.ts
- legacybridge/src/enterprise/audit/audit-logger.ts
- legacybridge/src/enterprise/auth/authentication.ts
- legacybridge/src/enterprise/auth/rbac.ts
- legacybridge/src/enterprise/config/kubernetes-deployment.yaml
- legacybridge/src/enterprise/services/tenant-conversion-service.ts
- legacybridge/src/enterprise/tenancy/tenant-context.ts
- legacybridge/src/hooks/useDebounce.ts
- legacybridge/src/hooks/useMonitoringWebSocket.ts
- legacybridge/src/lib/download-service.ts
- legacybridge/src/lib/error-logger.ts
- legacybridge/src/lib/monitoring/alert-manager.ts
- legacybridge/src/lib/monitoring/websocket-server.ts
- legacybridge/src/lib/sanitizer.ts
- legacybridge/src/lib/stores/files.ts
- legacybridge/src/lib/tauri-api.ts
- legacybridge/src/lib/unified-error-api.ts
- legacybridge/src/lib/utils.ts
- legacybridge/src/middleware.ts
- legacybridge/src/styles/monitoring.css
- legacybridge/src/types/errors.ts
- legacybridge/src/types/index.ts
- legacybridge/src/types/monitoring.ts
- legacybridge/src-tauri/security.config.json
- legacybridge/src-tauri/tauri.conf.json
- legacybridge/test_32bit_compatibility.c
- legacybridge/test_dll.c
- legacybridge/test_dll.py
- legacybridge/tests/accessibility/accessibility-tests.spec.ts
- legacybridge/tests/accessibility/component-accessibility.test.tsx
- legacybridge/tests/chaos/chaos-engineering-tests.spec.ts
- legacybridge/tests/framework/test-automation-framework.ts
- legacybridge/tests/framework/types.ts
- legacybridge/tests/integration/file-conversion-flow.test.tsx
- legacybridge/tests/load/k6-load-test.js
- legacybridge/tests/mocks/fileMock.js
- legacybridge/tests/mocks/styleMock.js
- legacybridge/tests/performance/performance-regression.test.ts
- legacybridge/tests/run-all-tests.js
- legacybridge/tests/security/security-test-suite.ts
- legacybridge/tests/setup/global-setup.ts
- legacybridge/tests/setup/global-teardown.ts
- legacybridge/tests/setup/jest.setup.js
- legacybridge/tests/unit/components/ConversionProgress.test.tsx
- legacybridge/tests/unit/components/DragDropZone.test.tsx
- legacybridge/tests/unit/components/ErrorBoundary.test.tsx
- legacybridge/tests/unit/components/MarkdownPreview.test.tsx
- legacybridge/tests/unit/components/MonitoringDashboard.test.tsx
- legacybridge/tests/unit/stores/files.test.ts
- legacybridge/tests/visual-regression/visual-regression-tests.spec.ts
- legacybridge/tsconfig.json
- tests/load/k6-load-test.js
- validate_performance_claims.py


## Code Changes (2025-07-26 00:37:42)

### Added Files
- .github/workflows/ci.yml
- COMPREHENSIVE_SECURITY_ANALYSIS.json
- docker-compose.yml
- docs/api/interactive-explorer/index.html
- docs/troubleshooting/interactive-wizard/index.html
- helm/legacybridge/Chart.yaml
- helm/legacybridge/values-production.yaml
- helm/legacybridge/values.yaml
- k8s/configmap.yaml
- k8s/deployment-optimized.yaml
- k8s/deployment.yaml
- k8s/ingress.yaml
- k8s/monitoring-stack.yaml
- k8s/monitoring.yaml
- legacybridge/.eslintrc.json
- legacybridge/ENTERPRISE_PACKAGE/MANIFEST.json
- legacybridge/ENTERPRISE_PACKAGE/examples/other/test_dll.c
- legacybridge/ENTERPRISE_PACKAGE/examples/other/test_dll.py
- legacybridge/ENTERPRISE_PACKAGE/include/legacybridge.h
- legacybridge/components.json
- legacybridge/deployment_package/include/legacybridge.h
- legacybridge/frontend-integration-example.ts
- legacybridge/include/legacybridge.h
- legacybridge/jest.config.js
- legacybridge/next.config.ts
- legacybridge/package-lock.json
- legacybridge/package.json
- legacybridge/perf_test.c
- legacybridge/playwright.config.ts
- legacybridge/src/__tests__/security/xss.test.tsx
- legacybridge/src/app/api/monitoring/alerts/route.ts
- legacybridge/src/app/api/monitoring/alerts/slack/route.ts
- legacybridge/src/app/api/monitoring/metrics/route.ts
- legacybridge/src/app/api/monitoring/prometheus/route.ts
- legacybridge/src/app/demo/page.tsx
- legacybridge/src/app/globals-backup.css
- legacybridge/src/app/globals.css
- legacybridge/src/app/home-page.tsx
- legacybridge/src/app/layout.tsx
- legacybridge/src/app/monitoring/page.tsx
- legacybridge/src/app/page.tsx
- legacybridge/src/components/ConversionProgress.tsx
- legacybridge/src/components/DiffView.tsx
- legacybridge/src/components/DownloadManager.tsx
- legacybridge/src/components/DragDropZone.tsx
- legacybridge/src/components/ErrorBoundary.tsx
- legacybridge/src/components/ErrorDisplay.tsx
- legacybridge/src/components/ErrorLogViewer.tsx
- legacybridge/src/components/LoadingAnimation.tsx
- legacybridge/src/components/MarkdownPreview.tsx
- legacybridge/src/components/PreviewPanel.tsx
- legacybridge/src/components/SuccessAnimation.tsx
- legacybridge/src/components/SyntaxHighlighter.tsx
- legacybridge/src/components/ThemeProvider.tsx
- legacybridge/src/components/UnifiedErrorDisplay.tsx
- legacybridge/src/components/layout/Footer.tsx
- legacybridge/src/components/layout/Header.tsx
- legacybridge/src/components/layout/MainLayout.tsx
- legacybridge/src/components/monitoring/BuildProgressRing.tsx
- legacybridge/src/components/monitoring/EnhancedFunctionCallMatrix.tsx
- legacybridge/src/components/monitoring/FunctionCallMatrix.tsx
- legacybridge/src/components/monitoring/LogStreamViewer.tsx
- legacybridge/src/components/monitoring/MonitoringDashboard.tsx
- legacybridge/src/components/monitoring/PerformanceChart.tsx
- legacybridge/src/components/monitoring/SystemHealthCard.tsx
- legacybridge/src/components/monitoring/index.ts
- legacybridge/src/components/ui/alert-dialog.tsx
- legacybridge/src/components/ui/badge.tsx
- legacybridge/src/components/ui/button.tsx
- legacybridge/src/components/ui/card.tsx
- legacybridge/src/components/ui/dropdown-menu.tsx
- legacybridge/src/components/ui/input.tsx
- legacybridge/src/components/ui/label.tsx
- legacybridge/src/components/ui/progress.tsx
- legacybridge/src/components/ui/separator.tsx
- legacybridge/src/components/ui/switch.tsx
- legacybridge/src/components/ui/tabs.tsx
- legacybridge/src/components/ui/tooltip.tsx
- legacybridge/src/enterprise/admin/AdminDashboard.tsx
- legacybridge/src/enterprise/api/enterprise-api.ts
- legacybridge/src/enterprise/audit/audit-logger.ts
- legacybridge/src/enterprise/auth/authentication.ts
- legacybridge/src/enterprise/auth/rbac.ts
- legacybridge/src/enterprise/config/kubernetes-deployment.yaml
- legacybridge/src/enterprise/services/tenant-conversion-service.ts
- legacybridge/src/enterprise/tenancy/tenant-context.ts
- legacybridge/src/hooks/useDebounce.ts
- legacybridge/src/hooks/useMonitoringWebSocket.ts
- legacybridge/src/lib/download-service.ts
- legacybridge/src/lib/error-logger.ts
- legacybridge/src/lib/monitoring/alert-manager.ts
- legacybridge/src/lib/monitoring/websocket-server.ts
- legacybridge/src/lib/sanitizer.ts
- legacybridge/src/lib/stores/files.ts
- legacybridge/src/lib/tauri-api.ts
- legacybridge/src/lib/unified-error-api.ts
- legacybridge/src/lib/utils.ts
- legacybridge/src/middleware.ts
- legacybridge/src/styles/monitoring.css
- legacybridge/src/types/errors.ts
- legacybridge/src/types/index.ts
- legacybridge/src/types/monitoring.ts
- legacybridge/src-tauri/security.config.json
- legacybridge/src-tauri/tauri.conf.json
- legacybridge/test_32bit_compatibility.c
- legacybridge/test_dll.c
- legacybridge/test_dll.py
- legacybridge/tests/accessibility/accessibility-tests.spec.ts
- legacybridge/tests/accessibility/component-accessibility.test.tsx
- legacybridge/tests/chaos/chaos-engineering-tests.spec.ts
- legacybridge/tests/framework/test-automation-framework.ts
- legacybridge/tests/framework/types.ts
- legacybridge/tests/integration/file-conversion-flow.test.tsx
- legacybridge/tests/load/k6-load-test.js
- legacybridge/tests/mocks/fileMock.js
- legacybridge/tests/mocks/styleMock.js
- legacybridge/tests/performance/performance-regression.test.ts
- legacybridge/tests/run-all-tests.js
- legacybridge/tests/security/security-test-suite.ts
- legacybridge/tests/setup/global-setup.ts
- legacybridge/tests/setup/global-teardown.ts
- legacybridge/tests/setup/jest.setup.js
- legacybridge/tests/unit/components/ConversionProgress.test.tsx
- legacybridge/tests/unit/components/DragDropZone.test.tsx
- legacybridge/tests/unit/components/ErrorBoundary.test.tsx
- legacybridge/tests/unit/components/MarkdownPreview.test.tsx
- legacybridge/tests/unit/components/MonitoringDashboard.test.tsx
- legacybridge/tests/unit/stores/files.test.ts
- legacybridge/tests/visual-regression/visual-regression-tests.spec.ts
- legacybridge/tsconfig.json
- tests/load/k6-load-test.js
- validate_performance_claims.py


## Code Changes (2025-07-26 00:28:49)

### Added Files
- .github/workflows/ci.yml
- COMPREHENSIVE_SECURITY_ANALYSIS.json
- docker-compose.yml
- docs/api/interactive-explorer/index.html
- docs/troubleshooting/interactive-wizard/index.html
- helm/legacybridge/Chart.yaml
- helm/legacybridge/values-production.yaml
- helm/legacybridge/values.yaml
- k8s/configmap.yaml
- k8s/deployment-optimized.yaml
- k8s/deployment.yaml
- k8s/ingress.yaml
- k8s/monitoring-stack.yaml
- k8s/monitoring.yaml
- legacybridge/.eslintrc.json
- legacybridge/ENTERPRISE_PACKAGE/MANIFEST.json
- legacybridge/ENTERPRISE_PACKAGE/examples/other/test_dll.c
- legacybridge/ENTERPRISE_PACKAGE/examples/other/test_dll.py
- legacybridge/ENTERPRISE_PACKAGE/include/legacybridge.h
- legacybridge/components.json
- legacybridge/deployment_package/include/legacybridge.h
- legacybridge/frontend-integration-example.ts
- legacybridge/include/legacybridge.h
- legacybridge/jest.config.js
- legacybridge/next.config.ts
- legacybridge/package-lock.json
- legacybridge/package.json
- legacybridge/perf_test.c
- legacybridge/playwright.config.ts
- legacybridge/src/__tests__/security/xss.test.tsx
- legacybridge/src/app/api/monitoring/alerts/route.ts
- legacybridge/src/app/api/monitoring/alerts/slack/route.ts
- legacybridge/src/app/api/monitoring/metrics/route.ts
- legacybridge/src/app/api/monitoring/prometheus/route.ts
- legacybridge/src/app/demo/page.tsx
- legacybridge/src/app/globals-backup.css
- legacybridge/src/app/globals.css
- legacybridge/src/app/home-page.tsx
- legacybridge/src/app/layout.tsx
- legacybridge/src/app/monitoring/page.tsx
- legacybridge/src/app/page.tsx
- legacybridge/src/components/ConversionProgress.tsx
- legacybridge/src/components/DiffView.tsx
- legacybridge/src/components/DownloadManager.tsx
- legacybridge/src/components/DragDropZone.tsx
- legacybridge/src/components/ErrorBoundary.tsx
- legacybridge/src/components/ErrorDisplay.tsx
- legacybridge/src/components/ErrorLogViewer.tsx
- legacybridge/src/components/LoadingAnimation.tsx
- legacybridge/src/components/MarkdownPreview.tsx
- legacybridge/src/components/PreviewPanel.tsx
- legacybridge/src/components/SuccessAnimation.tsx
- legacybridge/src/components/SyntaxHighlighter.tsx
- legacybridge/src/components/ThemeProvider.tsx
- legacybridge/src/components/UnifiedErrorDisplay.tsx
- legacybridge/src/components/layout/Footer.tsx
- legacybridge/src/components/layout/Header.tsx
- legacybridge/src/components/layout/MainLayout.tsx
- legacybridge/src/components/monitoring/BuildProgressRing.tsx
- legacybridge/src/components/monitoring/EnhancedFunctionCallMatrix.tsx
- legacybridge/src/components/monitoring/FunctionCallMatrix.tsx
- legacybridge/src/components/monitoring/LogStreamViewer.tsx
- legacybridge/src/components/monitoring/MonitoringDashboard.tsx
- legacybridge/src/components/monitoring/PerformanceChart.tsx
- legacybridge/src/components/monitoring/SystemHealthCard.tsx
- legacybridge/src/components/monitoring/index.ts
- legacybridge/src/components/ui/alert-dialog.tsx
- legacybridge/src/components/ui/badge.tsx
- legacybridge/src/components/ui/button.tsx
- legacybridge/src/components/ui/card.tsx
- legacybridge/src/components/ui/dropdown-menu.tsx
- legacybridge/src/components/ui/input.tsx
- legacybridge/src/components/ui/label.tsx
- legacybridge/src/components/ui/progress.tsx
- legacybridge/src/components/ui/separator.tsx
- legacybridge/src/components/ui/switch.tsx
- legacybridge/src/components/ui/tabs.tsx
- legacybridge/src/components/ui/tooltip.tsx
- legacybridge/src/enterprise/admin/AdminDashboard.tsx
- legacybridge/src/enterprise/api/enterprise-api.ts
- legacybridge/src/enterprise/audit/audit-logger.ts
- legacybridge/src/enterprise/auth/authentication.ts
- legacybridge/src/enterprise/auth/rbac.ts
- legacybridge/src/enterprise/config/kubernetes-deployment.yaml
- legacybridge/src/enterprise/services/tenant-conversion-service.ts
- legacybridge/src/enterprise/tenancy/tenant-context.ts
- legacybridge/src/hooks/useDebounce.ts
- legacybridge/src/hooks/useMonitoringWebSocket.ts
- legacybridge/src/lib/download-service.ts
- legacybridge/src/lib/error-logger.ts
- legacybridge/src/lib/monitoring/alert-manager.ts
- legacybridge/src/lib/monitoring/websocket-server.ts
- legacybridge/src/lib/sanitizer.ts
- legacybridge/src/lib/stores/files.ts
- legacybridge/src/lib/tauri-api.ts
- legacybridge/src/lib/unified-error-api.ts
- legacybridge/src/lib/utils.ts
- legacybridge/src/middleware.ts
- legacybridge/src/styles/monitoring.css
- legacybridge/src/types/errors.ts
- legacybridge/src/types/index.ts
- legacybridge/src/types/monitoring.ts
- legacybridge/src-tauri/security.config.json
- legacybridge/src-tauri/tauri.conf.json
- legacybridge/test_32bit_compatibility.c
- legacybridge/test_dll.c
- legacybridge/test_dll.py
- legacybridge/tests/accessibility/accessibility-tests.spec.ts
- legacybridge/tests/accessibility/component-accessibility.test.tsx
- legacybridge/tests/chaos/chaos-engineering-tests.spec.ts
- legacybridge/tests/framework/test-automation-framework.ts
- legacybridge/tests/framework/types.ts
- legacybridge/tests/integration/file-conversion-flow.test.tsx
- legacybridge/tests/load/k6-load-test.js
- legacybridge/tests/mocks/fileMock.js
- legacybridge/tests/mocks/styleMock.js
- legacybridge/tests/performance/performance-regression.test.ts
- legacybridge/tests/run-all-tests.js
- legacybridge/tests/security/security-test-suite.ts
- legacybridge/tests/setup/global-setup.ts
- legacybridge/tests/setup/global-teardown.ts
- legacybridge/tests/setup/jest.setup.js
- legacybridge/tests/unit/components/ConversionProgress.test.tsx
- legacybridge/tests/unit/components/DragDropZone.test.tsx
- legacybridge/tests/unit/components/ErrorBoundary.test.tsx
- legacybridge/tests/unit/components/MarkdownPreview.test.tsx
- legacybridge/tests/unit/components/MonitoringDashboard.test.tsx
- legacybridge/tests/unit/stores/files.test.ts
- legacybridge/tests/visual-regression/visual-regression-tests.spec.ts
- legacybridge/tsconfig.json
- tests/load/k6-load-test.js
- validate_performance_claims.py


## Code Changes (2025-07-26 00:26:41)

### Added Files
- .github/workflows/ci.yml
- COMPREHENSIVE_SECURITY_ANALYSIS.json
- docker-compose.yml
- docs/api/interactive-explorer/index.html
- docs/troubleshooting/interactive-wizard/index.html
- helm/legacybridge/Chart.yaml
- helm/legacybridge/values-production.yaml
- helm/legacybridge/values.yaml
- k8s/configmap.yaml
- k8s/deployment-optimized.yaml
- k8s/deployment.yaml
- k8s/ingress.yaml
- k8s/monitoring-stack.yaml
- k8s/monitoring.yaml
- legacybridge/.eslintrc.json
- legacybridge/ENTERPRISE_PACKAGE/MANIFEST.json
- legacybridge/ENTERPRISE_PACKAGE/examples/other/test_dll.c
- legacybridge/ENTERPRISE_PACKAGE/examples/other/test_dll.py
- legacybridge/ENTERPRISE_PACKAGE/include/legacybridge.h
- legacybridge/components.json
- legacybridge/deployment_package/include/legacybridge.h
- legacybridge/frontend-integration-example.ts
- legacybridge/include/legacybridge.h
- legacybridge/jest.config.js
- legacybridge/next.config.ts
- legacybridge/package-lock.json
- legacybridge/package.json
- legacybridge/perf_test.c
- legacybridge/playwright.config.ts
- legacybridge/src/__tests__/security/xss.test.tsx
- legacybridge/src/app/api/monitoring/alerts/route.ts
- legacybridge/src/app/api/monitoring/alerts/slack/route.ts
- legacybridge/src/app/api/monitoring/metrics/route.ts
- legacybridge/src/app/api/monitoring/prometheus/route.ts
- legacybridge/src/app/demo/page.tsx
- legacybridge/src/app/globals-backup.css
- legacybridge/src/app/globals.css
- legacybridge/src/app/home-page.tsx
- legacybridge/src/app/layout.tsx
- legacybridge/src/app/monitoring/page.tsx
- legacybridge/src/app/page.tsx
- legacybridge/src/components/ConversionProgress.tsx
- legacybridge/src/components/DiffView.tsx
- legacybridge/src/components/DownloadManager.tsx
- legacybridge/src/components/DragDropZone.tsx
- legacybridge/src/components/ErrorBoundary.tsx
- legacybridge/src/components/ErrorDisplay.tsx
- legacybridge/src/components/ErrorLogViewer.tsx
- legacybridge/src/components/LoadingAnimation.tsx
- legacybridge/src/components/MarkdownPreview.tsx
- legacybridge/src/components/PreviewPanel.tsx
- legacybridge/src/components/SuccessAnimation.tsx
- legacybridge/src/components/SyntaxHighlighter.tsx
- legacybridge/src/components/ThemeProvider.tsx
- legacybridge/src/components/UnifiedErrorDisplay.tsx
- legacybridge/src/components/layout/Footer.tsx
- legacybridge/src/components/layout/Header.tsx
- legacybridge/src/components/layout/MainLayout.tsx
- legacybridge/src/components/monitoring/BuildProgressRing.tsx
- legacybridge/src/components/monitoring/EnhancedFunctionCallMatrix.tsx
- legacybridge/src/components/monitoring/FunctionCallMatrix.tsx
- legacybridge/src/components/monitoring/LogStreamViewer.tsx
- legacybridge/src/components/monitoring/MonitoringDashboard.tsx
- legacybridge/src/components/monitoring/PerformanceChart.tsx
- legacybridge/src/components/monitoring/SystemHealthCard.tsx
- legacybridge/src/components/monitoring/index.ts
- legacybridge/src/components/ui/alert-dialog.tsx
- legacybridge/src/components/ui/badge.tsx
- legacybridge/src/components/ui/button.tsx
- legacybridge/src/components/ui/card.tsx
- legacybridge/src/components/ui/dropdown-menu.tsx
- legacybridge/src/components/ui/input.tsx
- legacybridge/src/components/ui/label.tsx
- legacybridge/src/components/ui/progress.tsx
- legacybridge/src/components/ui/separator.tsx
- legacybridge/src/components/ui/switch.tsx
- legacybridge/src/components/ui/tabs.tsx
- legacybridge/src/components/ui/tooltip.tsx
- legacybridge/src/enterprise/admin/AdminDashboard.tsx
- legacybridge/src/enterprise/api/enterprise-api.ts
- legacybridge/src/enterprise/audit/audit-logger.ts
- legacybridge/src/enterprise/auth/authentication.ts
- legacybridge/src/enterprise/auth/rbac.ts
- legacybridge/src/enterprise/config/kubernetes-deployment.yaml
- legacybridge/src/enterprise/services/tenant-conversion-service.ts
- legacybridge/src/enterprise/tenancy/tenant-context.ts
- legacybridge/src/hooks/useDebounce.ts
- legacybridge/src/hooks/useMonitoringWebSocket.ts
- legacybridge/src/lib/download-service.ts
- legacybridge/src/lib/error-logger.ts
- legacybridge/src/lib/monitoring/alert-manager.ts
- legacybridge/src/lib/monitoring/websocket-server.ts
- legacybridge/src/lib/sanitizer.ts
- legacybridge/src/lib/stores/files.ts
- legacybridge/src/lib/tauri-api.ts
- legacybridge/src/lib/unified-error-api.ts
- legacybridge/src/lib/utils.ts
- legacybridge/src/middleware.ts
- legacybridge/src/styles/monitoring.css
- legacybridge/src/types/errors.ts
- legacybridge/src/types/index.ts
- legacybridge/src/types/monitoring.ts
- legacybridge/src-tauri/security.config.json
- legacybridge/src-tauri/tauri.conf.json
- legacybridge/test_32bit_compatibility.c
- legacybridge/test_dll.c
- legacybridge/test_dll.py
- legacybridge/tests/accessibility/accessibility-tests.spec.ts
- legacybridge/tests/accessibility/component-accessibility.test.tsx
- legacybridge/tests/chaos/chaos-engineering-tests.spec.ts
- legacybridge/tests/framework/test-automation-framework.ts
- legacybridge/tests/framework/types.ts
- legacybridge/tests/integration/file-conversion-flow.test.tsx
- legacybridge/tests/load/k6-load-test.js
- legacybridge/tests/mocks/fileMock.js
- legacybridge/tests/mocks/styleMock.js
- legacybridge/tests/performance/performance-regression.test.ts
- legacybridge/tests/run-all-tests.js
- legacybridge/tests/security/security-test-suite.ts
- legacybridge/tests/setup/global-setup.ts
- legacybridge/tests/setup/global-teardown.ts
- legacybridge/tests/setup/jest.setup.js
- legacybridge/tests/unit/components/ConversionProgress.test.tsx
- legacybridge/tests/unit/components/DragDropZone.test.tsx
- legacybridge/tests/unit/components/ErrorBoundary.test.tsx
- legacybridge/tests/unit/components/MarkdownPreview.test.tsx
- legacybridge/tests/unit/components/MonitoringDashboard.test.tsx
- legacybridge/tests/unit/stores/files.test.ts
- legacybridge/tests/visual-regression/visual-regression-tests.spec.ts
- legacybridge/tsconfig.json
- tests/load/k6-load-test.js
- validate_performance_claims.py


## Code Changes (2025-07-26 00:24:12)

### Added Files
- .github/workflows/ci.yml
- COMPREHENSIVE_SECURITY_ANALYSIS.json
- docker-compose.yml
- docs/api/interactive-explorer/index.html
- docs/troubleshooting/interactive-wizard/index.html
- helm/legacybridge/Chart.yaml
- helm/legacybridge/values-production.yaml
- helm/legacybridge/values.yaml
- k8s/configmap.yaml
- k8s/deployment-optimized.yaml
- k8s/deployment.yaml
- k8s/ingress.yaml
- k8s/monitoring-stack.yaml
- k8s/monitoring.yaml
- legacybridge/.eslintrc.json
- legacybridge/ENTERPRISE_PACKAGE/MANIFEST.json
- legacybridge/ENTERPRISE_PACKAGE/examples/other/test_dll.c
- legacybridge/ENTERPRISE_PACKAGE/examples/other/test_dll.py
- legacybridge/ENTERPRISE_PACKAGE/include/legacybridge.h
- legacybridge/components.json
- legacybridge/deployment_package/include/legacybridge.h
- legacybridge/frontend-integration-example.ts
- legacybridge/include/legacybridge.h
- legacybridge/jest.config.js
- legacybridge/next.config.ts
- legacybridge/package-lock.json
- legacybridge/package.json
- legacybridge/perf_test.c
- legacybridge/playwright.config.ts
- legacybridge/src/__tests__/security/xss.test.tsx
- legacybridge/src/app/api/monitoring/alerts/route.ts
- legacybridge/src/app/api/monitoring/alerts/slack/route.ts
- legacybridge/src/app/api/monitoring/metrics/route.ts
- legacybridge/src/app/api/monitoring/prometheus/route.ts
- legacybridge/src/app/demo/page.tsx
- legacybridge/src/app/globals-backup.css
- legacybridge/src/app/globals.css
- legacybridge/src/app/home-page.tsx
- legacybridge/src/app/layout.tsx
- legacybridge/src/app/monitoring/page.tsx
- legacybridge/src/app/page.tsx
- legacybridge/src/components/ConversionProgress.tsx
- legacybridge/src/components/DiffView.tsx
- legacybridge/src/components/DownloadManager.tsx
- legacybridge/src/components/DragDropZone.tsx
- legacybridge/src/components/ErrorBoundary.tsx
- legacybridge/src/components/ErrorDisplay.tsx
- legacybridge/src/components/ErrorLogViewer.tsx
- legacybridge/src/components/LoadingAnimation.tsx
- legacybridge/src/components/MarkdownPreview.tsx
- legacybridge/src/components/PreviewPanel.tsx
- legacybridge/src/components/SuccessAnimation.tsx
- legacybridge/src/components/SyntaxHighlighter.tsx
- legacybridge/src/components/ThemeProvider.tsx
- legacybridge/src/components/UnifiedErrorDisplay.tsx
- legacybridge/src/components/layout/Footer.tsx
- legacybridge/src/components/layout/Header.tsx
- legacybridge/src/components/layout/MainLayout.tsx
- legacybridge/src/components/monitoring/BuildProgressRing.tsx
- legacybridge/src/components/monitoring/EnhancedFunctionCallMatrix.tsx
- legacybridge/src/components/monitoring/FunctionCallMatrix.tsx
- legacybridge/src/components/monitoring/LogStreamViewer.tsx
- legacybridge/src/components/monitoring/MonitoringDashboard.tsx
- legacybridge/src/components/monitoring/PerformanceChart.tsx
- legacybridge/src/components/monitoring/SystemHealthCard.tsx
- legacybridge/src/components/monitoring/index.ts
- legacybridge/src/components/ui/alert-dialog.tsx
- legacybridge/src/components/ui/badge.tsx
- legacybridge/src/components/ui/button.tsx
- legacybridge/src/components/ui/card.tsx
- legacybridge/src/components/ui/dropdown-menu.tsx
- legacybridge/src/components/ui/input.tsx
- legacybridge/src/components/ui/label.tsx
- legacybridge/src/components/ui/progress.tsx
- legacybridge/src/components/ui/separator.tsx
- legacybridge/src/components/ui/switch.tsx
- legacybridge/src/components/ui/tabs.tsx
- legacybridge/src/components/ui/tooltip.tsx
- legacybridge/src/enterprise/admin/AdminDashboard.tsx
- legacybridge/src/enterprise/api/enterprise-api.ts
- legacybridge/src/enterprise/audit/audit-logger.ts
- legacybridge/src/enterprise/auth/authentication.ts
- legacybridge/src/enterprise/auth/rbac.ts
- legacybridge/src/enterprise/config/kubernetes-deployment.yaml
- legacybridge/src/enterprise/services/tenant-conversion-service.ts
- legacybridge/src/enterprise/tenancy/tenant-context.ts
- legacybridge/src/hooks/useDebounce.ts
- legacybridge/src/hooks/useMonitoringWebSocket.ts
- legacybridge/src/lib/download-service.ts
- legacybridge/src/lib/error-logger.ts
- legacybridge/src/lib/monitoring/alert-manager.ts
- legacybridge/src/lib/monitoring/websocket-server.ts
- legacybridge/src/lib/sanitizer.ts
- legacybridge/src/lib/stores/files.ts
- legacybridge/src/lib/tauri-api.ts
- legacybridge/src/lib/unified-error-api.ts
- legacybridge/src/lib/utils.ts
- legacybridge/src/middleware.ts
- legacybridge/src/styles/monitoring.css
- legacybridge/src/types/errors.ts
- legacybridge/src/types/index.ts
- legacybridge/src/types/monitoring.ts
- legacybridge/src-tauri/security.config.json
- legacybridge/src-tauri/tauri.conf.json
- legacybridge/test_32bit_compatibility.c
- legacybridge/test_dll.c
- legacybridge/test_dll.py
- legacybridge/tests/accessibility/accessibility-tests.spec.ts
- legacybridge/tests/accessibility/component-accessibility.test.tsx
- legacybridge/tests/chaos/chaos-engineering-tests.spec.ts
- legacybridge/tests/framework/test-automation-framework.ts
- legacybridge/tests/framework/types.ts
- legacybridge/tests/integration/file-conversion-flow.test.tsx
- legacybridge/tests/load/k6-load-test.js
- legacybridge/tests/mocks/fileMock.js
- legacybridge/tests/mocks/styleMock.js
- legacybridge/tests/performance/performance-regression.test.ts
- legacybridge/tests/run-all-tests.js
- legacybridge/tests/security/security-test-suite.ts
- legacybridge/tests/setup/global-setup.ts
- legacybridge/tests/setup/global-teardown.ts
- legacybridge/tests/setup/jest.setup.js
- legacybridge/tests/unit/components/ConversionProgress.test.tsx
- legacybridge/tests/unit/components/DragDropZone.test.tsx
- legacybridge/tests/unit/components/ErrorBoundary.test.tsx
- legacybridge/tests/unit/components/MarkdownPreview.test.tsx
- legacybridge/tests/unit/components/MonitoringDashboard.test.tsx
- legacybridge/tests/unit/stores/files.test.ts
- legacybridge/tests/visual-regression/visual-regression-tests.spec.ts
- legacybridge/tsconfig.json
- tests/load/k6-load-test.js
- validate_performance_claims.py


## Code Changes (2025-07-26 00:17:19)

### Added Files
- .github/workflows/ci.yml
- COMPREHENSIVE_SECURITY_ANALYSIS.json
- docker-compose.yml
- docs/api/interactive-explorer/index.html
- docs/troubleshooting/interactive-wizard/index.html
- helm/legacybridge/Chart.yaml
- helm/legacybridge/values-production.yaml
- helm/legacybridge/values.yaml
- k8s/configmap.yaml
- k8s/deployment-optimized.yaml
- k8s/deployment.yaml
- k8s/ingress.yaml
- k8s/monitoring-stack.yaml
- k8s/monitoring.yaml
- legacybridge/.eslintrc.json
- legacybridge/ENTERPRISE_PACKAGE/MANIFEST.json
- legacybridge/ENTERPRISE_PACKAGE/examples/other/test_dll.c
- legacybridge/ENTERPRISE_PACKAGE/examples/other/test_dll.py
- legacybridge/ENTERPRISE_PACKAGE/include/legacybridge.h
- legacybridge/components.json
- legacybridge/deployment_package/include/legacybridge.h
- legacybridge/frontend-integration-example.ts
- legacybridge/include/legacybridge.h
- legacybridge/jest.config.js
- legacybridge/next.config.ts
- legacybridge/package-lock.json
- legacybridge/package.json
- legacybridge/perf_test.c
- legacybridge/playwright.config.ts
- legacybridge/src/__tests__/security/xss.test.tsx
- legacybridge/src/app/api/monitoring/alerts/route.ts
- legacybridge/src/app/api/monitoring/alerts/slack/route.ts
- legacybridge/src/app/api/monitoring/metrics/route.ts
- legacybridge/src/app/api/monitoring/prometheus/route.ts
- legacybridge/src/app/demo/page.tsx
- legacybridge/src/app/globals-backup.css
- legacybridge/src/app/globals.css
- legacybridge/src/app/home-page.tsx
- legacybridge/src/app/layout.tsx
- legacybridge/src/app/monitoring/page.tsx
- legacybridge/src/app/page.tsx
- legacybridge/src/components/ConversionProgress.tsx
- legacybridge/src/components/DiffView.tsx
- legacybridge/src/components/DownloadManager.tsx
- legacybridge/src/components/DragDropZone.tsx
- legacybridge/src/components/ErrorBoundary.tsx
- legacybridge/src/components/ErrorDisplay.tsx
- legacybridge/src/components/ErrorLogViewer.tsx
- legacybridge/src/components/LoadingAnimation.tsx
- legacybridge/src/components/MarkdownPreview.tsx
- legacybridge/src/components/PreviewPanel.tsx
- legacybridge/src/components/SuccessAnimation.tsx
- legacybridge/src/components/SyntaxHighlighter.tsx
- legacybridge/src/components/ThemeProvider.tsx
- legacybridge/src/components/UnifiedErrorDisplay.tsx
- legacybridge/src/components/layout/Footer.tsx
- legacybridge/src/components/layout/Header.tsx
- legacybridge/src/components/layout/MainLayout.tsx
- legacybridge/src/components/monitoring/BuildProgressRing.tsx
- legacybridge/src/components/monitoring/EnhancedFunctionCallMatrix.tsx
- legacybridge/src/components/monitoring/FunctionCallMatrix.tsx
- legacybridge/src/components/monitoring/LogStreamViewer.tsx
- legacybridge/src/components/monitoring/MonitoringDashboard.tsx
- legacybridge/src/components/monitoring/PerformanceChart.tsx
- legacybridge/src/components/monitoring/SystemHealthCard.tsx
- legacybridge/src/components/monitoring/index.ts
- legacybridge/src/components/ui/alert-dialog.tsx
- legacybridge/src/components/ui/badge.tsx
- legacybridge/src/components/ui/button.tsx
- legacybridge/src/components/ui/card.tsx
- legacybridge/src/components/ui/dropdown-menu.tsx
- legacybridge/src/components/ui/input.tsx
- legacybridge/src/components/ui/label.tsx
- legacybridge/src/components/ui/progress.tsx
- legacybridge/src/components/ui/separator.tsx
- legacybridge/src/components/ui/switch.tsx
- legacybridge/src/components/ui/tabs.tsx
- legacybridge/src/components/ui/tooltip.tsx
- legacybridge/src/enterprise/admin/AdminDashboard.tsx
- legacybridge/src/enterprise/api/enterprise-api.ts
- legacybridge/src/enterprise/audit/audit-logger.ts
- legacybridge/src/enterprise/auth/authentication.ts
- legacybridge/src/enterprise/auth/rbac.ts
- legacybridge/src/enterprise/config/kubernetes-deployment.yaml
- legacybridge/src/enterprise/services/tenant-conversion-service.ts
- legacybridge/src/enterprise/tenancy/tenant-context.ts
- legacybridge/src/hooks/useDebounce.ts
- legacybridge/src/hooks/useMonitoringWebSocket.ts
- legacybridge/src/lib/download-service.ts
- legacybridge/src/lib/error-logger.ts
- legacybridge/src/lib/monitoring/alert-manager.ts
- legacybridge/src/lib/monitoring/websocket-server.ts
- legacybridge/src/lib/sanitizer.ts
- legacybridge/src/lib/stores/files.ts
- legacybridge/src/lib/tauri-api.ts
- legacybridge/src/lib/unified-error-api.ts
- legacybridge/src/lib/utils.ts
- legacybridge/src/middleware.ts
- legacybridge/src/styles/monitoring.css
- legacybridge/src/types/errors.ts
- legacybridge/src/types/index.ts
- legacybridge/src/types/monitoring.ts
- legacybridge/src-tauri/security.config.json
- legacybridge/src-tauri/tauri.conf.json
- legacybridge/test_32bit_compatibility.c
- legacybridge/test_dll.c
- legacybridge/test_dll.py
- legacybridge/tests/accessibility/accessibility-tests.spec.ts
- legacybridge/tests/accessibility/component-accessibility.test.tsx
- legacybridge/tests/chaos/chaos-engineering-tests.spec.ts
- legacybridge/tests/framework/test-automation-framework.ts
- legacybridge/tests/framework/types.ts
- legacybridge/tests/integration/file-conversion-flow.test.tsx
- legacybridge/tests/load/k6-load-test.js
- legacybridge/tests/mocks/fileMock.js
- legacybridge/tests/mocks/styleMock.js
- legacybridge/tests/performance/performance-regression.test.ts
- legacybridge/tests/run-all-tests.js
- legacybridge/tests/security/security-test-suite.ts
- legacybridge/tests/setup/global-setup.ts
- legacybridge/tests/setup/global-teardown.ts
- legacybridge/tests/setup/jest.setup.js
- legacybridge/tests/unit/components/ConversionProgress.test.tsx
- legacybridge/tests/unit/components/DragDropZone.test.tsx
- legacybridge/tests/unit/components/ErrorBoundary.test.tsx
- legacybridge/tests/unit/components/MarkdownPreview.test.tsx
- legacybridge/tests/unit/components/MonitoringDashboard.test.tsx
- legacybridge/tests/unit/stores/files.test.ts
- legacybridge/tests/visual-regression/visual-regression-tests.spec.ts
- legacybridge/tsconfig.json
- tests/load/k6-load-test.js
- validate_performance_claims.py


## Code Changes (2025-07-26 00:16:14)

### Added Files
- .github/workflows/ci.yml
- COMPREHENSIVE_SECURITY_ANALYSIS.json
- docker-compose.yml
- docs/api/interactive-explorer/index.html
- docs/troubleshooting/interactive-wizard/index.html
- helm/legacybridge/Chart.yaml
- helm/legacybridge/values-production.yaml
- helm/legacybridge/values.yaml
- k8s/configmap.yaml
- k8s/deployment-optimized.yaml
- k8s/deployment.yaml
- k8s/ingress.yaml
- k8s/monitoring-stack.yaml
- k8s/monitoring.yaml
- legacybridge/.eslintrc.json
- legacybridge/ENTERPRISE_PACKAGE/MANIFEST.json
- legacybridge/ENTERPRISE_PACKAGE/examples/other/test_dll.c
- legacybridge/ENTERPRISE_PACKAGE/examples/other/test_dll.py
- legacybridge/ENTERPRISE_PACKAGE/include/legacybridge.h
- legacybridge/components.json
- legacybridge/deployment_package/include/legacybridge.h
- legacybridge/frontend-integration-example.ts
- legacybridge/include/legacybridge.h
- legacybridge/jest.config.js
- legacybridge/next.config.ts
- legacybridge/package-lock.json
- legacybridge/package.json
- legacybridge/perf_test.c
- legacybridge/playwright.config.ts
- legacybridge/src/__tests__/security/xss.test.tsx
- legacybridge/src/app/api/monitoring/alerts/route.ts
- legacybridge/src/app/api/monitoring/alerts/slack/route.ts
- legacybridge/src/app/api/monitoring/metrics/route.ts
- legacybridge/src/app/api/monitoring/prometheus/route.ts
- legacybridge/src/app/demo/page.tsx
- legacybridge/src/app/globals-backup.css
- legacybridge/src/app/globals.css
- legacybridge/src/app/home-page.tsx
- legacybridge/src/app/layout.tsx
- legacybridge/src/app/monitoring/page.tsx
- legacybridge/src/app/page.tsx
- legacybridge/src/components/ConversionProgress.tsx
- legacybridge/src/components/DiffView.tsx
- legacybridge/src/components/DownloadManager.tsx
- legacybridge/src/components/DragDropZone.tsx
- legacybridge/src/components/ErrorBoundary.tsx
- legacybridge/src/components/ErrorDisplay.tsx
- legacybridge/src/components/ErrorLogViewer.tsx
- legacybridge/src/components/LoadingAnimation.tsx
- legacybridge/src/components/MarkdownPreview.tsx
- legacybridge/src/components/PreviewPanel.tsx
- legacybridge/src/components/SuccessAnimation.tsx
- legacybridge/src/components/SyntaxHighlighter.tsx
- legacybridge/src/components/ThemeProvider.tsx
- legacybridge/src/components/UnifiedErrorDisplay.tsx
- legacybridge/src/components/layout/Footer.tsx
- legacybridge/src/components/layout/Header.tsx
- legacybridge/src/components/layout/MainLayout.tsx
- legacybridge/src/components/monitoring/BuildProgressRing.tsx
- legacybridge/src/components/monitoring/EnhancedFunctionCallMatrix.tsx
- legacybridge/src/components/monitoring/FunctionCallMatrix.tsx
- legacybridge/src/components/monitoring/LogStreamViewer.tsx
- legacybridge/src/components/monitoring/MonitoringDashboard.tsx
- legacybridge/src/components/monitoring/PerformanceChart.tsx
- legacybridge/src/components/monitoring/SystemHealthCard.tsx
- legacybridge/src/components/monitoring/index.ts
- legacybridge/src/components/ui/alert-dialog.tsx
- legacybridge/src/components/ui/badge.tsx
- legacybridge/src/components/ui/button.tsx
- legacybridge/src/components/ui/card.tsx
- legacybridge/src/components/ui/dropdown-menu.tsx
- legacybridge/src/components/ui/input.tsx
- legacybridge/src/components/ui/label.tsx
- legacybridge/src/components/ui/progress.tsx
- legacybridge/src/components/ui/separator.tsx
- legacybridge/src/components/ui/switch.tsx
- legacybridge/src/components/ui/tabs.tsx
- legacybridge/src/components/ui/tooltip.tsx
- legacybridge/src/enterprise/admin/AdminDashboard.tsx
- legacybridge/src/enterprise/api/enterprise-api.ts
- legacybridge/src/enterprise/audit/audit-logger.ts
- legacybridge/src/enterprise/auth/authentication.ts
- legacybridge/src/enterprise/auth/rbac.ts
- legacybridge/src/enterprise/config/kubernetes-deployment.yaml
- legacybridge/src/enterprise/services/tenant-conversion-service.ts
- legacybridge/src/enterprise/tenancy/tenant-context.ts
- legacybridge/src/hooks/useDebounce.ts
- legacybridge/src/hooks/useMonitoringWebSocket.ts
- legacybridge/src/lib/download-service.ts
- legacybridge/src/lib/error-logger.ts
- legacybridge/src/lib/monitoring/alert-manager.ts
- legacybridge/src/lib/monitoring/websocket-server.ts
- legacybridge/src/lib/sanitizer.ts
- legacybridge/src/lib/stores/files.ts
- legacybridge/src/lib/tauri-api.ts
- legacybridge/src/lib/unified-error-api.ts
- legacybridge/src/lib/utils.ts
- legacybridge/src/middleware.ts
- legacybridge/src/styles/monitoring.css
- legacybridge/src/types/errors.ts
- legacybridge/src/types/index.ts
- legacybridge/src/types/monitoring.ts
- legacybridge/src-tauri/security.config.json
- legacybridge/src-tauri/tauri.conf.json
- legacybridge/test_32bit_compatibility.c
- legacybridge/test_dll.c
- legacybridge/test_dll.py
- legacybridge/tests/accessibility/accessibility-tests.spec.ts
- legacybridge/tests/accessibility/component-accessibility.test.tsx
- legacybridge/tests/chaos/chaos-engineering-tests.spec.ts
- legacybridge/tests/framework/test-automation-framework.ts
- legacybridge/tests/framework/types.ts
- legacybridge/tests/integration/file-conversion-flow.test.tsx
- legacybridge/tests/load/k6-load-test.js
- legacybridge/tests/mocks/fileMock.js
- legacybridge/tests/mocks/styleMock.js
- legacybridge/tests/performance/performance-regression.test.ts
- legacybridge/tests/run-all-tests.js
- legacybridge/tests/security/security-test-suite.ts
- legacybridge/tests/setup/global-setup.ts
- legacybridge/tests/setup/global-teardown.ts
- legacybridge/tests/setup/jest.setup.js
- legacybridge/tests/unit/components/ConversionProgress.test.tsx
- legacybridge/tests/unit/components/DragDropZone.test.tsx
- legacybridge/tests/unit/components/ErrorBoundary.test.tsx
- legacybridge/tests/unit/components/MarkdownPreview.test.tsx
- legacybridge/tests/unit/components/MonitoringDashboard.test.tsx
- legacybridge/tests/unit/stores/files.test.ts
- legacybridge/tests/visual-regression/visual-regression-tests.spec.ts
- legacybridge/tsconfig.json
- tests/load/k6-load-test.js
- validate_performance_claims.py


## Code Changes (2025-07-26 00:12:02)

### Added Files
- .github/workflows/ci.yml
- COMPREHENSIVE_SECURITY_ANALYSIS.json
- docker-compose.yml
- docs/api/interactive-explorer/index.html
- docs/troubleshooting/interactive-wizard/index.html
- helm/legacybridge/Chart.yaml
- helm/legacybridge/values-production.yaml
- helm/legacybridge/values.yaml
- k8s/configmap.yaml
- k8s/deployment-optimized.yaml
- k8s/deployment.yaml
- k8s/ingress.yaml
- k8s/monitoring-stack.yaml
- k8s/monitoring.yaml
- legacybridge/.eslintrc.json
- legacybridge/ENTERPRISE_PACKAGE/MANIFEST.json
- legacybridge/ENTERPRISE_PACKAGE/examples/other/test_dll.c
- legacybridge/ENTERPRISE_PACKAGE/examples/other/test_dll.py
- legacybridge/ENTERPRISE_PACKAGE/include/legacybridge.h
- legacybridge/components.json
- legacybridge/deployment_package/include/legacybridge.h
- legacybridge/frontend-integration-example.ts
- legacybridge/include/legacybridge.h
- legacybridge/jest.config.js
- legacybridge/next.config.ts
- legacybridge/package-lock.json
- legacybridge/package.json
- legacybridge/perf_test.c
- legacybridge/playwright.config.ts
- legacybridge/src/__tests__/security/xss.test.tsx
- legacybridge/src/app/api/monitoring/alerts/route.ts
- legacybridge/src/app/api/monitoring/alerts/slack/route.ts
- legacybridge/src/app/api/monitoring/metrics/route.ts
- legacybridge/src/app/api/monitoring/prometheus/route.ts
- legacybridge/src/app/demo/page.tsx
- legacybridge/src/app/globals-backup.css
- legacybridge/src/app/globals.css
- legacybridge/src/app/home-page.tsx
- legacybridge/src/app/layout.tsx
- legacybridge/src/app/monitoring/page.tsx
- legacybridge/src/app/page.tsx
- legacybridge/src/components/ConversionProgress.tsx
- legacybridge/src/components/DiffView.tsx
- legacybridge/src/components/DownloadManager.tsx
- legacybridge/src/components/DragDropZone.tsx
- legacybridge/src/components/ErrorBoundary.tsx
- legacybridge/src/components/ErrorDisplay.tsx
- legacybridge/src/components/ErrorLogViewer.tsx
- legacybridge/src/components/LoadingAnimation.tsx
- legacybridge/src/components/MarkdownPreview.tsx
- legacybridge/src/components/PreviewPanel.tsx
- legacybridge/src/components/SuccessAnimation.tsx
- legacybridge/src/components/SyntaxHighlighter.tsx
- legacybridge/src/components/ThemeProvider.tsx
- legacybridge/src/components/UnifiedErrorDisplay.tsx
- legacybridge/src/components/layout/Footer.tsx
- legacybridge/src/components/layout/Header.tsx
- legacybridge/src/components/layout/MainLayout.tsx
- legacybridge/src/components/monitoring/BuildProgressRing.tsx
- legacybridge/src/components/monitoring/EnhancedFunctionCallMatrix.tsx
- legacybridge/src/components/monitoring/FunctionCallMatrix.tsx
- legacybridge/src/components/monitoring/LogStreamViewer.tsx
- legacybridge/src/components/monitoring/MonitoringDashboard.tsx
- legacybridge/src/components/monitoring/PerformanceChart.tsx
- legacybridge/src/components/monitoring/SystemHealthCard.tsx
- legacybridge/src/components/monitoring/index.ts
- legacybridge/src/components/ui/alert-dialog.tsx
- legacybridge/src/components/ui/badge.tsx
- legacybridge/src/components/ui/button.tsx
- legacybridge/src/components/ui/card.tsx
- legacybridge/src/components/ui/dropdown-menu.tsx
- legacybridge/src/components/ui/input.tsx
- legacybridge/src/components/ui/label.tsx
- legacybridge/src/components/ui/progress.tsx
- legacybridge/src/components/ui/separator.tsx
- legacybridge/src/components/ui/switch.tsx
- legacybridge/src/components/ui/tabs.tsx
- legacybridge/src/components/ui/tooltip.tsx
- legacybridge/src/enterprise/admin/AdminDashboard.tsx
- legacybridge/src/enterprise/api/enterprise-api.ts
- legacybridge/src/enterprise/audit/audit-logger.ts
- legacybridge/src/enterprise/auth/authentication.ts
- legacybridge/src/enterprise/auth/rbac.ts
- legacybridge/src/enterprise/config/kubernetes-deployment.yaml
- legacybridge/src/enterprise/services/tenant-conversion-service.ts
- legacybridge/src/enterprise/tenancy/tenant-context.ts
- legacybridge/src/hooks/useDebounce.ts
- legacybridge/src/hooks/useMonitoringWebSocket.ts
- legacybridge/src/lib/download-service.ts
- legacybridge/src/lib/error-logger.ts
- legacybridge/src/lib/monitoring/alert-manager.ts
- legacybridge/src/lib/monitoring/websocket-server.ts
- legacybridge/src/lib/sanitizer.ts
- legacybridge/src/lib/stores/files.ts
- legacybridge/src/lib/tauri-api.ts
- legacybridge/src/lib/unified-error-api.ts
- legacybridge/src/lib/utils.ts
- legacybridge/src/middleware.ts
- legacybridge/src/styles/monitoring.css
- legacybridge/src/types/errors.ts
- legacybridge/src/types/index.ts
- legacybridge/src/types/monitoring.ts
- legacybridge/src-tauri/security.config.json
- legacybridge/src-tauri/tauri.conf.json
- legacybridge/test_32bit_compatibility.c
- legacybridge/test_dll.c
- legacybridge/test_dll.py
- legacybridge/tests/accessibility/accessibility-tests.spec.ts
- legacybridge/tests/accessibility/component-accessibility.test.tsx
- legacybridge/tests/chaos/chaos-engineering-tests.spec.ts
- legacybridge/tests/framework/test-automation-framework.ts
- legacybridge/tests/framework/types.ts
- legacybridge/tests/integration/file-conversion-flow.test.tsx
- legacybridge/tests/load/k6-load-test.js
- legacybridge/tests/mocks/fileMock.js
- legacybridge/tests/mocks/styleMock.js
- legacybridge/tests/performance/performance-regression.test.ts
- legacybridge/tests/run-all-tests.js
- legacybridge/tests/security/security-test-suite.ts
- legacybridge/tests/setup/global-setup.ts
- legacybridge/tests/setup/global-teardown.ts
- legacybridge/tests/setup/jest.setup.js
- legacybridge/tests/unit/components/ConversionProgress.test.tsx
- legacybridge/tests/unit/components/DragDropZone.test.tsx
- legacybridge/tests/unit/components/ErrorBoundary.test.tsx
- legacybridge/tests/unit/components/MarkdownPreview.test.tsx
- legacybridge/tests/unit/components/MonitoringDashboard.test.tsx
- legacybridge/tests/unit/stores/files.test.ts
- legacybridge/tests/visual-regression/visual-regression-tests.spec.ts
- legacybridge/tsconfig.json
- tests/load/k6-load-test.js
- validate_performance_claims.py


## Code Changes (2025-07-26 00:10:10)

### Added Files
- .github/workflows/ci.yml
- COMPREHENSIVE_SECURITY_ANALYSIS.json
- docker-compose.yml
- docs/api/interactive-explorer/index.html
- docs/troubleshooting/interactive-wizard/index.html
- helm/legacybridge/Chart.yaml
- helm/legacybridge/values-production.yaml
- helm/legacybridge/values.yaml
- k8s/configmap.yaml
- k8s/deployment-optimized.yaml
- k8s/deployment.yaml
- k8s/ingress.yaml
- k8s/monitoring-stack.yaml
- k8s/monitoring.yaml
- legacybridge/.eslintrc.json
- legacybridge/ENTERPRISE_PACKAGE/MANIFEST.json
- legacybridge/ENTERPRISE_PACKAGE/examples/other/test_dll.c
- legacybridge/ENTERPRISE_PACKAGE/examples/other/test_dll.py
- legacybridge/ENTERPRISE_PACKAGE/include/legacybridge.h
- legacybridge/components.json
- legacybridge/deployment_package/include/legacybridge.h
- legacybridge/frontend-integration-example.ts
- legacybridge/include/legacybridge.h
- legacybridge/jest.config.js
- legacybridge/next.config.ts
- legacybridge/package-lock.json
- legacybridge/package.json
- legacybridge/perf_test.c
- legacybridge/playwright.config.ts
- legacybridge/src/__tests__/security/xss.test.tsx
- legacybridge/src/app/api/monitoring/alerts/route.ts
- legacybridge/src/app/api/monitoring/alerts/slack/route.ts
- legacybridge/src/app/api/monitoring/metrics/route.ts
- legacybridge/src/app/api/monitoring/prometheus/route.ts
- legacybridge/src/app/demo/page.tsx
- legacybridge/src/app/globals-backup.css
- legacybridge/src/app/globals.css
- legacybridge/src/app/home-page.tsx
- legacybridge/src/app/layout.tsx
- legacybridge/src/app/monitoring/page.tsx
- legacybridge/src/app/page.tsx
- legacybridge/src/components/ConversionProgress.tsx
- legacybridge/src/components/DiffView.tsx
- legacybridge/src/components/DownloadManager.tsx
- legacybridge/src/components/DragDropZone.tsx
- legacybridge/src/components/ErrorBoundary.tsx
- legacybridge/src/components/ErrorDisplay.tsx
- legacybridge/src/components/ErrorLogViewer.tsx
- legacybridge/src/components/LoadingAnimation.tsx
- legacybridge/src/components/MarkdownPreview.tsx
- legacybridge/src/components/PreviewPanel.tsx
- legacybridge/src/components/SuccessAnimation.tsx
- legacybridge/src/components/SyntaxHighlighter.tsx
- legacybridge/src/components/ThemeProvider.tsx
- legacybridge/src/components/UnifiedErrorDisplay.tsx
- legacybridge/src/components/layout/Footer.tsx
- legacybridge/src/components/layout/Header.tsx
- legacybridge/src/components/layout/MainLayout.tsx
- legacybridge/src/components/monitoring/BuildProgressRing.tsx
- legacybridge/src/components/monitoring/EnhancedFunctionCallMatrix.tsx
- legacybridge/src/components/monitoring/FunctionCallMatrix.tsx
- legacybridge/src/components/monitoring/LogStreamViewer.tsx
- legacybridge/src/components/monitoring/MonitoringDashboard.tsx
- legacybridge/src/components/monitoring/PerformanceChart.tsx
- legacybridge/src/components/monitoring/SystemHealthCard.tsx
- legacybridge/src/components/monitoring/index.ts
- legacybridge/src/components/ui/alert-dialog.tsx
- legacybridge/src/components/ui/badge.tsx
- legacybridge/src/components/ui/button.tsx
- legacybridge/src/components/ui/card.tsx
- legacybridge/src/components/ui/dropdown-menu.tsx
- legacybridge/src/components/ui/input.tsx
- legacybridge/src/components/ui/label.tsx
- legacybridge/src/components/ui/progress.tsx
- legacybridge/src/components/ui/separator.tsx
- legacybridge/src/components/ui/switch.tsx
- legacybridge/src/components/ui/tabs.tsx
- legacybridge/src/components/ui/tooltip.tsx
- legacybridge/src/enterprise/admin/AdminDashboard.tsx
- legacybridge/src/enterprise/api/enterprise-api.ts
- legacybridge/src/enterprise/audit/audit-logger.ts
- legacybridge/src/enterprise/auth/authentication.ts
- legacybridge/src/enterprise/auth/rbac.ts
- legacybridge/src/enterprise/config/kubernetes-deployment.yaml
- legacybridge/src/enterprise/services/tenant-conversion-service.ts
- legacybridge/src/enterprise/tenancy/tenant-context.ts
- legacybridge/src/hooks/useDebounce.ts
- legacybridge/src/hooks/useMonitoringWebSocket.ts
- legacybridge/src/lib/download-service.ts
- legacybridge/src/lib/error-logger.ts
- legacybridge/src/lib/monitoring/alert-manager.ts
- legacybridge/src/lib/monitoring/websocket-server.ts
- legacybridge/src/lib/sanitizer.ts
- legacybridge/src/lib/stores/files.ts
- legacybridge/src/lib/tauri-api.ts
- legacybridge/src/lib/unified-error-api.ts
- legacybridge/src/lib/utils.ts
- legacybridge/src/middleware.ts
- legacybridge/src/styles/monitoring.css
- legacybridge/src/types/errors.ts
- legacybridge/src/types/index.ts
- legacybridge/src/types/monitoring.ts
- legacybridge/src-tauri/security.config.json
- legacybridge/src-tauri/tauri.conf.json
- legacybridge/test_32bit_compatibility.c
- legacybridge/test_dll.c
- legacybridge/test_dll.py
- legacybridge/tests/accessibility/accessibility-tests.spec.ts
- legacybridge/tests/accessibility/component-accessibility.test.tsx
- legacybridge/tests/chaos/chaos-engineering-tests.spec.ts
- legacybridge/tests/framework/test-automation-framework.ts
- legacybridge/tests/framework/types.ts
- legacybridge/tests/integration/file-conversion-flow.test.tsx
- legacybridge/tests/load/k6-load-test.js
- legacybridge/tests/mocks/fileMock.js
- legacybridge/tests/mocks/styleMock.js
- legacybridge/tests/performance/performance-regression.test.ts
- legacybridge/tests/run-all-tests.js
- legacybridge/tests/security/security-test-suite.ts
- legacybridge/tests/setup/global-setup.ts
- legacybridge/tests/setup/global-teardown.ts
- legacybridge/tests/setup/jest.setup.js
- legacybridge/tests/unit/components/ConversionProgress.test.tsx
- legacybridge/tests/unit/components/DragDropZone.test.tsx
- legacybridge/tests/unit/components/ErrorBoundary.test.tsx
- legacybridge/tests/unit/components/MarkdownPreview.test.tsx
- legacybridge/tests/unit/components/MonitoringDashboard.test.tsx
- legacybridge/tests/unit/stores/files.test.ts
- legacybridge/tests/visual-regression/visual-regression-tests.spec.ts
- legacybridge/tsconfig.json
- tests/load/k6-load-test.js
- validate_performance_claims.py


## Code Changes (2025-07-26 00:07:58)

### Added Files
- .github/workflows/ci.yml
- COMPREHENSIVE_SECURITY_ANALYSIS.json
- docker-compose.yml
- docs/api/interactive-explorer/index.html
- docs/troubleshooting/interactive-wizard/index.html
- helm/legacybridge/Chart.yaml
- helm/legacybridge/values-production.yaml
- helm/legacybridge/values.yaml
- k8s/configmap.yaml
- k8s/deployment-optimized.yaml
- k8s/deployment.yaml
- k8s/ingress.yaml
- k8s/monitoring-stack.yaml
- k8s/monitoring.yaml
- legacybridge/.eslintrc.json
- legacybridge/ENTERPRISE_PACKAGE/MANIFEST.json
- legacybridge/ENTERPRISE_PACKAGE/examples/other/test_dll.c
- legacybridge/ENTERPRISE_PACKAGE/examples/other/test_dll.py
- legacybridge/ENTERPRISE_PACKAGE/include/legacybridge.h
- legacybridge/components.json
- legacybridge/deployment_package/include/legacybridge.h
- legacybridge/frontend-integration-example.ts
- legacybridge/include/legacybridge.h
- legacybridge/jest.config.js
- legacybridge/next.config.ts
- legacybridge/package-lock.json
- legacybridge/package.json
- legacybridge/perf_test.c
- legacybridge/playwright.config.ts
- legacybridge/src/__tests__/security/xss.test.tsx
- legacybridge/src/app/api/monitoring/alerts/route.ts
- legacybridge/src/app/api/monitoring/alerts/slack/route.ts
- legacybridge/src/app/api/monitoring/metrics/route.ts
- legacybridge/src/app/api/monitoring/prometheus/route.ts
- legacybridge/src/app/demo/page.tsx
- legacybridge/src/app/globals-backup.css
- legacybridge/src/app/globals.css
- legacybridge/src/app/home-page.tsx
- legacybridge/src/app/layout.tsx
- legacybridge/src/app/monitoring/page.tsx
- legacybridge/src/app/page.tsx
- legacybridge/src/components/ConversionProgress.tsx
- legacybridge/src/components/DiffView.tsx
- legacybridge/src/components/DownloadManager.tsx
- legacybridge/src/components/DragDropZone.tsx
- legacybridge/src/components/ErrorBoundary.tsx
- legacybridge/src/components/ErrorDisplay.tsx
- legacybridge/src/components/ErrorLogViewer.tsx
- legacybridge/src/components/LoadingAnimation.tsx
- legacybridge/src/components/MarkdownPreview.tsx
- legacybridge/src/components/PreviewPanel.tsx
- legacybridge/src/components/SuccessAnimation.tsx
- legacybridge/src/components/SyntaxHighlighter.tsx
- legacybridge/src/components/ThemeProvider.tsx
- legacybridge/src/components/UnifiedErrorDisplay.tsx
- legacybridge/src/components/layout/Footer.tsx
- legacybridge/src/components/layout/Header.tsx
- legacybridge/src/components/layout/MainLayout.tsx
- legacybridge/src/components/monitoring/BuildProgressRing.tsx
- legacybridge/src/components/monitoring/EnhancedFunctionCallMatrix.tsx
- legacybridge/src/components/monitoring/FunctionCallMatrix.tsx
- legacybridge/src/components/monitoring/LogStreamViewer.tsx
- legacybridge/src/components/monitoring/MonitoringDashboard.tsx
- legacybridge/src/components/monitoring/PerformanceChart.tsx
- legacybridge/src/components/monitoring/SystemHealthCard.tsx
- legacybridge/src/components/monitoring/index.ts
- legacybridge/src/components/ui/alert-dialog.tsx
- legacybridge/src/components/ui/badge.tsx
- legacybridge/src/components/ui/button.tsx
- legacybridge/src/components/ui/card.tsx
- legacybridge/src/components/ui/dropdown-menu.tsx
- legacybridge/src/components/ui/input.tsx
- legacybridge/src/components/ui/label.tsx
- legacybridge/src/components/ui/progress.tsx
- legacybridge/src/components/ui/separator.tsx
- legacybridge/src/components/ui/switch.tsx
- legacybridge/src/components/ui/tabs.tsx
- legacybridge/src/components/ui/tooltip.tsx
- legacybridge/src/enterprise/admin/AdminDashboard.tsx
- legacybridge/src/enterprise/api/enterprise-api.ts
- legacybridge/src/enterprise/audit/audit-logger.ts
- legacybridge/src/enterprise/auth/authentication.ts
- legacybridge/src/enterprise/auth/rbac.ts
- legacybridge/src/enterprise/config/kubernetes-deployment.yaml
- legacybridge/src/enterprise/services/tenant-conversion-service.ts
- legacybridge/src/enterprise/tenancy/tenant-context.ts
- legacybridge/src/hooks/useDebounce.ts
- legacybridge/src/hooks/useMonitoringWebSocket.ts
- legacybridge/src/lib/download-service.ts
- legacybridge/src/lib/error-logger.ts
- legacybridge/src/lib/monitoring/alert-manager.ts
- legacybridge/src/lib/monitoring/websocket-server.ts
- legacybridge/src/lib/sanitizer.ts
- legacybridge/src/lib/stores/files.ts
- legacybridge/src/lib/tauri-api.ts
- legacybridge/src/lib/unified-error-api.ts
- legacybridge/src/lib/utils.ts
- legacybridge/src/middleware.ts
- legacybridge/src/styles/monitoring.css
- legacybridge/src/types/errors.ts
- legacybridge/src/types/index.ts
- legacybridge/src/types/monitoring.ts
- legacybridge/src-tauri/security.config.json
- legacybridge/src-tauri/tauri.conf.json
- legacybridge/test_32bit_compatibility.c
- legacybridge/test_dll.c
- legacybridge/test_dll.py
- legacybridge/tests/accessibility/accessibility-tests.spec.ts
- legacybridge/tests/accessibility/component-accessibility.test.tsx
- legacybridge/tests/chaos/chaos-engineering-tests.spec.ts
- legacybridge/tests/framework/test-automation-framework.ts
- legacybridge/tests/framework/types.ts
- legacybridge/tests/integration/file-conversion-flow.test.tsx
- legacybridge/tests/load/k6-load-test.js
- legacybridge/tests/mocks/fileMock.js
- legacybridge/tests/mocks/styleMock.js
- legacybridge/tests/performance/performance-regression.test.ts
- legacybridge/tests/run-all-tests.js
- legacybridge/tests/security/security-test-suite.ts
- legacybridge/tests/setup/global-setup.ts
- legacybridge/tests/setup/global-teardown.ts
- legacybridge/tests/setup/jest.setup.js
- legacybridge/tests/unit/components/ConversionProgress.test.tsx
- legacybridge/tests/unit/components/DragDropZone.test.tsx
- legacybridge/tests/unit/components/ErrorBoundary.test.tsx
- legacybridge/tests/unit/components/MarkdownPreview.test.tsx
- legacybridge/tests/unit/components/MonitoringDashboard.test.tsx
- legacybridge/tests/unit/stores/files.test.ts
- legacybridge/tests/visual-regression/visual-regression-tests.spec.ts
- legacybridge/tsconfig.json
- tests/load/k6-load-test.js
- validate_performance_claims.py


## Code Changes (2025-07-25 23:56:40)

### Added Files
- COMPREHENSIVE_SECURITY_ANALYSIS.json
- docker-compose.yml
- docs/api/interactive-explorer/index.html
- docs/troubleshooting/interactive-wizard/index.html
- helm/legacybridge/Chart.yaml
- helm/legacybridge/values-production.yaml
- helm/legacybridge/values.yaml
- k8s/configmap.yaml
- k8s/deployment-optimized.yaml
- k8s/deployment.yaml
- k8s/ingress.yaml
- k8s/monitoring-stack.yaml
- k8s/monitoring.yaml
- legacybridge/.eslintrc.json
- legacybridge/ENTERPRISE_PACKAGE/MANIFEST.json
- legacybridge/ENTERPRISE_PACKAGE/examples/other/test_dll.c
- legacybridge/ENTERPRISE_PACKAGE/examples/other/test_dll.py
- legacybridge/ENTERPRISE_PACKAGE/include/legacybridge.h
- legacybridge/components.json
- legacybridge/deployment_package/include/legacybridge.h
- legacybridge/frontend-integration-example.ts
- legacybridge/include/legacybridge.h
- legacybridge/jest.config.js
- legacybridge/next.config.ts
- legacybridge/package-lock.json
- legacybridge/package.json
- legacybridge/perf_test.c
- legacybridge/playwright.config.ts
- legacybridge/src/__tests__/security/xss.test.tsx
- legacybridge/src/app/api/monitoring/alerts/route.ts
- legacybridge/src/app/api/monitoring/alerts/slack/route.ts
- legacybridge/src/app/api/monitoring/metrics/route.ts
- legacybridge/src/app/api/monitoring/prometheus/route.ts
- legacybridge/src/app/demo/page.tsx
- legacybridge/src/app/globals-backup.css
- legacybridge/src/app/globals.css
- legacybridge/src/app/home-page.tsx
- legacybridge/src/app/layout.tsx
- legacybridge/src/app/monitoring/page.tsx
- legacybridge/src/app/page.tsx
- legacybridge/src/components/ConversionProgress.tsx
- legacybridge/src/components/DiffView.tsx
- legacybridge/src/components/DownloadManager.tsx
- legacybridge/src/components/DragDropZone.tsx
- legacybridge/src/components/ErrorBoundary.tsx
- legacybridge/src/components/ErrorDisplay.tsx
- legacybridge/src/components/ErrorLogViewer.tsx
- legacybridge/src/components/LoadingAnimation.tsx
- legacybridge/src/components/MarkdownPreview.tsx
- legacybridge/src/components/PreviewPanel.tsx
- legacybridge/src/components/SuccessAnimation.tsx
- legacybridge/src/components/SyntaxHighlighter.tsx
- legacybridge/src/components/ThemeProvider.tsx
- legacybridge/src/components/UnifiedErrorDisplay.tsx
- legacybridge/src/components/layout/Footer.tsx
- legacybridge/src/components/layout/Header.tsx
- legacybridge/src/components/layout/MainLayout.tsx
- legacybridge/src/components/monitoring/BuildProgressRing.tsx
- legacybridge/src/components/monitoring/EnhancedFunctionCallMatrix.tsx
- legacybridge/src/components/monitoring/FunctionCallMatrix.tsx
- legacybridge/src/components/monitoring/LogStreamViewer.tsx
- legacybridge/src/components/monitoring/MonitoringDashboard.tsx
- legacybridge/src/components/monitoring/PerformanceChart.tsx
- legacybridge/src/components/monitoring/SystemHealthCard.tsx
- legacybridge/src/components/monitoring/index.ts
- legacybridge/src/components/ui/alert-dialog.tsx
- legacybridge/src/components/ui/badge.tsx
- legacybridge/src/components/ui/button.tsx
- legacybridge/src/components/ui/card.tsx
- legacybridge/src/components/ui/dropdown-menu.tsx
- legacybridge/src/components/ui/input.tsx
- legacybridge/src/components/ui/label.tsx
- legacybridge/src/components/ui/progress.tsx
- legacybridge/src/components/ui/separator.tsx
- legacybridge/src/components/ui/switch.tsx
- legacybridge/src/components/ui/tabs.tsx
- legacybridge/src/components/ui/tooltip.tsx
- legacybridge/src/enterprise/admin/AdminDashboard.tsx
- legacybridge/src/enterprise/api/enterprise-api.ts
- legacybridge/src/enterprise/audit/audit-logger.ts
- legacybridge/src/enterprise/auth/authentication.ts
- legacybridge/src/enterprise/auth/rbac.ts
- legacybridge/src/enterprise/config/kubernetes-deployment.yaml
- legacybridge/src/enterprise/services/tenant-conversion-service.ts
- legacybridge/src/enterprise/tenancy/tenant-context.ts
- legacybridge/src/hooks/useDebounce.ts
- legacybridge/src/hooks/useMonitoringWebSocket.ts
- legacybridge/src/lib/download-service.ts
- legacybridge/src/lib/error-logger.ts
- legacybridge/src/lib/monitoring/alert-manager.ts
- legacybridge/src/lib/monitoring/websocket-server.ts
- legacybridge/src/lib/sanitizer.ts
- legacybridge/src/lib/stores/files.ts
- legacybridge/src/lib/tauri-api.ts
- legacybridge/src/lib/unified-error-api.ts
- legacybridge/src/lib/utils.ts
- legacybridge/src/middleware.ts
- legacybridge/src/styles/monitoring.css
- legacybridge/src/types/errors.ts
- legacybridge/src/types/index.ts
- legacybridge/src/types/monitoring.ts
- legacybridge/src-tauri/security.config.json
- legacybridge/src-tauri/tauri.conf.json
- legacybridge/test_32bit_compatibility.c
- legacybridge/test_dll.c
- legacybridge/test_dll.py
- legacybridge/tests/accessibility/accessibility-tests.spec.ts
- legacybridge/tests/accessibility/component-accessibility.test.tsx
- legacybridge/tests/chaos/chaos-engineering-tests.spec.ts
- legacybridge/tests/framework/test-automation-framework.ts
- legacybridge/tests/framework/types.ts
- legacybridge/tests/integration/file-conversion-flow.test.tsx
- legacybridge/tests/load/k6-load-test.js
- legacybridge/tests/mocks/fileMock.js
- legacybridge/tests/mocks/styleMock.js
- legacybridge/tests/performance/performance-regression.test.ts
- legacybridge/tests/run-all-tests.js
- legacybridge/tests/security/security-test-suite.ts
- legacybridge/tests/setup/global-setup.ts
- legacybridge/tests/setup/global-teardown.ts
- legacybridge/tests/setup/jest.setup.js
- legacybridge/tests/unit/components/ConversionProgress.test.tsx
- legacybridge/tests/unit/components/DragDropZone.test.tsx
- legacybridge/tests/unit/components/ErrorBoundary.test.tsx
- legacybridge/tests/unit/components/MarkdownPreview.test.tsx
- legacybridge/tests/unit/components/MonitoringDashboard.test.tsx
- legacybridge/tests/unit/stores/files.test.ts
- legacybridge/tests/visual-regression/visual-regression-tests.spec.ts
- legacybridge/tsconfig.json
- tests/load/k6-load-test.js
- validate_performance_claims.py


## Code Changes (2025-07-25 23:54:08)

### Added Files
- COMPREHENSIVE_SECURITY_ANALYSIS.json
- docker-compose.yml
- docs/api/interactive-explorer/index.html
- docs/troubleshooting/interactive-wizard/index.html
- helm/legacybridge/Chart.yaml
- helm/legacybridge/values-production.yaml
- helm/legacybridge/values.yaml
- k8s/configmap.yaml
- k8s/deployment-optimized.yaml
- k8s/deployment.yaml
- k8s/ingress.yaml
- k8s/monitoring-stack.yaml
- k8s/monitoring.yaml
- legacybridge/.eslintrc.json
- legacybridge/ENTERPRISE_PACKAGE/MANIFEST.json
- legacybridge/ENTERPRISE_PACKAGE/examples/other/test_dll.c
- legacybridge/ENTERPRISE_PACKAGE/examples/other/test_dll.py
- legacybridge/ENTERPRISE_PACKAGE/include/legacybridge.h
- legacybridge/components.json
- legacybridge/deployment_package/include/legacybridge.h
- legacybridge/frontend-integration-example.ts
- legacybridge/include/legacybridge.h
- legacybridge/jest.config.js
- legacybridge/next.config.ts
- legacybridge/package-lock.json
- legacybridge/package.json
- legacybridge/perf_test.c
- legacybridge/playwright.config.ts
- legacybridge/src/__tests__/security/xss.test.tsx
- legacybridge/src/app/api/monitoring/alerts/route.ts
- legacybridge/src/app/api/monitoring/alerts/slack/route.ts
- legacybridge/src/app/api/monitoring/metrics/route.ts
- legacybridge/src/app/api/monitoring/prometheus/route.ts
- legacybridge/src/app/demo/page.tsx
- legacybridge/src/app/globals-backup.css
- legacybridge/src/app/globals.css
- legacybridge/src/app/home-page.tsx
- legacybridge/src/app/layout.tsx
- legacybridge/src/app/monitoring/page.tsx
- legacybridge/src/app/page.tsx
- legacybridge/src/components/ConversionProgress.tsx
- legacybridge/src/components/DiffView.tsx
- legacybridge/src/components/DownloadManager.tsx
- legacybridge/src/components/DragDropZone.tsx
- legacybridge/src/components/ErrorBoundary.tsx
- legacybridge/src/components/ErrorDisplay.tsx
- legacybridge/src/components/ErrorLogViewer.tsx
- legacybridge/src/components/LoadingAnimation.tsx
- legacybridge/src/components/MarkdownPreview.tsx
- legacybridge/src/components/PreviewPanel.tsx
- legacybridge/src/components/SuccessAnimation.tsx
- legacybridge/src/components/SyntaxHighlighter.tsx
- legacybridge/src/components/ThemeProvider.tsx
- legacybridge/src/components/UnifiedErrorDisplay.tsx
- legacybridge/src/components/layout/Footer.tsx
- legacybridge/src/components/layout/Header.tsx
- legacybridge/src/components/layout/MainLayout.tsx
- legacybridge/src/components/monitoring/BuildProgressRing.tsx
- legacybridge/src/components/monitoring/EnhancedFunctionCallMatrix.tsx
- legacybridge/src/components/monitoring/FunctionCallMatrix.tsx
- legacybridge/src/components/monitoring/LogStreamViewer.tsx
- legacybridge/src/components/monitoring/MonitoringDashboard.tsx
- legacybridge/src/components/monitoring/PerformanceChart.tsx
- legacybridge/src/components/monitoring/SystemHealthCard.tsx
- legacybridge/src/components/monitoring/index.ts
- legacybridge/src/components/ui/alert-dialog.tsx
- legacybridge/src/components/ui/badge.tsx
- legacybridge/src/components/ui/button.tsx
- legacybridge/src/components/ui/card.tsx
- legacybridge/src/components/ui/dropdown-menu.tsx
- legacybridge/src/components/ui/input.tsx
- legacybridge/src/components/ui/label.tsx
- legacybridge/src/components/ui/progress.tsx
- legacybridge/src/components/ui/separator.tsx
- legacybridge/src/components/ui/switch.tsx
- legacybridge/src/components/ui/tabs.tsx
- legacybridge/src/components/ui/tooltip.tsx
- legacybridge/src/enterprise/admin/AdminDashboard.tsx
- legacybridge/src/enterprise/api/enterprise-api.ts
- legacybridge/src/enterprise/audit/audit-logger.ts
- legacybridge/src/enterprise/auth/authentication.ts
- legacybridge/src/enterprise/auth/rbac.ts
- legacybridge/src/enterprise/config/kubernetes-deployment.yaml
- legacybridge/src/enterprise/services/tenant-conversion-service.ts
- legacybridge/src/enterprise/tenancy/tenant-context.ts
- legacybridge/src/hooks/useDebounce.ts
- legacybridge/src/hooks/useMonitoringWebSocket.ts
- legacybridge/src/lib/download-service.ts
- legacybridge/src/lib/error-logger.ts
- legacybridge/src/lib/monitoring/alert-manager.ts
- legacybridge/src/lib/monitoring/websocket-server.ts
- legacybridge/src/lib/sanitizer.ts
- legacybridge/src/lib/stores/files.ts
- legacybridge/src/lib/tauri-api.ts
- legacybridge/src/lib/unified-error-api.ts
- legacybridge/src/lib/utils.ts
- legacybridge/src/middleware.ts
- legacybridge/src/styles/monitoring.css
- legacybridge/src/types/errors.ts
- legacybridge/src/types/index.ts
- legacybridge/src/types/monitoring.ts
- legacybridge/src-tauri/security.config.json
- legacybridge/src-tauri/tauri.conf.json
- legacybridge/test_32bit_compatibility.c
- legacybridge/test_dll.c
- legacybridge/test_dll.py
- legacybridge/tests/accessibility/accessibility-tests.spec.ts
- legacybridge/tests/accessibility/component-accessibility.test.tsx
- legacybridge/tests/chaos/chaos-engineering-tests.spec.ts
- legacybridge/tests/framework/test-automation-framework.ts
- legacybridge/tests/framework/types.ts
- legacybridge/tests/integration/file-conversion-flow.test.tsx
- legacybridge/tests/load/k6-load-test.js
- legacybridge/tests/mocks/fileMock.js
- legacybridge/tests/mocks/styleMock.js
- legacybridge/tests/performance/performance-regression.test.ts
- legacybridge/tests/run-all-tests.js
- legacybridge/tests/security/security-test-suite.ts
- legacybridge/tests/setup/global-setup.ts
- legacybridge/tests/setup/global-teardown.ts
- legacybridge/tests/setup/jest.setup.js
- legacybridge/tests/unit/components/ConversionProgress.test.tsx
- legacybridge/tests/unit/components/DragDropZone.test.tsx
- legacybridge/tests/unit/components/ErrorBoundary.test.tsx
- legacybridge/tests/unit/components/MarkdownPreview.test.tsx
- legacybridge/tests/unit/components/MonitoringDashboard.test.tsx
- legacybridge/tests/unit/stores/files.test.ts
- legacybridge/tests/visual-regression/visual-regression-tests.spec.ts
- legacybridge/tsconfig.json
- tests/load/k6-load-test.js
- validate_performance_claims.py


## Code Changes (2025-07-25 23:51:24)

### Added Files
- COMPREHENSIVE_SECURITY_ANALYSIS.json
- docker-compose.yml
- docs/api/interactive-explorer/index.html
- docs/troubleshooting/interactive-wizard/index.html
- helm/legacybridge/Chart.yaml
- helm/legacybridge/values-production.yaml
- helm/legacybridge/values.yaml
- k8s/configmap.yaml
- k8s/deployment-optimized.yaml
- k8s/deployment.yaml
- k8s/ingress.yaml
- k8s/monitoring-stack.yaml
- k8s/monitoring.yaml
- legacybridge/.eslintrc.json
- legacybridge/ENTERPRISE_PACKAGE/MANIFEST.json
- legacybridge/ENTERPRISE_PACKAGE/examples/other/test_dll.c
- legacybridge/ENTERPRISE_PACKAGE/examples/other/test_dll.py
- legacybridge/ENTERPRISE_PACKAGE/include/legacybridge.h
- legacybridge/components.json
- legacybridge/deployment_package/include/legacybridge.h
- legacybridge/frontend-integration-example.ts
- legacybridge/include/legacybridge.h
- legacybridge/jest.config.js
- legacybridge/next.config.ts
- legacybridge/package-lock.json
- legacybridge/package.json
- legacybridge/perf_test.c
- legacybridge/playwright.config.ts
- legacybridge/src/__tests__/security/xss.test.tsx
- legacybridge/src/app/api/monitoring/alerts/route.ts
- legacybridge/src/app/api/monitoring/alerts/slack/route.ts
- legacybridge/src/app/api/monitoring/metrics/route.ts
- legacybridge/src/app/api/monitoring/prometheus/route.ts
- legacybridge/src/app/demo/page.tsx
- legacybridge/src/app/globals-backup.css
- legacybridge/src/app/globals.css
- legacybridge/src/app/home-page.tsx
- legacybridge/src/app/layout.tsx
- legacybridge/src/app/monitoring/page.tsx
- legacybridge/src/app/page.tsx
- legacybridge/src/components/ConversionProgress.tsx
- legacybridge/src/components/DiffView.tsx
- legacybridge/src/components/DownloadManager.tsx
- legacybridge/src/components/DragDropZone.tsx
- legacybridge/src/components/ErrorBoundary.tsx
- legacybridge/src/components/ErrorDisplay.tsx
- legacybridge/src/components/ErrorLogViewer.tsx
- legacybridge/src/components/LoadingAnimation.tsx
- legacybridge/src/components/MarkdownPreview.tsx
- legacybridge/src/components/PreviewPanel.tsx
- legacybridge/src/components/SuccessAnimation.tsx
- legacybridge/src/components/SyntaxHighlighter.tsx
- legacybridge/src/components/ThemeProvider.tsx
- legacybridge/src/components/UnifiedErrorDisplay.tsx
- legacybridge/src/components/layout/Footer.tsx
- legacybridge/src/components/layout/Header.tsx
- legacybridge/src/components/layout/MainLayout.tsx
- legacybridge/src/components/monitoring/BuildProgressRing.tsx
- legacybridge/src/components/monitoring/EnhancedFunctionCallMatrix.tsx
- legacybridge/src/components/monitoring/FunctionCallMatrix.tsx
- legacybridge/src/components/monitoring/LogStreamViewer.tsx
- legacybridge/src/components/monitoring/MonitoringDashboard.tsx
- legacybridge/src/components/monitoring/PerformanceChart.tsx
- legacybridge/src/components/monitoring/SystemHealthCard.tsx
- legacybridge/src/components/monitoring/index.ts
- legacybridge/src/components/ui/alert-dialog.tsx
- legacybridge/src/components/ui/badge.tsx
- legacybridge/src/components/ui/button.tsx
- legacybridge/src/components/ui/card.tsx
- legacybridge/src/components/ui/dropdown-menu.tsx
- legacybridge/src/components/ui/input.tsx
- legacybridge/src/components/ui/label.tsx
- legacybridge/src/components/ui/progress.tsx
- legacybridge/src/components/ui/separator.tsx
- legacybridge/src/components/ui/switch.tsx
- legacybridge/src/components/ui/tabs.tsx
- legacybridge/src/components/ui/tooltip.tsx
- legacybridge/src/enterprise/admin/AdminDashboard.tsx
- legacybridge/src/enterprise/api/enterprise-api.ts
- legacybridge/src/enterprise/audit/audit-logger.ts
- legacybridge/src/enterprise/auth/authentication.ts
- legacybridge/src/enterprise/auth/rbac.ts
- legacybridge/src/enterprise/config/kubernetes-deployment.yaml
- legacybridge/src/enterprise/services/tenant-conversion-service.ts
- legacybridge/src/enterprise/tenancy/tenant-context.ts
- legacybridge/src/hooks/useDebounce.ts
- legacybridge/src/hooks/useMonitoringWebSocket.ts
- legacybridge/src/lib/download-service.ts
- legacybridge/src/lib/error-logger.ts
- legacybridge/src/lib/monitoring/alert-manager.ts
- legacybridge/src/lib/monitoring/websocket-server.ts
- legacybridge/src/lib/sanitizer.ts
- legacybridge/src/lib/stores/files.ts
- legacybridge/src/lib/tauri-api.ts
- legacybridge/src/lib/unified-error-api.ts
- legacybridge/src/lib/utils.ts
- legacybridge/src/middleware.ts
- legacybridge/src/styles/monitoring.css
- legacybridge/src/types/errors.ts
- legacybridge/src/types/index.ts
- legacybridge/src/types/monitoring.ts
- legacybridge/src-tauri/security.config.json
- legacybridge/src-tauri/tauri.conf.json
- legacybridge/test_32bit_compatibility.c
- legacybridge/test_dll.c
- legacybridge/test_dll.py
- legacybridge/tests/accessibility/accessibility-tests.spec.ts
- legacybridge/tests/accessibility/component-accessibility.test.tsx
- legacybridge/tests/chaos/chaos-engineering-tests.spec.ts
- legacybridge/tests/framework/test-automation-framework.ts
- legacybridge/tests/framework/types.ts
- legacybridge/tests/integration/file-conversion-flow.test.tsx
- legacybridge/tests/load/k6-load-test.js
- legacybridge/tests/mocks/fileMock.js
- legacybridge/tests/mocks/styleMock.js
- legacybridge/tests/performance/performance-regression.test.ts
- legacybridge/tests/run-all-tests.js
- legacybridge/tests/security/security-test-suite.ts
- legacybridge/tests/setup/global-setup.ts
- legacybridge/tests/setup/global-teardown.ts
- legacybridge/tests/setup/jest.setup.js
- legacybridge/tests/unit/components/ConversionProgress.test.tsx
- legacybridge/tests/unit/components/DragDropZone.test.tsx
- legacybridge/tests/unit/components/ErrorBoundary.test.tsx
- legacybridge/tests/unit/components/MarkdownPreview.test.tsx
- legacybridge/tests/unit/components/MonitoringDashboard.test.tsx
- legacybridge/tests/unit/stores/files.test.ts
- legacybridge/tests/visual-regression/visual-regression-tests.spec.ts
- legacybridge/tsconfig.json
- tests/load/k6-load-test.js
- validate_performance_claims.py


## Code Changes (2025-07-25 23:41:17)

### Added Files
- COMPREHENSIVE_SECURITY_ANALYSIS.json
- docker-compose.yml
- docs/api/interactive-explorer/index.html
- docs/troubleshooting/interactive-wizard/index.html
- helm/legacybridge/Chart.yaml
- helm/legacybridge/values-production.yaml
- helm/legacybridge/values.yaml
- k8s/configmap.yaml
- k8s/deployment-optimized.yaml
- k8s/deployment.yaml
- k8s/ingress.yaml
- k8s/monitoring-stack.yaml
- k8s/monitoring.yaml
- legacybridge/.eslintrc.json
- legacybridge/ENTERPRISE_PACKAGE/MANIFEST.json
- legacybridge/ENTERPRISE_PACKAGE/examples/other/test_dll.c
- legacybridge/ENTERPRISE_PACKAGE/examples/other/test_dll.py
- legacybridge/ENTERPRISE_PACKAGE/include/legacybridge.h
- legacybridge/components.json
- legacybridge/deployment_package/include/legacybridge.h
- legacybridge/frontend-integration-example.ts
- legacybridge/include/legacybridge.h
- legacybridge/jest.config.js
- legacybridge/next.config.ts
- legacybridge/package-lock.json
- legacybridge/package.json
- legacybridge/perf_test.c
- legacybridge/playwright.config.ts
- legacybridge/src/__tests__/security/xss.test.tsx
- legacybridge/src/app/api/monitoring/alerts/route.ts
- legacybridge/src/app/api/monitoring/alerts/slack/route.ts
- legacybridge/src/app/api/monitoring/metrics/route.ts
- legacybridge/src/app/api/monitoring/prometheus/route.ts
- legacybridge/src/app/demo/page.tsx
- legacybridge/src/app/globals-backup.css
- legacybridge/src/app/globals.css
- legacybridge/src/app/home-page.tsx
- legacybridge/src/app/layout.tsx
- legacybridge/src/app/monitoring/page.tsx
- legacybridge/src/app/page.tsx
- legacybridge/src/components/ConversionProgress.tsx
- legacybridge/src/components/DiffView.tsx
- legacybridge/src/components/DownloadManager.tsx
- legacybridge/src/components/DragDropZone.tsx
- legacybridge/src/components/ErrorBoundary.tsx
- legacybridge/src/components/ErrorDisplay.tsx
- legacybridge/src/components/ErrorLogViewer.tsx
- legacybridge/src/components/LoadingAnimation.tsx
- legacybridge/src/components/MarkdownPreview.tsx
- legacybridge/src/components/PreviewPanel.tsx
- legacybridge/src/components/SuccessAnimation.tsx
- legacybridge/src/components/SyntaxHighlighter.tsx
- legacybridge/src/components/ThemeProvider.tsx
- legacybridge/src/components/UnifiedErrorDisplay.tsx
- legacybridge/src/components/layout/Footer.tsx
- legacybridge/src/components/layout/Header.tsx
- legacybridge/src/components/layout/MainLayout.tsx
- legacybridge/src/components/monitoring/BuildProgressRing.tsx
- legacybridge/src/components/monitoring/EnhancedFunctionCallMatrix.tsx
- legacybridge/src/components/monitoring/FunctionCallMatrix.tsx
- legacybridge/src/components/monitoring/LogStreamViewer.tsx
- legacybridge/src/components/monitoring/MonitoringDashboard.tsx
- legacybridge/src/components/monitoring/PerformanceChart.tsx
- legacybridge/src/components/monitoring/SystemHealthCard.tsx
- legacybridge/src/components/monitoring/index.ts
- legacybridge/src/components/ui/alert-dialog.tsx
- legacybridge/src/components/ui/badge.tsx
- legacybridge/src/components/ui/button.tsx
- legacybridge/src/components/ui/card.tsx
- legacybridge/src/components/ui/dropdown-menu.tsx
- legacybridge/src/components/ui/input.tsx
- legacybridge/src/components/ui/label.tsx
- legacybridge/src/components/ui/progress.tsx
- legacybridge/src/components/ui/separator.tsx
- legacybridge/src/components/ui/switch.tsx
- legacybridge/src/components/ui/tabs.tsx
- legacybridge/src/components/ui/tooltip.tsx
- legacybridge/src/enterprise/admin/AdminDashboard.tsx
- legacybridge/src/enterprise/api/enterprise-api.ts
- legacybridge/src/enterprise/audit/audit-logger.ts
- legacybridge/src/enterprise/auth/authentication.ts
- legacybridge/src/enterprise/auth/rbac.ts
- legacybridge/src/enterprise/config/kubernetes-deployment.yaml
- legacybridge/src/enterprise/services/tenant-conversion-service.ts
- legacybridge/src/enterprise/tenancy/tenant-context.ts
- legacybridge/src/hooks/useDebounce.ts
- legacybridge/src/hooks/useMonitoringWebSocket.ts
- legacybridge/src/lib/download-service.ts
- legacybridge/src/lib/error-logger.ts
- legacybridge/src/lib/monitoring/alert-manager.ts
- legacybridge/src/lib/monitoring/websocket-server.ts
- legacybridge/src/lib/sanitizer.ts
- legacybridge/src/lib/stores/files.ts
- legacybridge/src/lib/tauri-api.ts
- legacybridge/src/lib/unified-error-api.ts
- legacybridge/src/lib/utils.ts
- legacybridge/src/middleware.ts
- legacybridge/src/styles/monitoring.css
- legacybridge/src/types/errors.ts
- legacybridge/src/types/index.ts
- legacybridge/src/types/monitoring.ts
- legacybridge/src-tauri/security.config.json
- legacybridge/src-tauri/tauri.conf.json
- legacybridge/test_32bit_compatibility.c
- legacybridge/test_dll.c
- legacybridge/test_dll.py
- legacybridge/tests/accessibility/accessibility-tests.spec.ts
- legacybridge/tests/accessibility/component-accessibility.test.tsx
- legacybridge/tests/chaos/chaos-engineering-tests.spec.ts
- legacybridge/tests/framework/test-automation-framework.ts
- legacybridge/tests/framework/types.ts
- legacybridge/tests/integration/file-conversion-flow.test.tsx
- legacybridge/tests/load/k6-load-test.js
- legacybridge/tests/mocks/fileMock.js
- legacybridge/tests/mocks/styleMock.js
- legacybridge/tests/performance/performance-regression.test.ts
- legacybridge/tests/run-all-tests.js
- legacybridge/tests/security/security-test-suite.ts
- legacybridge/tests/setup/global-setup.ts
- legacybridge/tests/setup/global-teardown.ts
- legacybridge/tests/setup/jest.setup.js
- legacybridge/tests/unit/components/ConversionProgress.test.tsx
- legacybridge/tests/unit/components/DragDropZone.test.tsx
- legacybridge/tests/unit/components/ErrorBoundary.test.tsx
- legacybridge/tests/unit/components/MarkdownPreview.test.tsx
- legacybridge/tests/unit/components/MonitoringDashboard.test.tsx
- legacybridge/tests/unit/stores/files.test.ts
- legacybridge/tests/visual-regression/visual-regression-tests.spec.ts
- legacybridge/tsconfig.json
- tests/load/k6-load-test.js
- validate_performance_claims.py


## Code Changes (2025-07-25 22:44:42)

### Added Files
- COMPREHENSIVE_SECURITY_ANALYSIS.json
- docker-compose.yml
- docs/api/interactive-explorer/index.html
- docs/troubleshooting/interactive-wizard/index.html
- helm/legacybridge/Chart.yaml
- helm/legacybridge/values-production.yaml
- helm/legacybridge/values.yaml
- k8s/configmap.yaml
- k8s/deployment-optimized.yaml
- k8s/deployment.yaml
- k8s/ingress.yaml
- k8s/monitoring-stack.yaml
- k8s/monitoring.yaml
- legacybridge/.eslintrc.json
- legacybridge/ENTERPRISE_PACKAGE/MANIFEST.json
- legacybridge/ENTERPRISE_PACKAGE/examples/other/test_dll.c
- legacybridge/ENTERPRISE_PACKAGE/examples/other/test_dll.py
- legacybridge/ENTERPRISE_PACKAGE/include/legacybridge.h
- legacybridge/components.json
- legacybridge/deployment_package/include/legacybridge.h
- legacybridge/frontend-integration-example.ts
- legacybridge/include/legacybridge.h
- legacybridge/jest.config.js
- legacybridge/next.config.ts
- legacybridge/package-lock.json
- legacybridge/package.json
- legacybridge/perf_test.c
- legacybridge/playwright.config.ts
- legacybridge/src/__tests__/security/xss.test.tsx
- legacybridge/src/app/api/monitoring/alerts/route.ts
- legacybridge/src/app/api/monitoring/alerts/slack/route.ts
- legacybridge/src/app/api/monitoring/metrics/route.ts
- legacybridge/src/app/api/monitoring/prometheus/route.ts
- legacybridge/src/app/demo/page.tsx
- legacybridge/src/app/globals-backup.css
- legacybridge/src/app/globals.css
- legacybridge/src/app/home-page.tsx
- legacybridge/src/app/layout.tsx
- legacybridge/src/app/monitoring/page.tsx
- legacybridge/src/app/page.tsx
- legacybridge/src/components/ConversionProgress.tsx
- legacybridge/src/components/DiffView.tsx
- legacybridge/src/components/DownloadManager.tsx
- legacybridge/src/components/DragDropZone.tsx
- legacybridge/src/components/ErrorBoundary.tsx
- legacybridge/src/components/ErrorDisplay.tsx
- legacybridge/src/components/ErrorLogViewer.tsx
- legacybridge/src/components/LoadingAnimation.tsx
- legacybridge/src/components/MarkdownPreview.tsx
- legacybridge/src/components/PreviewPanel.tsx
- legacybridge/src/components/SuccessAnimation.tsx
- legacybridge/src/components/SyntaxHighlighter.tsx
- legacybridge/src/components/ThemeProvider.tsx
- legacybridge/src/components/UnifiedErrorDisplay.tsx
- legacybridge/src/components/layout/Footer.tsx
- legacybridge/src/components/layout/Header.tsx
- legacybridge/src/components/layout/MainLayout.tsx
- legacybridge/src/components/monitoring/BuildProgressRing.tsx
- legacybridge/src/components/monitoring/EnhancedFunctionCallMatrix.tsx
- legacybridge/src/components/monitoring/FunctionCallMatrix.tsx
- legacybridge/src/components/monitoring/LogStreamViewer.tsx
- legacybridge/src/components/monitoring/MonitoringDashboard.tsx
- legacybridge/src/components/monitoring/PerformanceChart.tsx
- legacybridge/src/components/monitoring/SystemHealthCard.tsx
- legacybridge/src/components/monitoring/index.ts
- legacybridge/src/components/ui/alert-dialog.tsx
- legacybridge/src/components/ui/badge.tsx
- legacybridge/src/components/ui/button.tsx
- legacybridge/src/components/ui/card.tsx
- legacybridge/src/components/ui/dropdown-menu.tsx
- legacybridge/src/components/ui/input.tsx
- legacybridge/src/components/ui/label.tsx
- legacybridge/src/components/ui/progress.tsx
- legacybridge/src/components/ui/separator.tsx
- legacybridge/src/components/ui/switch.tsx
- legacybridge/src/components/ui/tabs.tsx
- legacybridge/src/components/ui/tooltip.tsx
- legacybridge/src/enterprise/admin/AdminDashboard.tsx
- legacybridge/src/enterprise/api/enterprise-api.ts
- legacybridge/src/enterprise/audit/audit-logger.ts
- legacybridge/src/enterprise/auth/authentication.ts
- legacybridge/src/enterprise/auth/rbac.ts
- legacybridge/src/enterprise/config/kubernetes-deployment.yaml
- legacybridge/src/enterprise/services/tenant-conversion-service.ts
- legacybridge/src/enterprise/tenancy/tenant-context.ts
- legacybridge/src/hooks/useDebounce.ts
- legacybridge/src/hooks/useMonitoringWebSocket.ts
- legacybridge/src/lib/download-service.ts
- legacybridge/src/lib/error-logger.ts
- legacybridge/src/lib/monitoring/alert-manager.ts
- legacybridge/src/lib/monitoring/websocket-server.ts
- legacybridge/src/lib/sanitizer.ts
- legacybridge/src/lib/stores/files.ts
- legacybridge/src/lib/tauri-api.ts
- legacybridge/src/lib/unified-error-api.ts
- legacybridge/src/lib/utils.ts
- legacybridge/src/middleware.ts
- legacybridge/src/styles/monitoring.css
- legacybridge/src/types/errors.ts
- legacybridge/src/types/index.ts
- legacybridge/src/types/monitoring.ts
- legacybridge/src-tauri/security.config.json
- legacybridge/src-tauri/tauri.conf.json
- legacybridge/test_32bit_compatibility.c
- legacybridge/test_dll.c
- legacybridge/test_dll.py
- legacybridge/tests/accessibility/accessibility-tests.spec.ts
- legacybridge/tests/accessibility/component-accessibility.test.tsx
- legacybridge/tests/chaos/chaos-engineering-tests.spec.ts
- legacybridge/tests/framework/test-automation-framework.ts
- legacybridge/tests/framework/types.ts
- legacybridge/tests/integration/file-conversion-flow.test.tsx
- legacybridge/tests/load/k6-load-test.js
- legacybridge/tests/mocks/fileMock.js
- legacybridge/tests/mocks/styleMock.js
- legacybridge/tests/performance/performance-regression.test.ts
- legacybridge/tests/run-all-tests.js
- legacybridge/tests/security/security-test-suite.ts
- legacybridge/tests/setup/global-setup.ts
- legacybridge/tests/setup/global-teardown.ts
- legacybridge/tests/setup/jest.setup.js
- legacybridge/tests/unit/components/ConversionProgress.test.tsx
- legacybridge/tests/unit/components/DragDropZone.test.tsx
- legacybridge/tests/unit/components/ErrorBoundary.test.tsx
- legacybridge/tests/unit/components/MarkdownPreview.test.tsx
- legacybridge/tests/unit/components/MonitoringDashboard.test.tsx
- legacybridge/tests/unit/stores/files.test.ts
- legacybridge/tests/visual-regression/visual-regression-tests.spec.ts
- legacybridge/tsconfig.json
- tests/load/k6-load-test.js
- validate_performance_claims.py


## Code Changes (2025-07-25 22:37:27)

### Added Files
- COMPREHENSIVE_SECURITY_ANALYSIS.json
- docker-compose.yml
- docs/api/interactive-explorer/index.html
- docs/troubleshooting/interactive-wizard/index.html
- helm/legacybridge/Chart.yaml
- helm/legacybridge/values-production.yaml
- helm/legacybridge/values.yaml
- k8s/configmap.yaml
- k8s/deployment-optimized.yaml
- k8s/deployment.yaml
- k8s/ingress.yaml
- k8s/monitoring-stack.yaml
- k8s/monitoring.yaml
- legacybridge/.eslintrc.json
- legacybridge/ENTERPRISE_PACKAGE/MANIFEST.json
- legacybridge/ENTERPRISE_PACKAGE/examples/other/test_dll.c
- legacybridge/ENTERPRISE_PACKAGE/examples/other/test_dll.py
- legacybridge/ENTERPRISE_PACKAGE/include/legacybridge.h
- legacybridge/components.json
- legacybridge/deployment_package/include/legacybridge.h
- legacybridge/frontend-integration-example.ts
- legacybridge/include/legacybridge.h
- legacybridge/jest.config.js
- legacybridge/next.config.ts
- legacybridge/package-lock.json
- legacybridge/package.json
- legacybridge/perf_test.c
- legacybridge/playwright.config.ts
- legacybridge/src/__tests__/security/xss.test.tsx
- legacybridge/src/app/api/monitoring/alerts/route.ts
- legacybridge/src/app/api/monitoring/alerts/slack/route.ts
- legacybridge/src/app/api/monitoring/metrics/route.ts
- legacybridge/src/app/api/monitoring/prometheus/route.ts
- legacybridge/src/app/demo/page.tsx
- legacybridge/src/app/globals-backup.css
- legacybridge/src/app/globals.css
- legacybridge/src/app/home-page.tsx
- legacybridge/src/app/layout.tsx
- legacybridge/src/app/monitoring/page.tsx
- legacybridge/src/app/page.tsx
- legacybridge/src/components/ConversionProgress.tsx
- legacybridge/src/components/DiffView.tsx
- legacybridge/src/components/DownloadManager.tsx
- legacybridge/src/components/DragDropZone.tsx
- legacybridge/src/components/ErrorBoundary.tsx
- legacybridge/src/components/ErrorDisplay.tsx
- legacybridge/src/components/ErrorLogViewer.tsx
- legacybridge/src/components/LoadingAnimation.tsx
- legacybridge/src/components/MarkdownPreview.tsx
- legacybridge/src/components/PreviewPanel.tsx
- legacybridge/src/components/SuccessAnimation.tsx
- legacybridge/src/components/SyntaxHighlighter.tsx
- legacybridge/src/components/ThemeProvider.tsx
- legacybridge/src/components/UnifiedErrorDisplay.tsx
- legacybridge/src/components/layout/Footer.tsx
- legacybridge/src/components/layout/Header.tsx
- legacybridge/src/components/layout/MainLayout.tsx
- legacybridge/src/components/monitoring/BuildProgressRing.tsx
- legacybridge/src/components/monitoring/EnhancedFunctionCallMatrix.tsx
- legacybridge/src/components/monitoring/FunctionCallMatrix.tsx
- legacybridge/src/components/monitoring/LogStreamViewer.tsx
- legacybridge/src/components/monitoring/MonitoringDashboard.tsx
- legacybridge/src/components/monitoring/PerformanceChart.tsx
- legacybridge/src/components/monitoring/SystemHealthCard.tsx
- legacybridge/src/components/monitoring/index.ts
- legacybridge/src/components/ui/alert-dialog.tsx
- legacybridge/src/components/ui/badge.tsx
- legacybridge/src/components/ui/button.tsx
- legacybridge/src/components/ui/card.tsx
- legacybridge/src/components/ui/dropdown-menu.tsx
- legacybridge/src/components/ui/input.tsx
- legacybridge/src/components/ui/label.tsx
- legacybridge/src/components/ui/progress.tsx
- legacybridge/src/components/ui/separator.tsx
- legacybridge/src/components/ui/switch.tsx
- legacybridge/src/components/ui/tabs.tsx
- legacybridge/src/components/ui/tooltip.tsx
- legacybridge/src/enterprise/admin/AdminDashboard.tsx
- legacybridge/src/enterprise/api/enterprise-api.ts
- legacybridge/src/enterprise/audit/audit-logger.ts
- legacybridge/src/enterprise/auth/authentication.ts
- legacybridge/src/enterprise/auth/rbac.ts
- legacybridge/src/enterprise/config/kubernetes-deployment.yaml
- legacybridge/src/enterprise/services/tenant-conversion-service.ts
- legacybridge/src/enterprise/tenancy/tenant-context.ts
- legacybridge/src/hooks/useDebounce.ts
- legacybridge/src/hooks/useMonitoringWebSocket.ts
- legacybridge/src/lib/download-service.ts
- legacybridge/src/lib/error-logger.ts
- legacybridge/src/lib/monitoring/alert-manager.ts
- legacybridge/src/lib/monitoring/websocket-server.ts
- legacybridge/src/lib/sanitizer.ts
- legacybridge/src/lib/stores/files.ts
- legacybridge/src/lib/tauri-api.ts
- legacybridge/src/lib/unified-error-api.ts
- legacybridge/src/lib/utils.ts
- legacybridge/src/middleware.ts
- legacybridge/src/styles/monitoring.css
- legacybridge/src/types/errors.ts
- legacybridge/src/types/index.ts
- legacybridge/src/types/monitoring.ts
- legacybridge/src-tauri/security.config.json
- legacybridge/src-tauri/tauri.conf.json
- legacybridge/test_32bit_compatibility.c
- legacybridge/test_dll.c
- legacybridge/test_dll.py
- legacybridge/tests/accessibility/accessibility-tests.spec.ts
- legacybridge/tests/accessibility/component-accessibility.test.tsx
- legacybridge/tests/chaos/chaos-engineering-tests.spec.ts
- legacybridge/tests/framework/test-automation-framework.ts
- legacybridge/tests/framework/types.ts
- legacybridge/tests/integration/file-conversion-flow.test.tsx
- legacybridge/tests/load/k6-load-test.js
- legacybridge/tests/mocks/fileMock.js
- legacybridge/tests/mocks/styleMock.js
- legacybridge/tests/performance/performance-regression.test.ts
- legacybridge/tests/run-all-tests.js
- legacybridge/tests/security/security-test-suite.ts
- legacybridge/tests/setup/global-setup.ts
- legacybridge/tests/setup/global-teardown.ts
- legacybridge/tests/setup/jest.setup.js
- legacybridge/tests/unit/components/ConversionProgress.test.tsx
- legacybridge/tests/unit/components/DragDropZone.test.tsx
- legacybridge/tests/unit/components/ErrorBoundary.test.tsx
- legacybridge/tests/unit/components/MarkdownPreview.test.tsx
- legacybridge/tests/unit/components/MonitoringDashboard.test.tsx
- legacybridge/tests/unit/stores/files.test.ts
- legacybridge/tests/visual-regression/visual-regression-tests.spec.ts
- legacybridge/tsconfig.json
- tests/load/k6-load-test.js
- validate_performance_claims.py


## Code Changes (2025-07-25 22:34:24)

### Added Files
- COMPREHENSIVE_SECURITY_ANALYSIS.json
- docker-compose.yml
- docs/api/interactive-explorer/index.html
- docs/troubleshooting/interactive-wizard/index.html
- helm/legacybridge/Chart.yaml
- helm/legacybridge/values-production.yaml
- helm/legacybridge/values.yaml
- k8s/configmap.yaml
- k8s/deployment-optimized.yaml
- k8s/deployment.yaml
- k8s/ingress.yaml
- k8s/monitoring-stack.yaml
- k8s/monitoring.yaml
- legacybridge/.eslintrc.json
- legacybridge/ENTERPRISE_PACKAGE/MANIFEST.json
- legacybridge/ENTERPRISE_PACKAGE/examples/other/test_dll.c
- legacybridge/ENTERPRISE_PACKAGE/examples/other/test_dll.py
- legacybridge/ENTERPRISE_PACKAGE/include/legacybridge.h
- legacybridge/components.json
- legacybridge/deployment_package/include/legacybridge.h
- legacybridge/frontend-integration-example.ts
- legacybridge/include/legacybridge.h
- legacybridge/jest.config.js
- legacybridge/next.config.ts
- legacybridge/package-lock.json
- legacybridge/package.json
- legacybridge/perf_test.c
- legacybridge/playwright.config.ts
- legacybridge/src/__tests__/security/xss.test.tsx
- legacybridge/src/app/api/monitoring/alerts/route.ts
- legacybridge/src/app/api/monitoring/alerts/slack/route.ts
- legacybridge/src/app/api/monitoring/metrics/route.ts
- legacybridge/src/app/api/monitoring/prometheus/route.ts
- legacybridge/src/app/demo/page.tsx
- legacybridge/src/app/globals-backup.css
- legacybridge/src/app/globals.css
- legacybridge/src/app/home-page.tsx
- legacybridge/src/app/layout.tsx
- legacybridge/src/app/monitoring/page.tsx
- legacybridge/src/app/page.tsx
- legacybridge/src/components/ConversionProgress.tsx
- legacybridge/src/components/DiffView.tsx
- legacybridge/src/components/DownloadManager.tsx
- legacybridge/src/components/DragDropZone.tsx
- legacybridge/src/components/ErrorBoundary.tsx
- legacybridge/src/components/ErrorDisplay.tsx
- legacybridge/src/components/ErrorLogViewer.tsx
- legacybridge/src/components/LoadingAnimation.tsx
- legacybridge/src/components/MarkdownPreview.tsx
- legacybridge/src/components/PreviewPanel.tsx
- legacybridge/src/components/SuccessAnimation.tsx
- legacybridge/src/components/SyntaxHighlighter.tsx
- legacybridge/src/components/ThemeProvider.tsx
- legacybridge/src/components/UnifiedErrorDisplay.tsx
- legacybridge/src/components/layout/Footer.tsx
- legacybridge/src/components/layout/Header.tsx
- legacybridge/src/components/layout/MainLayout.tsx
- legacybridge/src/components/monitoring/BuildProgressRing.tsx
- legacybridge/src/components/monitoring/EnhancedFunctionCallMatrix.tsx
- legacybridge/src/components/monitoring/FunctionCallMatrix.tsx
- legacybridge/src/components/monitoring/LogStreamViewer.tsx
- legacybridge/src/components/monitoring/MonitoringDashboard.tsx
- legacybridge/src/components/monitoring/PerformanceChart.tsx
- legacybridge/src/components/monitoring/SystemHealthCard.tsx
- legacybridge/src/components/monitoring/index.ts
- legacybridge/src/components/ui/alert-dialog.tsx
- legacybridge/src/components/ui/badge.tsx
- legacybridge/src/components/ui/button.tsx
- legacybridge/src/components/ui/card.tsx
- legacybridge/src/components/ui/dropdown-menu.tsx
- legacybridge/src/components/ui/input.tsx
- legacybridge/src/components/ui/label.tsx
- legacybridge/src/components/ui/progress.tsx
- legacybridge/src/components/ui/separator.tsx
- legacybridge/src/components/ui/switch.tsx
- legacybridge/src/components/ui/tabs.tsx
- legacybridge/src/components/ui/tooltip.tsx
- legacybridge/src/enterprise/admin/AdminDashboard.tsx
- legacybridge/src/enterprise/api/enterprise-api.ts
- legacybridge/src/enterprise/audit/audit-logger.ts
- legacybridge/src/enterprise/auth/authentication.ts
- legacybridge/src/enterprise/auth/rbac.ts
- legacybridge/src/enterprise/config/kubernetes-deployment.yaml
- legacybridge/src/enterprise/services/tenant-conversion-service.ts
- legacybridge/src/enterprise/tenancy/tenant-context.ts
- legacybridge/src/hooks/useDebounce.ts
- legacybridge/src/hooks/useMonitoringWebSocket.ts
- legacybridge/src/lib/download-service.ts
- legacybridge/src/lib/error-logger.ts
- legacybridge/src/lib/monitoring/alert-manager.ts
- legacybridge/src/lib/monitoring/websocket-server.ts
- legacybridge/src/lib/sanitizer.ts
- legacybridge/src/lib/stores/files.ts
- legacybridge/src/lib/tauri-api.ts
- legacybridge/src/lib/unified-error-api.ts
- legacybridge/src/lib/utils.ts
- legacybridge/src/middleware.ts
- legacybridge/src/styles/monitoring.css
- legacybridge/src/types/errors.ts
- legacybridge/src/types/index.ts
- legacybridge/src/types/monitoring.ts
- legacybridge/src-tauri/security.config.json
- legacybridge/src-tauri/tauri.conf.json
- legacybridge/test_32bit_compatibility.c
- legacybridge/test_dll.c
- legacybridge/test_dll.py
- legacybridge/tests/accessibility/accessibility-tests.spec.ts
- legacybridge/tests/accessibility/component-accessibility.test.tsx
- legacybridge/tests/chaos/chaos-engineering-tests.spec.ts
- legacybridge/tests/framework/test-automation-framework.ts
- legacybridge/tests/framework/types.ts
- legacybridge/tests/integration/file-conversion-flow.test.tsx
- legacybridge/tests/load/k6-load-test.js
- legacybridge/tests/mocks/fileMock.js
- legacybridge/tests/mocks/styleMock.js
- legacybridge/tests/performance/performance-regression.test.ts
- legacybridge/tests/run-all-tests.js
- legacybridge/tests/security/security-test-suite.ts
- legacybridge/tests/setup/global-setup.ts
- legacybridge/tests/setup/global-teardown.ts
- legacybridge/tests/setup/jest.setup.js
- legacybridge/tests/unit/components/ConversionProgress.test.tsx
- legacybridge/tests/unit/components/DragDropZone.test.tsx
- legacybridge/tests/unit/components/ErrorBoundary.test.tsx
- legacybridge/tests/unit/components/MarkdownPreview.test.tsx
- legacybridge/tests/unit/components/MonitoringDashboard.test.tsx
- legacybridge/tests/unit/stores/files.test.ts
- legacybridge/tests/visual-regression/visual-regression-tests.spec.ts
- legacybridge/tsconfig.json
- tests/load/k6-load-test.js
- validate_performance_claims.py


## Code Changes (2025-07-25 22:31:39)

### Added Files
- COMPREHENSIVE_SECURITY_ANALYSIS.json
- docker-compose.yml
- docs/api/interactive-explorer/index.html
- docs/troubleshooting/interactive-wizard/index.html
- helm/legacybridge/Chart.yaml
- helm/legacybridge/values-production.yaml
- helm/legacybridge/values.yaml
- k8s/configmap.yaml
- k8s/deployment-optimized.yaml
- k8s/deployment.yaml
- k8s/ingress.yaml
- k8s/monitoring-stack.yaml
- k8s/monitoring.yaml
- legacybridge/.eslintrc.json
- legacybridge/ENTERPRISE_PACKAGE/MANIFEST.json
- legacybridge/ENTERPRISE_PACKAGE/examples/other/test_dll.c
- legacybridge/ENTERPRISE_PACKAGE/examples/other/test_dll.py
- legacybridge/ENTERPRISE_PACKAGE/include/legacybridge.h
- legacybridge/components.json
- legacybridge/deployment_package/include/legacybridge.h
- legacybridge/frontend-integration-example.ts
- legacybridge/include/legacybridge.h
- legacybridge/jest.config.js
- legacybridge/next.config.ts
- legacybridge/package-lock.json
- legacybridge/package.json
- legacybridge/perf_test.c
- legacybridge/playwright.config.ts
- legacybridge/src/__tests__/security/xss.test.tsx
- legacybridge/src/app/api/monitoring/alerts/route.ts
- legacybridge/src/app/api/monitoring/alerts/slack/route.ts
- legacybridge/src/app/api/monitoring/metrics/route.ts
- legacybridge/src/app/api/monitoring/prometheus/route.ts
- legacybridge/src/app/demo/page.tsx
- legacybridge/src/app/globals-backup.css
- legacybridge/src/app/globals.css
- legacybridge/src/app/home-page.tsx
- legacybridge/src/app/layout.tsx
- legacybridge/src/app/monitoring/page.tsx
- legacybridge/src/app/page.tsx
- legacybridge/src/components/ConversionProgress.tsx
- legacybridge/src/components/DiffView.tsx
- legacybridge/src/components/DownloadManager.tsx
- legacybridge/src/components/DragDropZone.tsx
- legacybridge/src/components/ErrorBoundary.tsx
- legacybridge/src/components/ErrorDisplay.tsx
- legacybridge/src/components/ErrorLogViewer.tsx
- legacybridge/src/components/LoadingAnimation.tsx
- legacybridge/src/components/MarkdownPreview.tsx
- legacybridge/src/components/PreviewPanel.tsx
- legacybridge/src/components/SuccessAnimation.tsx
- legacybridge/src/components/SyntaxHighlighter.tsx
- legacybridge/src/components/ThemeProvider.tsx
- legacybridge/src/components/UnifiedErrorDisplay.tsx
- legacybridge/src/components/layout/Footer.tsx
- legacybridge/src/components/layout/Header.tsx
- legacybridge/src/components/layout/MainLayout.tsx
- legacybridge/src/components/monitoring/BuildProgressRing.tsx
- legacybridge/src/components/monitoring/EnhancedFunctionCallMatrix.tsx
- legacybridge/src/components/monitoring/FunctionCallMatrix.tsx
- legacybridge/src/components/monitoring/LogStreamViewer.tsx
- legacybridge/src/components/monitoring/MonitoringDashboard.tsx
- legacybridge/src/components/monitoring/PerformanceChart.tsx
- legacybridge/src/components/monitoring/SystemHealthCard.tsx
- legacybridge/src/components/monitoring/index.ts
- legacybridge/src/components/ui/alert-dialog.tsx
- legacybridge/src/components/ui/badge.tsx
- legacybridge/src/components/ui/button.tsx
- legacybridge/src/components/ui/card.tsx
- legacybridge/src/components/ui/dropdown-menu.tsx
- legacybridge/src/components/ui/input.tsx
- legacybridge/src/components/ui/label.tsx
- legacybridge/src/components/ui/progress.tsx
- legacybridge/src/components/ui/separator.tsx
- legacybridge/src/components/ui/switch.tsx
- legacybridge/src/components/ui/tabs.tsx
- legacybridge/src/components/ui/tooltip.tsx
- legacybridge/src/enterprise/admin/AdminDashboard.tsx
- legacybridge/src/enterprise/api/enterprise-api.ts
- legacybridge/src/enterprise/audit/audit-logger.ts
- legacybridge/src/enterprise/auth/authentication.ts
- legacybridge/src/enterprise/auth/rbac.ts
- legacybridge/src/enterprise/config/kubernetes-deployment.yaml
- legacybridge/src/enterprise/services/tenant-conversion-service.ts
- legacybridge/src/enterprise/tenancy/tenant-context.ts
- legacybridge/src/hooks/useDebounce.ts
- legacybridge/src/hooks/useMonitoringWebSocket.ts
- legacybridge/src/lib/download-service.ts
- legacybridge/src/lib/error-logger.ts
- legacybridge/src/lib/monitoring/alert-manager.ts
- legacybridge/src/lib/monitoring/websocket-server.ts
- legacybridge/src/lib/sanitizer.ts
- legacybridge/src/lib/stores/files.ts
- legacybridge/src/lib/tauri-api.ts
- legacybridge/src/lib/unified-error-api.ts
- legacybridge/src/lib/utils.ts
- legacybridge/src/middleware.ts
- legacybridge/src/styles/monitoring.css
- legacybridge/src/types/errors.ts
- legacybridge/src/types/index.ts
- legacybridge/src/types/monitoring.ts
- legacybridge/src-tauri/security.config.json
- legacybridge/src-tauri/tauri.conf.json
- legacybridge/test_32bit_compatibility.c
- legacybridge/test_dll.c
- legacybridge/test_dll.py
- legacybridge/tests/accessibility/accessibility-tests.spec.ts
- legacybridge/tests/accessibility/component-accessibility.test.tsx
- legacybridge/tests/chaos/chaos-engineering-tests.spec.ts
- legacybridge/tests/framework/test-automation-framework.ts
- legacybridge/tests/framework/types.ts
- legacybridge/tests/integration/file-conversion-flow.test.tsx
- legacybridge/tests/load/k6-load-test.js
- legacybridge/tests/mocks/fileMock.js
- legacybridge/tests/mocks/styleMock.js
- legacybridge/tests/performance/performance-regression.test.ts
- legacybridge/tests/run-all-tests.js
- legacybridge/tests/security/security-test-suite.ts
- legacybridge/tests/setup/global-setup.ts
- legacybridge/tests/setup/global-teardown.ts
- legacybridge/tests/setup/jest.setup.js
- legacybridge/tests/unit/components/ConversionProgress.test.tsx
- legacybridge/tests/unit/components/DragDropZone.test.tsx
- legacybridge/tests/unit/components/ErrorBoundary.test.tsx
- legacybridge/tests/unit/components/MarkdownPreview.test.tsx
- legacybridge/tests/unit/components/MonitoringDashboard.test.tsx
- legacybridge/tests/unit/stores/files.test.ts
- legacybridge/tests/visual-regression/visual-regression-tests.spec.ts
- legacybridge/tsconfig.json
- tests/load/k6-load-test.js
- validate_performance_claims.py


## Current Work Focus
[Describe what you're currently working on]

## Recent Changes
[List recent changes to the project]

## Next Steps
[List next steps for the project]

## Code Changes (2025-07-25 22:27:13)

### Added Files
- COMPREHENSIVE_SECURITY_ANALYSIS.json
- docker-compose.yml
- docs/api/interactive-explorer/index.html
- docs/troubleshooting/interactive-wizard/index.html
- helm/legacybridge/Chart.yaml
- helm/legacybridge/values-production.yaml
- helm/legacybridge/values.yaml
- k8s/configmap.yaml
- k8s/deployment-optimized.yaml
- k8s/deployment.yaml
- k8s/ingress.yaml
- k8s/monitoring-stack.yaml
- k8s/monitoring.yaml
- legacybridge/.eslintrc.json
- legacybridge/ENTERPRISE_PACKAGE/MANIFEST.json
- legacybridge/ENTERPRISE_PACKAGE/examples/other/test_dll.c
- legacybridge/ENTERPRISE_PACKAGE/examples/other/test_dll.py
- legacybridge/ENTERPRISE_PACKAGE/include/legacybridge.h
- legacybridge/components.json
- legacybridge/deployment_package/include/legacybridge.h
- legacybridge/frontend-integration-example.ts
- legacybridge/include/legacybridge.h
- legacybridge/jest.config.js
- legacybridge/next.config.ts
- legacybridge/package-lock.json
- legacybridge/package.json
- legacybridge/perf_test.c
- legacybridge/playwright.config.ts
- legacybridge/src/__tests__/security/xss.test.tsx
- legacybridge/src/app/api/monitoring/alerts/route.ts
- legacybridge/src/app/api/monitoring/alerts/slack/route.ts
- legacybridge/src/app/api/monitoring/metrics/route.ts
- legacybridge/src/app/api/monitoring/prometheus/route.ts
- legacybridge/src/app/demo/page.tsx
- legacybridge/src/app/globals-backup.css
- legacybridge/src/app/globals.css
- legacybridge/src/app/home-page.tsx
- legacybridge/src/app/layout.tsx
- legacybridge/src/app/monitoring/page.tsx
- legacybridge/src/app/page.tsx
- legacybridge/src/components/ConversionProgress.tsx
- legacybridge/src/components/DiffView.tsx
- legacybridge/src/components/DownloadManager.tsx
- legacybridge/src/components/DragDropZone.tsx
- legacybridge/src/components/ErrorBoundary.tsx
- legacybridge/src/components/ErrorDisplay.tsx
- legacybridge/src/components/ErrorLogViewer.tsx
- legacybridge/src/components/LoadingAnimation.tsx
- legacybridge/src/components/MarkdownPreview.tsx
- legacybridge/src/components/PreviewPanel.tsx
- legacybridge/src/components/SuccessAnimation.tsx
- legacybridge/src/components/SyntaxHighlighter.tsx
- legacybridge/src/components/ThemeProvider.tsx
- legacybridge/src/components/UnifiedErrorDisplay.tsx
- legacybridge/src/components/layout/Footer.tsx
- legacybridge/src/components/layout/Header.tsx
- legacybridge/src/components/layout/MainLayout.tsx
- legacybridge/src/components/monitoring/BuildProgressRing.tsx
- legacybridge/src/components/monitoring/EnhancedFunctionCallMatrix.tsx
- legacybridge/src/components/monitoring/FunctionCallMatrix.tsx
- legacybridge/src/components/monitoring/LogStreamViewer.tsx
- legacybridge/src/components/monitoring/MonitoringDashboard.tsx
- legacybridge/src/components/monitoring/PerformanceChart.tsx
- legacybridge/src/components/monitoring/SystemHealthCard.tsx
- legacybridge/src/components/monitoring/index.ts
- legacybridge/src/components/ui/alert-dialog.tsx
- legacybridge/src/components/ui/badge.tsx
- legacybridge/src/components/ui/button.tsx
- legacybridge/src/components/ui/card.tsx
- legacybridge/src/components/ui/dropdown-menu.tsx
- legacybridge/src/components/ui/input.tsx
- legacybridge/src/components/ui/label.tsx
- legacybridge/src/components/ui/progress.tsx
- legacybridge/src/components/ui/separator.tsx
- legacybridge/src/components/ui/switch.tsx
- legacybridge/src/components/ui/tabs.tsx
- legacybridge/src/components/ui/tooltip.tsx
- legacybridge/src/enterprise/admin/AdminDashboard.tsx
- legacybridge/src/enterprise/api/enterprise-api.ts
- legacybridge/src/enterprise/audit/audit-logger.ts
- legacybridge/src/enterprise/auth/authentication.ts
- legacybridge/src/enterprise/auth/rbac.ts
- legacybridge/src/enterprise/config/kubernetes-deployment.yaml
- legacybridge/src/enterprise/services/tenant-conversion-service.ts
- legacybridge/src/enterprise/tenancy/tenant-context.ts
- legacybridge/src/hooks/useDebounce.ts
- legacybridge/src/hooks/useMonitoringWebSocket.ts
- legacybridge/src/lib/download-service.ts
- legacybridge/src/lib/error-logger.ts
- legacybridge/src/lib/monitoring/alert-manager.ts
- legacybridge/src/lib/monitoring/websocket-server.ts
- legacybridge/src/lib/sanitizer.ts
- legacybridge/src/lib/stores/files.ts
- legacybridge/src/lib/tauri-api.ts
- legacybridge/src/lib/unified-error-api.ts
- legacybridge/src/lib/utils.ts
- legacybridge/src/middleware.ts
- legacybridge/src/styles/monitoring.css
- legacybridge/src/types/errors.ts
- legacybridge/src/types/index.ts
- legacybridge/src/types/monitoring.ts
- legacybridge/src-tauri/security.config.json
- legacybridge/src-tauri/tauri.conf.json
- legacybridge/test_32bit_compatibility.c
- legacybridge/test_dll.c
- legacybridge/test_dll.py
- legacybridge/tests/accessibility/accessibility-tests.spec.ts
- legacybridge/tests/accessibility/component-accessibility.test.tsx
- legacybridge/tests/chaos/chaos-engineering-tests.spec.ts
- legacybridge/tests/framework/test-automation-framework.ts
- legacybridge/tests/framework/types.ts
- legacybridge/tests/integration/file-conversion-flow.test.tsx
- legacybridge/tests/load/k6-load-test.js
- legacybridge/tests/mocks/fileMock.js
- legacybridge/tests/mocks/styleMock.js
- legacybridge/tests/performance/performance-regression.test.ts
- legacybridge/tests/run-all-tests.js
- legacybridge/tests/security/security-test-suite.ts
- legacybridge/tests/setup/global-setup.ts
- legacybridge/tests/setup/global-teardown.ts
- legacybridge/tests/setup/jest.setup.js
- legacybridge/tests/unit/components/ConversionProgress.test.tsx
- legacybridge/tests/unit/components/DragDropZone.test.tsx
- legacybridge/tests/unit/components/ErrorBoundary.test.tsx
- legacybridge/tests/unit/components/MarkdownPreview.test.tsx
- legacybridge/tests/unit/components/MonitoringDashboard.test.tsx
- legacybridge/tests/unit/stores/files.test.ts
- legacybridge/tests/visual-regression/visual-regression-tests.spec.ts
- legacybridge/tsconfig.json
- tests/load/k6-load-test.js
- validate_performance_claims.py

