# Start MCP Server for Windows (PowerShell)

# Set default environment variables
if (-not $env:NODE_ENV) { $env:NODE_ENV = "development" }
if (-not $env:MCP_PORT) { $env:MCP_PORT = "3030" }
if (-not $env:LOG_LEVEL) { $env:LOG_LEVEL = "info" }

Write-Host "Checking dependencies..."

# Check for Node.js
try {
    $nodeVersion = node -v
    Write-Host "Node.js version: $nodeVersion"
} catch {
    Write-Host "Node.js is not installed. Please install Node.js 16 or later."
    exit 1
}

# Check for npm
try {
    $npmVersion = npm -v
    Write-Host "npm version: $npmVersion"
} catch {
    Write-Host "npm is not installed. Please install npm."
    exit 1
}

# Check for LibreOffice (optional, for legacy format support)
try {
    $libreOffice = Get-Command soffice -ErrorAction Stop
    Write-Host "LibreOffice is installed. Legacy format conversion will be available."
    $env:ENABLE_DOC = "true"
    $env:ENABLE_WORDPERFECT = "true"
} catch {
    Write-Host "LibreOffice is not installed. Legacy format conversion will be disabled."
    $env:ENABLE_DOC = "false"
    $env:ENABLE_WORDPERFECT = "false"
}

# Check for Pandoc (optional, for legacy format support)
try {
    $pandoc = Get-Command pandoc -ErrorAction Stop
    Write-Host "Pandoc is installed."
} catch {
    if ($env:ENABLE_DOC -eq "true" -or $env:ENABLE_WORDPERFECT -eq "true") {
        Write-Host "Pandoc is not installed. Legacy format conversion will be disabled."
        $env:ENABLE_DOC = "false"
        $env:ENABLE_WORDPERFECT = "false"
    }
}

# Create required directories
$directories = @("logs", "uploads", "output", "temp")
foreach ($dir in $directories) {
    if (-not (Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir | Out-Null
        Write-Host "Created directory: $dir"
    }
}

# Install dependencies if needed
if (-not (Test-Path "node_modules")) {
    Write-Host "Installing dependencies..."
    npm install
}

# Build TypeScript if needed
if (-not (Test-Path "dist")) {
    Write-Host "Building TypeScript..."
    npm run build
}

# Start the server
Write-Host "Starting MCP Server on port $env:MCP_PORT..."
node dist/mcp-server/index.js