HTTP/1.1 500 Internal Server Error
Content-Security-Policy: default-src 'self';base-uri 'self';font-src 'self' https: data:;form-action 'self';frame-ancestors 'self';img-src 'self' data:;object-src 'none';script-src 'self';script-src-attr 'none';style-src 'self' https: 'unsafe-inline';upgrade-insecure-requests
Cross-Origin-Opener-Policy: same-origin
Cross-Origin-Resource-Policy: same-origin
Origin-Agent-Cluster: ?1
Referrer-Policy: no-referrer
Strict-Transport-Security: max-age=31536000; includeSubDomains
X-Content-Type-Options: nosniff
X-DNS-Prefetch-Control: off
X-Download-Options: noopen
X-Frame-Options: SAMEORIGIN
X-Permitted-Cross-Domain-Policies: none
X-XSS-Protection: 0
Access-Control-Allow-Origin: *
RateLimit-Policy: 100;w=900
RateLimit-Limit: 100
RateLimit-Remaining: 98
RateLimit-Reset: 833
Content-Type: application/json; charset=utf-8
Content-Length: 127
ETag: W/"7f-b2AIElVfLJxQgg065lJD1C7pLNs"
Date: Wed, 30 Jul 2025 01:48:21 GMT
Connection: keep-alive
Keep-Alive: timeout=5

{"status":"error","errorCode":"INTERNAL_ERROR","message":"An unexpected error occurred","timestamp":"2025-07-30T01:48:21.445Z"}