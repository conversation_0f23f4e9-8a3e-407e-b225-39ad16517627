# ===== Node.js / Next.js =====
# Dependencies
node_modules/
.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# Testing
coverage/
.nyc_output/

# Next.js
.next/
out/
dist/

# Production
build/

# Debug logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*
lerna-debug.log*

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env*.local

# Vercel
.vercel

# TypeScript
*.tsbuildinfo
next-env.d.ts

# ===== Rust / Tauri =====
# Rust build artifacts
target/
Cargo.lock
**/*.rs.bk

# Tauri
src-tauri/target/
src-tauri/Cargo.lock
src-tauri/WixTools/
src-tauri/.cargo/

# ===== IDE / Editor =====
# Visual Studio Code
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
!.vscode/*.code-snippets
.history/
*.vsix

# IntelliJ IDEA
.idea/
*.iml
*.iws
*.ipr
out/

# Eclipse
.project
.classpath
.c9/
*.launch
.settings/

# Sublime Text
*.sublime-workspace
*.sublime-project

# Vim
[._]*.s[a-v][a-z]
[._]*.sw[a-p]
[._]s[a-rt-v][a-z]
[._]ss[a-gi-z]
[._]sw[a-p]
Session.vim
Sessionx.vim
.netrwhist
*~
tags
[._]*.un~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*
.org-id-locations
*_archive
*_flymake.*
/eshell/history
/eshell/lastdir
/elpa/
*.rel
/auto/
.cask/
dist/
flycheck_*.el
.projectile
.dir-locals.el
/network-security.data

# ===== OS Files =====
# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ===== Package Managers =====
# npm
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
.npm
.eslintcache
.node_repl_history
*.tgz
.yarn-integrity

# pnpm
pnpm-lock.yaml
.pnpm-store/

# Bower
bower_components/

# ===== Build Outputs =====
# General
dist/
dist-ssr/
build/
out/
*.out
*.log
*.pid
*.seed
*.pid.lock

# Webpack
.cache/
.parcel-cache/
.webpack/

# Rollup
.rollup.cache/

# Vite
vite.config.js.timestamp-*
vite.config.ts.timestamp-*

# ===== Testing =====
# Jest
coverage/
.jest/

# Playwright
test-results/
playwright-report/
playwright/.cache/

# Vitest
coverage/
.vitest/

# ===== Temporary Files =====
*.tmp
*.temp
*.swp
*.swo
*~
.tmp/
.temp/
tmp/
temp/

# ===== Logs =====
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# ===== Database =====
*.db
*.sqlite
*.sqlite3
*.db-journal

# ===== Backup Files =====
*.bak
*.backup
*.old
*.orig

# ===== Compressed Files =====
*.zip
*.tar
*.tar.gz
*.tgz
*.rar
*.7z
*.dmg
*.iso

# ===== Binary Files =====
*.exe
*.dll
*.so
*.dylib
*.bin
*.app

# ===== Generated Documentation =====
docs/_build/
docs/.vuepress/dist/
site/

# ===== Certificates =====
*.pem
*.key
*.crt
*.p12
*.pfx

# ===== Project Specific =====
# Tauri build outputs
src-tauri/target/
src-tauri/Cargo.lock
src-tauri/WixTools/

# Next.js build outputs
.next/
out/
.vercel/

# Test outputs
test-results/
playwright-report/
coverage/

# Temporary conversion files
*.tmp.rtf
*.tmp.md
*.conversion.log

# User-specific configuration
config.local.json
settings.local.json

# Development databases
dev.db
test.db

# Local environment overrides
.env.local
.env.*.local

# OS thumbnails and metadata
.DS_Store
Thumbs.db
desktop.ini

# Editor backup files
*~
*.swp
*.swo
.idea/
.vscode/
*.sublime-*

# npm/yarn/pnpm logs and caches
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*
.npm/
.yarn/
.pnpm-store/

# Build artifacts
dist/
build/
out/
*.tsbuildinfo

# Rust artifacts
target/
Cargo.lock
**/*.rs.bk
*.pdb