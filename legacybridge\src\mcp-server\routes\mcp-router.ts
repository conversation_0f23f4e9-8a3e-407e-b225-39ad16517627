// MCP Server Router
// Implements the Model Context Protocol (MCP) routes

import { Router, Request, Response, NextFunction } from 'express';
import multer from 'multer';
import { v4 as uuidv4 } from 'uuid';
import path from 'path';
import fs from 'fs';
import { MCPConfig } from '../utils/mcp-config';
import { MCPLogger } from '../utils/mcp-logger';
import { MCPCache } from '../services/mcp-cache';
import { ValidationError, NotFoundError, ConversionError } from '../middleware/mcp-error-handler';
import { MCPConversionService } from '../services/mcp-conversion-service';
import { MCPBatchService } from '../services/mcp-batch-service';
import { MCPLegacyFormatService } from '../services/mcp-legacy-format-service';

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(process.cwd(), 'uploads');
    
    // Create uploads directory if it doesn't exist
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniquePrefix = `${Date.now()}-${uuidv4()}`;
    cb(null, `${uniquePrefix}-${file.originalname}`);
  },
});

const fileFilter = (req: Request, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  // Accept RTF, Markdown, DOC, and WordPerfect files
  const allowedMimeTypes = [
    'application/rtf',
    'text/rtf',
    'text/markdown',
    'text/plain',
    'application/msword',           // DOC
    'application/vnd.ms-word',      // DOC
    'application/vnd.wordperfect',  // WordPerfect
    'application/octet-stream',     // Generic binary
  ];
  
  if (allowedMimeTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new ValidationError(`Unsupported file type: ${file.mimetype}`));
  }
};

const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 100 * 1024 * 1024, // 100MB
  },
});

// MCP Router
export const MCPRouter = (config: MCPConfig, cache: MCPCache, logger: MCPLogger) => {
  const router = Router();
  const conversionService = new MCPConversionService(config, cache, logger);
  const batchService = new MCPBatchService(config, conversionService, logger);
  const legacyFormatService = new MCPLegacyFormatService(config, logger);

  // MCP Protocol Endpoints
  
  // List available tools
  router.get('/tools', (req: Request, res: Response) => {
    const tools = [
      {
        name: 'convert_rtf_to_markdown',
        description: 'Convert RTF content to Markdown format',
        parameters: {
          type: 'object',
          properties: {
            content: {
              type: 'string',
              description: 'RTF content to convert'
            },
            options: {
              type: 'object',
              properties: {
                preserveFormatting: {
                  type: 'boolean',
                  description: 'Whether to preserve formatting'
                },
                includeMetadata: {
                  type: 'boolean',
                  description: 'Whether to include metadata in the output'
                }
              }
            }
          },
          required: ['content']
        }
      },
      {
        name: 'convert_markdown_to_rtf',
        description: 'Convert Markdown content to RTF format',
        parameters: {
          type: 'object',
          properties: {
            content: {
              type: 'string',
              description: 'Markdown content to convert'
            },
            options: {
              type: 'object',
              properties: {
                template: {
                  type: 'string',
                  description: 'Template to use for conversion'
                },
                customStyles: {
                  type: 'object',
                  description: 'Custom styles to apply'
                },
                preserveFormatting: {
                  type: 'boolean',
                  description: 'Whether to preserve formatting'
                },
                includeMetadata: {
                  type: 'boolean',
                  description: 'Whether to include metadata in the output'
                }
              }
            }
          },
          required: ['content']
        }
      },
      {
        name: 'batch_convert',
        description: 'Convert multiple files in a batch',
        parameters: {
          type: 'object',
          properties: {
            files: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  content: {
                    type: 'string',
                    description: 'File content to convert'
                  },
                  fileName: {
                    type: 'string',
                    description: 'Original file name'
                  },
                  conversionType: {
                    type: 'string',
                    enum: ['rtf_to_md', 'md_to_rtf'],
                    description: 'Type of conversion to perform'
                  },
                  options: {
                    type: 'object',
                    description: 'Conversion options'
                  }
                },
                required: ['content', 'fileName', 'conversionType']
              }
            },
            batchOptions: {
              type: 'object',
              properties: {
                priority: {
                  type: 'string',
                  enum: ['low', 'normal', 'high'],
                  description: 'Batch priority'
                },
                callbackUrl: {
                  type: 'string',
                  description: 'URL to call when batch is complete'
                }
              }
            }
          },
          required: ['files']
        }
      }
    ];
    
    // Add legacy format tools if enabled
    if (config.legacyFormats.enableDOC) {
      tools.push({
        name: 'convert_doc_to_markdown',
        description: 'Convert DOC content to Markdown format',
        parameters: {
          type: 'object',
          properties: {
            content: {
              type: 'string',
              description: 'DOC content to convert (base64 encoded)'
            },
            options: {
              type: 'object',
              properties: {
                preserveFormatting: {
                  type: 'boolean',
                  description: 'Whether to preserve formatting'
                },
                includeMetadata: {
                  type: 'boolean',
                  description: 'Whether to include metadata in the output'
                }
              }
            }
          },
          required: ['content']
        }
      });
    }
    
    if (config.legacyFormats.enableWordPerfect) {
      tools.push({
        name: 'convert_wordperfect_to_markdown',
        description: 'Convert WordPerfect content to Markdown format',
        parameters: {
          type: 'object',
          properties: {
            content: {
              type: 'string',
              description: 'WordPerfect content to convert (base64 encoded)'
            },
            options: {
              type: 'object',
              properties: {
                preserveFormatting: {
                  type: 'boolean',
                  description: 'Whether to preserve formatting'
                },
                includeMetadata: {
                  type: 'boolean',
                  description: 'Whether to include metadata in the output'
                }
              }
            }
          },
          required: ['content']
        }
      });
    }
    
    res.json({
      tools
    });
  });

  // Execute a tool
  router.post('/tools/execute', async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { tool, parameters } = req.body;
      
      if (!tool) {
        throw new ValidationError('Tool name is required');
      }
      
      if (!parameters) {
        throw new ValidationError('Tool parameters are required');
      }
      
      let result;
      
      switch (tool) {
        case 'convert_rtf_to_markdown':
          result = await conversionService.convertRtfToMarkdown(
            parameters.content,
            parameters.options
          );
          break;
          
        case 'convert_markdown_to_rtf':
          result = await conversionService.convertMarkdownToRtf(
            parameters.content,
            parameters.options
          );
          break;
          
        case 'batch_convert':
          result = await batchService.submitBatch(
            parameters.files,
            parameters.batchOptions
          );
          break;
          
        case 'convert_doc_to_markdown':
          if (!config.legacyFormats.enableDOC) {
            throw new ValidationError('DOC conversion is not enabled');
          }
          result = await legacyFormatService.convertDocToMarkdown(
            parameters.content,
            parameters.options
          );
          break;
          
        case 'convert_wordperfect_to_markdown':
          if (!config.legacyFormats.enableWordPerfect) {
            throw new ValidationError('WordPerfect conversion is not enabled');
          }
          result = await legacyFormatService.convertWordPerfectToMarkdown(
            parameters.content,
            parameters.options
          );
          break;
          
        default:
          throw new NotFoundError(`Tool not found: ${tool}`);
      }
      
      res.json({
        status: 'success',
        result
      });
    } catch (error) {
      next(error);
    }
  });

  // File upload endpoint
  router.post('/upload', upload.single('file'), async (req: Request, res: Response, next: NextFunction) => {
    try {
      if (!req.file) {
        throw new ValidationError('No file uploaded');
      }
      
      const { conversionType, options } = req.body;
      
      if (!conversionType) {
        throw new ValidationError('Conversion type is required');
      }
      
      let result;
      const filePath = req.file.path;
      const fileName = req.file.originalname;
      const fileSize = req.file.size;
      
      // Determine conversion type based on file extension and requested conversion
      const fileExt = path.extname(fileName).toLowerCase();
      
      switch (conversionType) {
        case 'rtf_to_md':
          if (fileExt !== '.rtf') {
            throw new ValidationError('File must be RTF format for rtf_to_md conversion');
          }
          result = await conversionService.convertRtfFileToMarkdown(filePath, JSON.parse(options || '{}'));
          break;
          
        case 'md_to_rtf':
          if (fileExt !== '.md' && fileExt !== '.markdown' && fileExt !== '.txt') {
            throw new ValidationError('File must be Markdown format for md_to_rtf conversion');
          }
          result = await conversionService.convertMarkdownFileToRtf(filePath, JSON.parse(options || '{}'));
          break;
          
        case 'doc_to_md':
          if (!config.legacyFormats.enableDOC) {
            throw new ValidationError('DOC conversion is not enabled');
          }
          if (fileExt !== '.doc' && fileExt !== '.docx') {
            throw new ValidationError('File must be DOC format for doc_to_md conversion');
          }
          result = await legacyFormatService.convertDocFileToMarkdown(filePath, JSON.parse(options || '{}'));
          break;
          
        case 'wp_to_md':
          if (!config.legacyFormats.enableWordPerfect) {
            throw new ValidationError('WordPerfect conversion is not enabled');
          }
          if (fileExt !== '.wpd' && fileExt !== '.wp') {
            throw new ValidationError('File must be WordPerfect format for wp_to_md conversion');
          }
          result = await legacyFormatService.convertWordPerfectFileToMarkdown(filePath, JSON.parse(options || '{}'));
          break;
          
        default:
          throw new ValidationError(`Unsupported conversion type: ${conversionType}`);
      }
      
      res.json({
        status: 'success',
        fileName,
        fileSize,
        result
      });
    } catch (error) {
      next(error);
    }
  });

  // Batch status endpoint
  router.get('/batch/:batchId', async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { batchId } = req.params;
      
      if (!batchId) {
        throw new ValidationError('Batch ID is required');
      }
      
      const status = await batchService.getBatchStatus(batchId);
      
      res.json({
        status: 'success',
        batchStatus: status
      });
    } catch (error) {
      next(error);
    }
  });

  // Cancel batch endpoint
  router.post('/batch/:batchId/cancel', async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { batchId } = req.params;
      
      if (!batchId) {
        throw new ValidationError('Batch ID is required');
      }
      
      await batchService.cancelBatch(batchId);
      
      res.json({
        status: 'success',
        message: `Batch ${batchId} cancelled`
      });
    } catch (error) {
      next(error);
    }
  });

  // List available templates
  router.get('/templates', async (req: Request, res: Response, next: NextFunction) => {
    try {
      const templates = await conversionService.listTemplates();
      
      res.json({
        status: 'success',
        templates
      });
    } catch (error) {
      next(error);
    }
  });

  // Validate document
  router.post('/validate', async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { content, format } = req.body;
      
      if (!content) {
        throw new ValidationError('Content is required');
      }
      
      if (!format) {
        throw new ValidationError('Format is required');
      }
      
      const result = await conversionService.validateDocument(content, format);
      
      res.json({
        status: 'success',
        result
      });
    } catch (error) {
      next(error);
    }
  });

  return router;
};