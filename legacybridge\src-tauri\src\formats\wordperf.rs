// WordPerfect format parser
// Supports WordPerfect 5.1 and later versions

use std::collections::HashMap;
use crate::conversion::error::ConversionError;
use super::common::*;
use super::{FormatDetection, ConversionResult, FormatType};

/// WordPerfect file signatures
const WP_SIGNATURE: &[u8] = &[0xFF, 0x57, 0x50, 0x43]; // "WPC" signature
const WP5_SIGNATURE: &[u8] = &[0xFF, 0x57, 0x50, 0x43, 0x10, 0x00, 0x00, 0x00]; // WP 5.x
const WP6_SIGNATURE: &[u8] = &[0xFF, 0x57, 0x50, 0x43, 0x20, 0x00, 0x00, 0x00]; // WP 6.x

/// WordPerfect control codes
const WP_HARD_RETURN: u8 = 0x0A;
const WP_SOFT_RETURN: u8 = 0x0B;
const WP_TAB: u8 = 0x09;
const WP_SPACE: u8 = 0x20;
const WP_FUNCTION_PREFIX: u8 = 0xC0;
const WP_EXTENDED_PREFIX: u8 = 0xC1;

/// WordPerfect document header
#[derive(Debug)]
struct WordPerfectHeader {
    signature: [u8; 4],
    version: u32,
    file_size: u32,
    document_area_pointer: u32,
    product_type: u16,
    file_type: u16,
    major_version: u8,
    minor_version: u8,
    revision: u16,
    encryption_flag: u16,
}

impl WordPerfectHeader {
    fn parse(content: &[u8]) -> Result<Self, ConversionError> {
        if content.len() < 32 {
            return Err(ConversionError::InvalidInput("WordPerfect header too short".to_string()));
        }

        let mut signature = [0u8; 4];
        signature.copy_from_slice(&content[0..4]);

        Ok(WordPerfectHeader {
            signature,
            version: bytes_to_u32_le(&content[4..8]),
            file_size: bytes_to_u32_le(&content[8..12]),
            document_area_pointer: bytes_to_u32_le(&content[12..16]),
            product_type: bytes_to_u16_le(&content[16..18]),
            file_type: bytes_to_u16_le(&content[18..20]),
            major_version: content[20],
            minor_version: content[21],
            revision: bytes_to_u16_le(&content[22..24]),
            encryption_flag: bytes_to_u16_le(&content[24..26]),
        })
    }

    fn is_valid(&self) -> bool {
        self.signature == WP_SIGNATURE[0..4] && 
        self.major_version >= 5 && 
        self.major_version <= 12
    }

    fn version_string(&self) -> String {
        format!("{}.{}", self.major_version, self.minor_version)
    }

    fn is_encrypted(&self) -> bool {
        self.encryption_flag != 0
    }
}

/// WordPerfect formatting function
#[derive(Debug, Clone)]
struct WpFunction {
    code: u8,
    subcode: Option<u8>,
    data: Vec<u8>,
    description: String,
}

impl WpFunction {
    fn parse(data: &[u8], offset: usize) -> Result<(Self, usize), ConversionError> {
        if offset >= data.len() {
            return Err(ConversionError::InvalidInput("Function offset out of bounds".to_string()));
        }

        let code = data[offset];
        let mut consumed = 1;
        let mut subcode = None;
        let mut function_data = Vec::new();
        
        // Parse function based on code
        let description = match code {
            0xC0 => {
                // Single-byte function
                "Hard Return".to_string()
            },
            0xC1 => {
                // Multi-byte function
                if offset + 1 < data.len() {
                    subcode = Some(data[offset + 1]);
                    consumed = 2;
                    
                    // Parse function data length and content
                    if offset + 3 < data.len() {
                        let data_length = bytes_to_u16_le(&data[offset + 2..offset + 4]) as usize;
                        consumed += 2;
                        
                        if offset + consumed + data_length <= data.len() {
                            function_data = data[offset + consumed..offset + consumed + data_length].to_vec();
                            consumed += data_length;
                        }
                    }
                    
                    match subcode.unwrap_or(0) {
                        0x01 => "Bold On".to_string(),
                        0x02 => "Bold Off".to_string(),
                        0x03 => "Italic On".to_string(),
                        0x04 => "Italic Off".to_string(),
                        0x05 => "Underline On".to_string(),
                        0x06 => "Underline Off".to_string(),
                        0x10 => "Font Change".to_string(),
                        0x20 => "Margin Set".to_string(),
                        0x30 => "Tab Set".to_string(),
                        _ => format!("Unknown Function {:02X}", subcode.unwrap_or(0)),
                    }
                } else {
                    "Incomplete Function".to_string()
                }
            },
            _ => format!("Character {:02X}", code),
        };

        Ok((WpFunction {
            code,
            subcode,
            data: function_data,
            description,
        }, consumed))
    }
}

/// Detect WordPerfect format
pub fn detect_wordperfect_format(content: &[u8]) -> Result<FormatDetection, ConversionError> {
    if content.len() < 32 {
        return Ok(FormatDetection {
            format_type: FormatType::Unknown,
            confidence: 0.0,
            version: None,
            metadata: HashMap::new(),
        });
    }

    // Check for WordPerfect signature
    if !has_magic_bytes(content, WP_SIGNATURE) {
        return Ok(FormatDetection {
            format_type: FormatType::Unknown,
            confidence: 0.0,
            version: None,
            metadata: HashMap::new(),
        });
    }

    let header = WordPerfectHeader::parse(content)?;
    if !header.is_valid() {
        return Ok(FormatDetection {
            format_type: FormatType::Unknown,
            confidence: 0.3,
            version: None,
            metadata: HashMap::new(),
        });
    }

    let mut confidence = 0.8;
    let mut metadata = extract_metadata(content, "WordPerfect");

    // Check for specific version signatures
    if has_magic_bytes(content, WP5_SIGNATURE) {
        confidence = 0.95;
        metadata.insert("version_detected".to_string(), "5.x".to_string());
    } else if has_magic_bytes(content, WP6_SIGNATURE) {
        confidence = 0.95;
        metadata.insert("version_detected".to_string(), "6.x".to_string());
    }

    // Add header information to metadata
    metadata.insert("major_version".to_string(), header.major_version.to_string());
    metadata.insert("minor_version".to_string(), header.minor_version.to_string());
    metadata.insert("file_size".to_string(), header.file_size.to_string());
    metadata.insert("encrypted".to_string(), header.is_encrypted().to_string());
    metadata.insert("product_type".to_string(), header.product_type.to_string());

    Ok(FormatDetection {
        format_type: FormatType::WordPerfect,
        confidence,
        version: Some(header.version_string()),
        metadata,
    })
}

/// Convert WordPerfect to Markdown
pub fn convert_wordperfect_to_markdown(content: &[u8]) -> Result<ConversionResult, ConversionError> {
    let detection = detect_wordperfect_format(content)?;
    if detection.format_type != FormatType::WordPerfect {
        return Err(ConversionError::UnsupportedFormat("Not a valid WordPerfect file".to_string()));
    }

    let header = WordPerfectHeader::parse(content)?;
    
    if header.is_encrypted() {
        return Err(ConversionError::UnsupportedFormat("Encrypted WordPerfect files are not supported".to_string()));
    }

    // Extract document content
    let document_start = header.document_area_pointer as usize;
    if document_start >= content.len() {
        return Err(ConversionError::InvalidInput("Document area pointer out of bounds".to_string()));
    }

    let document_data = &content[document_start..];
    let (text_content, formatting_info) = extract_text_and_formatting(document_data)?;

    // Convert to markdown with basic formatting
    let markdown_content = apply_markdown_formatting(&text_content, &formatting_info);

    let mut metadata = detection.metadata;
    metadata.insert("extracted_text_length".to_string(), text_content.len().to_string());
    metadata.insert("formatting_functions".to_string(), formatting_info.len().to_string());

    let mut warnings = Vec::new();
    if header.major_version < 6 {
        warnings.push("WordPerfect 5.x files may have limited formatting support".to_string());
    }
    if text_content.len() < 10 {
        warnings.push("Very little text content extracted".to_string());
    }

    Ok(ConversionResult {
        content: markdown_content,
        format: "markdown".to_string(),
        metadata,
        warnings,
    })
}

/// Convert WordPerfect to RTF
pub fn convert_wordperfect_to_rtf(content: &[u8]) -> Result<ConversionResult, ConversionError> {
    let detection = detect_wordperfect_format(content)?;
    if detection.format_type != FormatType::WordPerfect {
        return Err(ConversionError::UnsupportedFormat("Not a valid WordPerfect file".to_string()));
    }

    let header = WordPerfectHeader::parse(content)?;
    
    if header.is_encrypted() {
        return Err(ConversionError::UnsupportedFormat("Encrypted WordPerfect files are not supported".to_string()));
    }

    let document_start = header.document_area_pointer as usize;
    if document_start >= content.len() {
        return Err(ConversionError::InvalidInput("Document area pointer out of bounds".to_string()));
    }

    let document_data = &content[document_start..];
    let (text_content, formatting_info) = extract_text_and_formatting(document_data)?;

    // Convert to RTF with formatting
    let rtf_content = apply_rtf_formatting(&text_content, &formatting_info);

    Ok(ConversionResult {
        content: rtf_content,
        format: "rtf".to_string(),
        metadata: detection.metadata,
        warnings: Vec::new(),
    })
}

/// Extract text and formatting from WordPerfect document data
fn extract_text_and_formatting(data: &[u8]) -> Result<(String, Vec<WpFunction>), ConversionError> {
    let mut text = String::new();
    let mut functions = Vec::new();
    let mut i = 0;

    while i < data.len() {
        let byte = data[i];

        match byte {
            WP_HARD_RETURN => {
                text.push('\n');
                i += 1;
            },
            WP_SOFT_RETURN => {
                text.push(' ');
                i += 1;
            },
            WP_TAB => {
                text.push('\t');
                i += 1;
            },
            WP_FUNCTION_PREFIX | WP_EXTENDED_PREFIX => {
                // Parse formatting function
                match WpFunction::parse(data, i) {
                    Ok((function, consumed)) => {
                        functions.push(function);
                        i += consumed;
                    },
                    Err(_) => {
                        // Skip invalid function
                        i += 1;
                    }
                }
            },
            0x20..=0x7E => {
                // Printable ASCII character
                text.push(byte as char);
                i += 1;
            },
            _ => {
                // Skip other control characters
                i += 1;
            }
        }
    }

    Ok((text, functions))
}

/// Apply markdown formatting based on WordPerfect functions
fn apply_markdown_formatting(text: &str, functions: &[WpFunction]) -> String {
    let mut result = String::new();
    let mut bold_active = false;
    let mut italic_active = false;
    let mut underline_active = false;

    // Simple formatting application (would need more sophisticated parsing for real implementation)
    for line in text.lines() {
        let mut formatted_line = line.to_string();

        // Apply basic formatting based on functions found
        for function in functions {
            match function.description.as_str() {
                "Bold On" => {
                    if !bold_active {
                        formatted_line = format!("**{}**", formatted_line);
                        bold_active = true;
                    }
                },
                "Italic On" => {
                    if !italic_active {
                        formatted_line = format!("*{}*", formatted_line);
                        italic_active = true;
                    }
                },
                "Underline On" => {
                    if !underline_active {
                        formatted_line = format!("<u>{}</u>", formatted_line);
                        underline_active = true;
                    }
                },
                _ => {}
            }
        }

        result.push_str(&formatted_line);
        result.push('\n');
    }

    if result.is_empty() {
        "# WordPerfect Document\n\n(No readable content found)".to_string()
    } else {
        format!("# WordPerfect Document\n\n{}", result.trim())
    }
}

/// Apply RTF formatting based on WordPerfect functions
fn apply_rtf_formatting(text: &str, _functions: &[WpFunction]) -> String {
    // Basic RTF conversion
    let escaped_text = text
        .replace('\\', "\\\\")
        .replace('{', "\\{")
        .replace('}', "\\}")
        .replace('\n', "\\par ");

    format!(
        "{{\\rtf1\\ansi\\deff0 {{\\fonttbl {{\\f0 Times New Roman;}}}} \\f0\\fs24 {}}}",
        escaped_text
    )
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_wordperfect_header_parsing() {
        let mut header_data = vec![0u8; 32];
        header_data[0..4].copy_from_slice(WP_SIGNATURE);
        header_data[20] = 5; // Major version
        header_data[21] = 1; // Minor version

        let header = WordPerfectHeader::parse(&header_data).unwrap();
        assert!(header.is_valid());
        assert_eq!(header.version_string(), "5.1");
    }

    #[test]
    fn test_wordperfect_detection() {
        let mut content = vec![0u8; 32];
        content[0..4].copy_from_slice(WP_SIGNATURE);
        content[20] = 6;
        content[21] = 0;

        let detection = detect_wordperfect_format(&content).unwrap();
        assert_eq!(detection.format_type, FormatType::WordPerfect);
        assert!(detection.confidence > 0.7);
    }

    #[test]
    fn test_wp_function_parsing() {
        let data = [0xC1, 0x01, 0x00, 0x00]; // Bold On function
        let (function, consumed) = WpFunction::parse(&data, 0).unwrap();
        assert_eq!(function.code, 0xC1);
        assert_eq!(function.subcode, Some(0x01));
        assert_eq!(consumed, 4);
    }
}
