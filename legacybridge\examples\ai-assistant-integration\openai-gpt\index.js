// OpenAI GPT Integration Example
// This example demonstrates how to integrate the LegacyBridge MCP server with OpenAI's GPT models

require('dotenv').config();
const { OpenAIClient } = require('./openai-client');
const { MCPClient } = require('./mcp-client');
const { functionDefinitions, implementedFunctions } = require('./functions');
const readline = require('readline');

// Create clients
const openai = new OpenAIClient(process.env.OPENAI_API_KEY);
const mcp = new MCPClient(process.env.MCP_SERVER_URL, process.env.MCP_API_KEY);

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Initialize conversation
const messages = [
  {
    role: 'system',
    content: `You are an AI assistant that can help users convert between document formats using the LegacyBridge MCP server.
You can convert between RTF and Markdown formats. When a user provides document content, analyze it to determine if it's RTF or Markdown,
and then use the appropriate conversion function. Always show the converted content in a code block with the appropriate language tag.`
  }
];

// Main conversation loop
async function startConversation() {
  console.log('\nLegacyBridge MCP + OpenAI GPT Integration Example');
  console.log('=================================================');
  console.log('Type your messages below. Type "exit" to quit.\n');
  
  await promptUser();
}

async function promptUser() {
  rl.question('You: ', async (input) => {
    if (input.toLowerCase() === 'exit') {
      console.log('Goodbye!');
      rl.close();
      return;
    }
    
    // Add user message to conversation
    messages.push({ role: 'user', content: input });
    
    try {
      // Get response from OpenAI with function calling
      const response = await openai.createChatCompletion({
        model: 'gpt-4',
        messages,
        functions: functionDefinitions,
        function_call: 'auto'
      });
      
      const responseMessage = response.choices[0].message;
      
      // Check if the model wants to call a function
      if (responseMessage.function_call) {
        const functionName = responseMessage.function_call.name;
        const functionArgs = JSON.parse(responseMessage.function_call.arguments);
        
        console.log(`\nAssistant is calling function: ${functionName}`);
        
        // Call the implemented function
        const functionResult = await implementedFunctions[functionName](functionArgs, mcp);
        
        // Add function response to conversation
        messages.push(responseMessage);
        messages.push({
          role: 'function',
          name: functionName,
          content: JSON.stringify(functionResult)
        });
        
        // Get final response from OpenAI
        const finalResponse = await openai.createChatCompletion({
          model: 'gpt-4',
          messages
        });
        
        const finalResponseMessage = finalResponse.choices[0].message;
        messages.push(finalResponseMessage);
        
        console.log(`\nAssistant: ${finalResponseMessage.content}\n`);
      } else {
        // Regular response (no function call)
        messages.push(responseMessage);
        console.log(`\nAssistant: ${responseMessage.content}\n`);
      }
    } catch (error) {
      console.error('Error:', error.message);
    }
    
    // Continue conversation
    promptUser();
  });
}

// Start the conversation
startConversation().catch(console.error);