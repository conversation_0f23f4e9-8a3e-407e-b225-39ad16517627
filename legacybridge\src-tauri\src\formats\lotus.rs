// Lotus 1-2-3 format parser
// Supports Lotus 1-2-3 WK1, WKS, and 123 files

use std::collections::HashMap;
use crate::conversion::error::ConversionError;
use super::common::*;
use super::{FormatDetection, ConversionResult, FormatType};

/// Lotus 1-2-3 file signatures and constants
const LOTUS_WK1_SIGNATURE: &[u8] = &[0x00, 0x00, 0x02, 0x00]; // WK1 signature
const LOTUS_WKS_SIGNATURE: &[u8] = &[0x00, 0x00, 0x01, 0x00]; // WKS signature
const LOTUS_123_SIGNATURE: &[u8] = &[0x00, 0x00, 0x1A, 0x00]; // 123 signature

/// Lotus record types
const LOTUS_BOF: u16 = 0x0000;  // Beginning of file
const LOTUS_EOF: u16 = 0x0001;  // End of file
const LOTUS_CALCMODE: u16 = 0x0002;
const LOTUS_CALCORDER: u16 = 0x0003;
const LOTUS_SPLIT: u16 = 0x0004;
const LOTUS_SYNC: u16 = 0x0005;
const LOTUS_RANGE: u16 = 0x0006;
const LOTUS_WINDOW1: u16 = 0x0007;
const LOTUS_COLW1: u16 = 0x0008;
const LOTUS_WINTWO: u16 = 0x0009;
const LOTUS_COLW2: u16 = 0x000A;
const LOTUS_NAME: u16 = 0x000B;
const LOTUS_BLANK: u16 = 0x000C;
const LOTUS_INTEGER: u16 = 0x000D;
const LOTUS_NUMBER: u16 = 0x000E;
const LOTUS_LABEL: u16 = 0x000F;
const LOTUS_FORMULA: u16 = 0x0010;

/// Lotus cell types
#[derive(Debug, Clone)]
enum CellType {
    Blank,
    Integer(i16),
    Number(f64),
    Label(String),
    Formula(String),
}

/// Lotus cell reference
#[derive(Debug, Clone)]
struct CellRef {
    row: u16,
    col: u8,
}

impl CellRef {
    fn new(row: u16, col: u8) -> Self {
        Self { row, col }
    }

    fn to_excel_notation(&self) -> String {
        let mut col_str = String::new();
        let mut col_num = self.col as u32;
        
        loop {
            col_str.insert(0, (b'A' + (col_num % 26) as u8) as char);
            if col_num < 26 {
                break;
            }
            col_num = col_num / 26 - 1;
        }
        
        format!("{}{}", col_str, self.row + 1)
    }
}

/// Lotus cell data
#[derive(Debug, Clone)]
struct LotusCell {
    reference: CellRef,
    cell_type: CellType,
    format: u8,
}

/// Lotus worksheet
#[derive(Debug)]
struct LotusWorksheet {
    cells: Vec<LotusCell>,
    metadata: HashMap<String, String>,
    max_row: u16,
    max_col: u8,
}

impl LotusWorksheet {
    fn new() -> Self {
        Self {
            cells: Vec::new(),
            metadata: HashMap::new(),
            max_row: 0,
            max_col: 0,
        }
    }

    fn add_cell(&mut self, cell: LotusCell) {
        self.max_row = self.max_row.max(cell.reference.row);
        self.max_col = self.max_col.max(cell.reference.col);
        self.cells.push(cell);
    }

    fn get_cell(&self, row: u16, col: u8) -> Option<&LotusCell> {
        self.cells.iter().find(|cell| cell.reference.row == row && cell.reference.col == col)
    }
}

/// Lotus record structure
#[derive(Debug)]
struct LotusRecord {
    record_type: u16,
    length: u16,
    data: Vec<u8>,
}

impl LotusRecord {
    fn parse(content: &[u8], offset: usize) -> Result<(Self, usize), ConversionError> {
        if offset + 4 > content.len() {
            return Err(ConversionError::InvalidInput("Record header incomplete".to_string()));
        }

        let record_type = bytes_to_u16_le(&content[offset..offset + 2]);
        let length = bytes_to_u16_le(&content[offset + 2..offset + 4]);
        
        if offset + 4 + length as usize > content.len() {
            return Err(ConversionError::InvalidInput("Record data incomplete".to_string()));
        }

        let data = content[offset + 4..offset + 4 + length as usize].to_vec();
        
        Ok((LotusRecord {
            record_type,
            length,
            data,
        }, 4 + length as usize))
    }
}

/// Detect Lotus 1-2-3 format
pub fn detect_lotus_format(content: &[u8]) -> Result<FormatDetection, ConversionError> {
    if content.len() < 8 {
        return Ok(FormatDetection {
            format_type: FormatType::Unknown,
            confidence: 0.0,
            version: None,
            metadata: HashMap::new(),
        });
    }

    let mut confidence = 0.0;
    let mut version = None;
    let mut metadata = extract_metadata(content, "Lotus 1-2-3");

    // Check for Lotus signatures
    if has_magic_bytes(content, LOTUS_WK1_SIGNATURE) {
        confidence = 0.9;
        version = Some("WK1".to_string());
        metadata.insert("format_variant".to_string(), "WK1".to_string());
    } else if has_magic_bytes(content, LOTUS_WKS_SIGNATURE) {
        confidence = 0.9;
        version = Some("WKS".to_string());
        metadata.insert("format_variant".to_string(), "WKS".to_string());
    } else if has_magic_bytes(content, LOTUS_123_SIGNATURE) {
        confidence = 0.9;
        version = Some("123".to_string());
        metadata.insert("format_variant".to_string(), "123".to_string());
    }

    // Validate by checking for BOF record
    if confidence > 0.0 {
        if let Ok((record, _)) = LotusRecord::parse(content, 0) {
            if record.record_type == LOTUS_BOF {
                confidence = 0.95;
                metadata.insert("has_bof_record".to_string(), "true".to_string());
                
                // Extract version info from BOF record
                if record.data.len() >= 2 {
                    let file_version = bytes_to_u16_le(&record.data[0..2]);
                    metadata.insert("file_version".to_string(), file_version.to_string());
                }
            }
        }
    }

    // Additional validation by checking record structure
    if confidence > 0.5 {
        let mut valid_records = 0;
        let mut offset = 0;
        
        while offset < content.len() && valid_records < 10 {
            if let Ok((record, consumed)) = LotusRecord::parse(content, offset) {
                match record.record_type {
                    LOTUS_BOF | LOTUS_EOF | LOTUS_CALCMODE | LOTUS_WINDOW1 |
                    LOTUS_BLANK | LOTUS_INTEGER | LOTUS_NUMBER | LOTUS_LABEL | LOTUS_FORMULA => {
                        valid_records += 1;
                    },
                    _ => {}
                }
                offset += consumed;
            } else {
                break;
            }
        }
        
        if valid_records >= 3 {
            confidence = f32::min(confidence + 0.1, 0.98);
            metadata.insert("valid_records_found".to_string(), valid_records.to_string());
        }
    }

    let format_type = if confidence > 0.7 {
        FormatType::Lotus123
    } else {
        FormatType::Unknown
    };

    Ok(FormatDetection {
        format_type,
        confidence,
        version,
        metadata,
    })
}

/// Parse Lotus 1-2-3 worksheet
fn parse_lotus_worksheet(content: &[u8]) -> Result<LotusWorksheet, ConversionError> {
    let mut worksheet = LotusWorksheet::new();
    let mut offset = 0;

    while offset < content.len() {
        let (record, consumed) = LotusRecord::parse(content, offset)?;
        
        match record.record_type {
            LOTUS_BOF => {
                if record.data.len() >= 2 {
                    let version = bytes_to_u16_le(&record.data[0..2]);
                    worksheet.metadata.insert("version".to_string(), version.to_string());
                }
            },
            LOTUS_EOF => {
                break; // End of file
            },
            LOTUS_BLANK => {
                if let Ok(cell) = parse_blank_cell(&record.data) {
                    worksheet.add_cell(cell);
                }
            },
            LOTUS_INTEGER => {
                if let Ok(cell) = parse_integer_cell(&record.data) {
                    worksheet.add_cell(cell);
                }
            },
            LOTUS_NUMBER => {
                if let Ok(cell) = parse_number_cell(&record.data) {
                    worksheet.add_cell(cell);
                }
            },
            LOTUS_LABEL => {
                if let Ok(cell) = parse_label_cell(&record.data) {
                    worksheet.add_cell(cell);
                }
            },
            LOTUS_FORMULA => {
                if let Ok(cell) = parse_formula_cell(&record.data) {
                    worksheet.add_cell(cell);
                }
            },
            _ => {
                // Skip unknown record types
            }
        }
        
        offset += consumed;
    }

    Ok(worksheet)
}

/// Parse blank cell record
fn parse_blank_cell(data: &[u8]) -> Result<LotusCell, ConversionError> {
    if data.len() < 5 {
        return Err(ConversionError::InvalidInput("Blank cell record too short".to_string()));
    }

    let format = data[0];
    let col = data[1];
    let row = bytes_to_u16_le(&data[2..4]);

    Ok(LotusCell {
        reference: CellRef::new(row, col),
        cell_type: CellType::Blank,
        format,
    })
}

/// Parse integer cell record
fn parse_integer_cell(data: &[u8]) -> Result<LotusCell, ConversionError> {
    if data.len() < 7 {
        return Err(ConversionError::InvalidInput("Integer cell record too short".to_string()));
    }

    let format = data[0];
    let col = data[1];
    let row = bytes_to_u16_le(&data[2..4]);
    let value = bytes_to_u16_le(&data[4..6]) as i16;

    Ok(LotusCell {
        reference: CellRef::new(row, col),
        cell_type: CellType::Integer(value),
        format,
    })
}

/// Parse number cell record
fn parse_number_cell(data: &[u8]) -> Result<LotusCell, ConversionError> {
    if data.len() < 13 {
        return Err(ConversionError::InvalidInput("Number cell record too short".to_string()));
    }

    let format = data[0];
    let col = data[1];
    let row = bytes_to_u16_le(&data[2..4]);
    
    // Parse 8-byte IEEE 754 double
    let mut value_bytes = [0u8; 8];
    value_bytes.copy_from_slice(&data[5..13]);
    let value = f64::from_le_bytes(value_bytes);

    Ok(LotusCell {
        reference: CellRef::new(row, col),
        cell_type: CellType::Number(value),
        format,
    })
}

/// Parse label cell record
fn parse_label_cell(data: &[u8]) -> Result<LotusCell, ConversionError> {
    if data.len() < 6 {
        return Err(ConversionError::InvalidInput("Label cell record too short".to_string()));
    }

    let format = data[0];
    let col = data[1];
    let row = bytes_to_u16_le(&data[2..4]);
    let label = String::from_utf8_lossy(&data[5..]).trim_end_matches('\0').to_string();

    Ok(LotusCell {
        reference: CellRef::new(row, col),
        cell_type: CellType::Label(label),
        format,
    })
}

/// Parse formula cell record (simplified)
fn parse_formula_cell(data: &[u8]) -> Result<LotusCell, ConversionError> {
    if data.len() < 15 {
        return Err(ConversionError::InvalidInput("Formula cell record too short".to_string()));
    }

    let format = data[0];
    let col = data[1];
    let row = bytes_to_u16_le(&data[2..4]);
    
    // For now, just extract the formula as a string representation
    let formula = format!("(Formula: {} bytes)", data.len() - 5);

    Ok(LotusCell {
        reference: CellRef::new(row, col),
        cell_type: CellType::Formula(formula),
        format,
    })
}

/// Convert Lotus 1-2-3 to Markdown
pub fn convert_lotus_to_markdown(content: &[u8]) -> Result<ConversionResult, ConversionError> {
    let detection = detect_lotus_format(content)?;
    if detection.format_type != FormatType::Lotus123 {
        return Err(ConversionError::UnsupportedFormat("Not a valid Lotus 1-2-3 file".to_string()));
    }

    let worksheet = parse_lotus_worksheet(content)?;
    let markdown_content = convert_worksheet_to_markdown(&worksheet)?;

    let mut metadata = detection.metadata;
    metadata.insert("cells_found".to_string(), worksheet.cells.len().to_string());
    metadata.insert("max_row".to_string(), worksheet.max_row.to_string());
    metadata.insert("max_col".to_string(), worksheet.max_col.to_string());

    let mut warnings = Vec::new();
    if worksheet.cells.is_empty() {
        warnings.push("No cells found in worksheet".to_string());
    }
    if worksheet.max_row > 100 || worksheet.max_col > 26 {
        warnings.push("Large worksheet - markdown table may be truncated".to_string());
    }

    Ok(ConversionResult {
        content: markdown_content,
        format: "markdown".to_string(),
        metadata,
        warnings,
    })
}

/// Convert Lotus 1-2-3 to RTF
pub fn convert_lotus_to_rtf(content: &[u8]) -> Result<ConversionResult, ConversionError> {
    let markdown_result = convert_lotus_to_markdown(content)?;
    
    // Convert markdown table to RTF (simplified)
    let rtf_content = format!(
        "{{\\rtf1\\ansi\\deff0 {{\\fonttbl {{\\f0 Courier New;}}}} \\f0\\fs20 {}}}",
        markdown_result.content
            .replace('\n', "\\par ")
            .replace('|', "\\cell ")
    );

    Ok(ConversionResult {
        content: rtf_content,
        format: "rtf".to_string(),
        metadata: markdown_result.metadata,
        warnings: markdown_result.warnings,
    })
}

/// Convert worksheet to markdown table
fn convert_worksheet_to_markdown(worksheet: &LotusWorksheet) -> Result<String, ConversionError> {
    let mut markdown = String::new();
    
    markdown.push_str("# Lotus 1-2-3 Worksheet\n\n");
    
    if let Some(version) = worksheet.metadata.get("version") {
        markdown.push_str(&format!("**Version:** {}\n", version));
    }
    markdown.push_str(&format!("**Dimensions:** {} rows × {} columns\n\n", 
                              worksheet.max_row + 1, worksheet.max_col + 1));

    if worksheet.cells.is_empty() {
        markdown.push_str("(Empty worksheet)\n");
        return Ok(markdown);
    }

    // Create table header
    markdown.push('|');
    for col in 0..=worksheet.max_col.min(25) { // Limit columns for readability
        let col_name = CellRef::new(0, col).to_excel_notation().chars().take_while(|c| c.is_alphabetic()).collect::<String>();
        markdown.push_str(&format!(" {} |", col_name));
    }
    markdown.push('\n');

    // Table separator
    markdown.push('|');
    for _ in 0..=worksheet.max_col.min(25) {
        markdown.push_str(" --- |");
    }
    markdown.push('\n');

    // Table rows
    for row in 0..=worksheet.max_row.min(99) { // Limit rows for readability
        markdown.push('|');
        for col in 0..=worksheet.max_col.min(25) {
            let cell_value = if let Some(cell) = worksheet.get_cell(row, col) {
                match &cell.cell_type {
                    CellType::Blank => "".to_string(),
                    CellType::Integer(val) => val.to_string(),
                    CellType::Number(val) => format!("{:.2}", val),
                    CellType::Label(text) => text.clone(),
                    CellType::Formula(formula) => formula.clone(),
                }
            } else {
                "".to_string()
            };
            
            let escaped_value = cell_value.replace('|', "\\|").replace('\n', " ");
            markdown.push_str(&format!(" {} |", escaped_value));
        }
        markdown.push('\n');
    }

    if worksheet.max_row > 99 || worksheet.max_col > 25 {
        markdown.push_str(&format!("\n*Note: Showing first 100 rows and 26 columns of {} × {} worksheet*\n", 
                                  worksheet.max_row + 1, worksheet.max_col + 1));
    }

    Ok(markdown)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_lotus_detection() {
        let mut content = vec![0u8; 16];
        content[0..4].copy_from_slice(LOTUS_WK1_SIGNATURE);
        content[4] = 0x00; // BOF record type
        content[5] = 0x00;
        content[6] = 0x02; // Record length
        content[7] = 0x00;

        let detection = detect_lotus_format(&content).unwrap();
        assert_eq!(detection.format_type, FormatType::Lotus123);
        assert!(detection.confidence > 0.8);
    }

    #[test]
    fn test_cell_reference_notation() {
        let cell_ref = CellRef::new(0, 0);
        assert_eq!(cell_ref.to_excel_notation(), "A1");
        
        let cell_ref = CellRef::new(25, 25);
        assert_eq!(cell_ref.to_excel_notation(), "Z26");
    }

    #[test]
    fn test_record_parsing() {
        let data = [0x00, 0x00, 0x02, 0x00, 0x01, 0x02]; // BOF record with 2 bytes data
        let (record, consumed) = LotusRecord::parse(&data, 0).unwrap();
        assert_eq!(record.record_type, LOTUS_BOF);
        assert_eq!(record.length, 2);
        assert_eq!(consumed, 6);
    }
}
