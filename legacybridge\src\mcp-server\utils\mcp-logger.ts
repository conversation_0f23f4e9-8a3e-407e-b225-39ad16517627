// MCP Server Logger
// Provides structured logging for the MCP server

import winston from 'winston';
import { format } from 'winston';

const { combine, timestamp, printf, colorize, json } = format;

// Custom log format for development
const devFormat = printf(({ level, message, timestamp, ...metadata }) => {
  let metaStr = '';
  if (Object.keys(metadata).length > 0) {
    metaStr = JSON.stringify(metadata);
  }
  return `${timestamp} [${level}]: ${message} ${metaStr}`;
});

export class MCPLogger {
  public logger: winston.Logger;
  public logLevel: string;

  constructor(level: string = 'info') {
    this.logLevel = level;
    // Create logger with appropriate configuration
    this.logger = winston.createLogger({
      level: level,
      format: combine(
        timestamp(),
        process.env.NODE_ENV === 'production' ? json() : devFormat
      ),
      defaultMeta: { service: 'mcp-server' },
      transports: [
        // Console transport
        new winston.transports.Console({
          format: combine(
            process.env.NODE_ENV !== 'production' ? colorize() : format.uncolorize(),
            timestamp(),
            devFormat
          ),
        }),
        // File transport for errors
        new winston.transports.File({ 
          filename: 'logs/mcp-error.log', 
          level: 'error',
          maxsize: 10 * 1024 * 1024, // 10MB
          maxFiles: 5,
        }),
        // File transport for all logs
        new winston.transports.File({ 
          filename: 'logs/mcp-combined.log',
          maxsize: 10 * 1024 * 1024, // 10MB
          maxFiles: 5,
        }),
      ],
    });

    // Ensure log directory exists
    const fs = require('fs');
    if (!fs.existsSync('logs')) {
      fs.mkdirSync('logs');
    }
  }

  // Log methods
  public debug(message: string, meta: any = {}): void {
    if (Object.keys(meta).length === 0) {
      this.logger.debug(message);
    } else {
      this.logger.debug(message, meta);
    }
  }

  public info(message: string, meta: any = {}): void {
    if (Object.keys(meta).length === 0) {
      this.logger.info(message);
    } else {
      this.logger.info(message, meta);
    }
  }

  public warn(message: string, meta: any = {}): void {
    if (Object.keys(meta).length === 0) {
      this.logger.warn(message);
    } else {
      this.logger.warn(message, meta);
    }
  }

  public error(message: string, error?: any, meta: any = {}): void {
    if (error instanceof Error) {
      this.logger.error(message, error);
    } else if (error && typeof error === 'object') {
      this.logger.error(message, error);
    } else if (Object.keys(meta).length === 0 && !error) {
      this.logger.error(message);
    } else {
      this.logger.error(message, meta);
    }
  }

  // Log request details
  public logRequest(req: any, res: any, duration: number): void {
    const { method, path, ip, headers } = req;
    const { statusCode } = res;
    
    this.info(`${method} ${path} ${statusCode} ${duration}ms`, {
      method,
      path,
      statusCode,
      duration,
      ip,
      userAgent: headers['user-agent'],
      requestId: headers['x-request-id'],
    });
  }

  // Log conversion job
  public logConversion(jobId: string, status: string, details: any = {}): void {
    this.info(`Conversion job ${jobId} ${status}`, {
      jobId,
      status,
      ...details,
    });
  }

  // Log batch job
  public logBatchJob(batchId: string, status: string, details: any = {}): void {
    this.info(`Batch job ${batchId} ${status}`, {
      batchId,
      status,
      ...details,
    });
  }

  // Log security events
  public logSecurityEvent(event: string, details: any = {}): void {
    this.warn(`Security event: ${event}`, {
      securityEvent: event,
      ...details,
    });
  }
}