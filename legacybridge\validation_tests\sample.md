# Test Document

This document tests **bold**, *italic*, and ***combined*** formatting.

## Features

- Bullet lists
- Numbered lists
- Tables
- Code blocks

### Table Example

| Feature | Status |
|---------|--------|
| Bold    | ✓      |
| Italic  | ✓      |
| Lists   | ✓      |

### Code Example

```rust
fn main() {
    println!("Hello, RTF!");
}
```

Special characters: © ® ™ € £ ¥
Unicode: 你好 مرحبا 🚀
