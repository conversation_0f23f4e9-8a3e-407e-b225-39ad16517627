// MCP Server Entry Point
// Starts the MCP server with the specified configuration

import { MCPServer } from './core/mcp-server';
import { loadConfig } from './utils/mcp-config';
import { MCPLogger } from './utils/mcp-logger';

// Create logger
const logger = new MCPLogger(process.env['LOG_LEVEL'] || 'info');

// Load configuration
try {
  const config = loadConfig();
  
  // Create and start server
  const server = new MCPServer(config);
  
  // Handle process signals
  process.on('SIGINT', async () => {
    logger.info('Received SIGINT signal, shutting down...');
    await server.stop();
    process.exit(0);
  });
  
  process.on('SIGTERM', async () => {
    logger.info('Received SIGTERM signal, shutting down...');
    await server.stop();
    process.exit(0);
  });
  
  // Start server
  server.start();
  
  // Log startup message
  logger.info(`MCP Server started on port ${config.port}`);
  logger.info(`Environment: ${config.environment}`);
  logger.info(`Log level: ${config.logLevel}`);
  logger.info(`Cache enabled: ${config.cache.enabled}`);
  logger.info(`Legacy formats: DOC=${config.legacyFormats.enableDOC}, WordPerfect=${config.legacyFormats.enableWordPerfect}`);
  
} catch (error) {
  logger.error('Failed to start MCP Server', error);
  process.exit(1);
}