{"permissions": {"allow": ["Bash(npx:*)", "WebFetch(domain:github.com)", "<PERSON><PERSON>(git clone:*)", "Bash(/home/<USER>/.local/bin/uv run python main.py --help)"]}, "enabledMcpjsonServers": ["firebase", "fetch", "sequential-thinking", "puppeteer", "mcp-playwright", "everything", "memory", "memory-bank-mcp", "github-official", "mcp-filesystem", "desktop-commander", "netlify", "context7-mcp", "consult7", "brave-search", "taskmaster-ai", "n8n-mcp-server", "perplexity-mcp", "deep-code-reasoning", "shadcn-ui", "nextjs-manager", "zen", "quick-data-mcp", "vibe-coder-mcp-wsl", "vibe-coder-mcp", "mcp-installer", "n8n-mcp", "firecrawl", "agentic-tools-claude"], "disabledMcpjsonServers": ["vibe-coder-mcp-wsl", "zen", "dart-mcp", "gemini", "taskmanager", "dart"], "enableAllProjectMcpServers": true}