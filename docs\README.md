# LegacyBridge Documentation

## Table of Contents
- [Welcome](#welcome)
- [Documentation Structure](#documentation-structure)
  - [🚀 Getting Started](#-getting-started)
  - [🔌 API Documentation](#-api-documentation)
  - [📚 Integration Guides](#-integration-guides)
  - [🏗️ Architecture Documentation](#️-architecture-documentation)
  - [🔧 Troubleshooting](#-troubleshooting)
  - [🎥 Video Tutorials](#-video-tutorials)
  - [👨‍💻 Development](#-development)
- [Quick Links](#quick-links)
- [Documentation Coverage](#documentation-coverage)
- [Search Documentation](#search-documentation)

## Welcome

Welcome to the comprehensive documentation for LegacyBridge - the enterprise-grade RTF ↔ Markdown converter.

## Documentation Structure

### 🚀 Getting Started
- [Overview & Navigation](getting-started/README.md)
- [Quick Start Guide](getting-started/quick-start.md) - Get up and running in 5 minutes
- [Installation Guides](getting-started/installation/)
  - [Windows Installation](getting-started/installation/windows.md)
  - [Linux Installation](getting-started/installation/linux.md)
  - [macOS Installation](getting-started/installation/macos.md)
  - [Docker Deployment](getting-started/installation/docker.md)
- [First Conversion Tutorial](getting-started/first-conversion.md)

### 🔌 API Documentation
- [Interactive API Explorer](api/interactive-explorer/) - Try API calls in your browser
- [API Reference](api/reference/README.md)
  - [Core Functions](api/reference/core-functions.md) - All 29 FFI functions
  - [Error Codes](api/reference/error-codes.md) - Complete error reference
  - [Rate Limits](api/reference/rate-limits.md) - API limitations and quotas
- [Code Examples](api/examples/)
  - [VB6 Integration](api/examples/vb6/)
  - [VFP9 Integration](api/examples/vfp9/)
  - [.NET Integration](api/examples/dotnet/)
  - [Python Wrapper](api/examples/python/)
  - [JavaScript/Web](api/examples/javascript/)

### 📚 Integration Guides
- [Legacy Integration](guides/legacy-integration/)
  - [VB6 Complete Guide](guides/legacy-integration/vb6-complete.md)
  - [VFP9 Complete Guide](guides/legacy-integration/vfp9-complete.md)
  - [.NET Integration Guide](guides/legacy-integration/dotnet-integration.md)
  - [32-bit Compatibility](guides/legacy-integration/32bit-compatibility.md)
  - [Memory Management](guides/legacy-integration/memory-management.md)
- [Enterprise Deployment](guides/enterprise-deployment/)
  - [Architecture Planning](guides/enterprise-deployment/architecture-planning.md)
  - [Scalability Guide](guides/enterprise-deployment/scalability-guide.md)
  - [Security Hardening](guides/enterprise-deployment/security-hardening.md)
  - [Monitoring Setup](guides/enterprise-deployment/monitoring-setup.md)
  - [Disaster Recovery](guides/enterprise-deployment/disaster-recovery.md)
- [Performance Optimization](guides/performance/)
  - [Optimization Guide](guides/performance/optimization-guide.md)
  - [Benchmarking](guides/performance/benchmarking.md)
  - [Troubleshooting](guides/performance/troubleshooting.md)
- [Migration Guides](guides/migration/)
  - [From Pandoc](guides/migration/from-pandoc.md)
  - [From Other Converters](guides/migration/from-other-converters.md)
  - [Version Upgrades](guides/migration/version-upgrades.md)

### 🏗️ Architecture Documentation
- [System Overview](architecture/system-overview.md)
- [Component Diagrams](architecture/component-diagrams/) - Interactive architecture diagrams
- [Data Flow](architecture/data-flow.md) - Conversion process flow
- [Security Model](architecture/security-model.md)
- [Scalability Design](architecture/scalability-design.md)

### 🔧 Troubleshooting
- [Interactive Troubleshooting Wizard](troubleshooting/interactive-wizard/) - Step-by-step problem solving
- [Common Issues](troubleshooting/common-issues.md) - FAQ with solutions
- [Error Codes Reference](troubleshooting/error-codes.md)
- [Diagnostic Tools](troubleshooting/diagnostic-tools.md)
- [Performance Issues](troubleshooting/performance-issues.md)
- [Security Issues](troubleshooting/security-issues.md)

### 🎥 Video Tutorials
- [Installation Walkthrough](video-tutorials/installation-walkthrough.md)
- [Basic Usage Demo](video-tutorials/basic-usage-demo.md)
- [Enterprise Setup](video-tutorials/enterprise-setup.md)
- [Troubleshooting Guide](video-tutorials/troubleshooting-guide.md)
- [Advanced Features](video-tutorials/advanced-features.md)

### 👨‍💻 Development
- [Contributing Guidelines](development/contributing.md)
- [Coding Standards](development/coding-standards.md)
- [Testing Guide](development/testing-guide.md)
- [Release Process](development/release-process.md)

## Quick Links

- 📖 [User Guide](/legacybridge/USER_GUIDE.md)
- 🔌 [API Reference](/legacybridge/API_REFERENCE.md)
- 🚀 [Installation Guide](/legacybridge/ENTERPRISE_INSTALLATION_GUIDE.md)
- 🐛 [Troubleshooting Guide](/legacybridge/TROUBLESHOOTING_GUIDE.md)
- 📝 [Release Notes](/legacybridge/RELEASE_NOTES.md)

## Documentation Coverage

| Category | Coverage | Status |
|----------|----------|--------|
| API Documentation | 100% | ✅ Complete |
| Code Examples | 95% | ✅ Complete |
| Visual Guides | 80% | 🚧 In Progress |
| Video Tutorials | 70% | 🚧 In Progress |
| Interactive Features | 90% | ✅ Complete |

## Search Documentation

Use the search functionality to quickly find what you're looking for across all documentation.

---

Built with ❤️ by [Beau Lewis](mailto:<EMAIL>)