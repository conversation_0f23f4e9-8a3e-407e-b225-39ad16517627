version: '3.9'

services:
  # Main application
  legacybridge:
    build:
      context: .
      dockerfile: Dockerfile.optimized
      args:
        VERSION: ${VERSION:-dev}
        BUILD_DATE: ${BUILD_DATE:-now}
        COMMIT_SHA: ${COMMIT_SHA:-local}
    image: legacybridge:${VERSION:-latest}
    container_name: legacybridge-app
    ports:
      - "3000:3000"
      - "9090:9090"
    environment:
      NODE_ENV: ${NODE_ENV:-development}
      LOG_LEVEL: ${LOG_LEVEL:-debug}
      DATABASE_URL: ********************************************/legacybridge
      REDIS_URL: redis://redis:6379
      METRICS_PORT: 9090
      JWT_SECRET: ${JWT_SECRET:-development-secret}
      ENCRYPTION_KEY: ${ENCRYPTION_KEY:-development-key}
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    volumes:
      - ./legacybridge/uploads:/app/uploads
      - ./legacybridge/temp:/app/temp
    networks:
      - legacybridge-network
    restart: unless-stopped

  # PostgreSQL database
  postgres:
    image: postgres:15-alpine
    container_name: legacybridge-postgres
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: legacybridge
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --locale=en_US.UTF-8"
    ports:
      - "5432:5432"
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init.sql:ro
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - legacybridge-network
    restart: unless-stopped

  # Redis cache
  redis:
    image: redis:7-alpine
    container_name: legacybridge-redis
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - legacybridge-network
    restart: unless-stopped

  # Prometheus monitoring
  prometheus:
    image: prom/prometheus:v2.47.0
    container_name: legacybridge-prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/usr/share/prometheus/console_libraries'
      - '--web.console.templates=/usr/share/prometheus/consoles'
      - '--web.enable-lifecycle'
    ports:
      - "9091:9090"
    volumes:
      - ./monitoring/prometheus:/etc/prometheus:ro
      - prometheus-data:/prometheus
    networks:
      - legacybridge-network
    restart: unless-stopped

  # Grafana visualization
  grafana:
    image: grafana/grafana:10.1.0
    container_name: legacybridge-grafana
    environment:
      GF_SECURITY_ADMIN_USER: admin
      GF_SECURITY_ADMIN_PASSWORD: admin
      GF_USERS_ALLOW_SIGN_UP: 'false'
      GF_INSTALL_PLUGINS: grafana-clock-panel,grafana-simple-json-datasource
    ports:
      - "3001:3000"
    volumes:
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning:ro
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards:ro
      - grafana-data:/var/lib/grafana
    depends_on:
      - prometheus
    networks:
      - legacybridge-network
    restart: unless-stopped

  # Jaeger tracing (development)
  jaeger:
    image: jaegertracing/all-in-one:latest
    container_name: legacybridge-jaeger
    environment:
      COLLECTOR_ZIPKIN_HOST_PORT: :9411
    ports:
      - "5775:5775/udp"
      - "6831:6831/udp"
      - "6832:6832/udp"
      - "5778:5778"
      - "16686:16686"
      - "14268:14268"
      - "14250:14250"
      - "9411:9411"
    networks:
      - legacybridge-network
    profiles:
      - debug

  # MinIO (S3-compatible storage for development)
  minio:
    image: minio/minio:latest
    container_name: legacybridge-minio
    command: server /data --console-address ":9001"
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio-data:/data
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
    networks:
      - legacybridge-network
    profiles:
      - storage

  # Nginx reverse proxy (optional)
  nginx:
    image: nginx:alpine
    container_name: legacybridge-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - legacybridge
    networks:
      - legacybridge-network
    profiles:
      - production

  # pgAdmin (database management)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: legacybridge-pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "5050:80"
    volumes:
      - pgadmin-data:/var/lib/pgadmin
    networks:
      - legacybridge-network
    profiles:
      - tools

  # Redis Commander (Redis UI)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: legacybridge-redis-commander
    environment:
      REDIS_HOSTS: local:redis:6379
    ports:
      - "8081:8081"
    depends_on:
      - redis
    networks:
      - legacybridge-network
    profiles:
      - tools

networks:
  legacybridge-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  postgres-data:
    driver: local
  redis-data:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local
  minio-data:
    driver: local
  pgadmin-data:
    driver: local