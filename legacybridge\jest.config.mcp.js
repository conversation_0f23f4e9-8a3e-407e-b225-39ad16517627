module.exports = {
  // Test environment
  testEnvironment: 'node',
  
  // Module paths
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@mcp/(.*)$': '<rootDir>/src/mcp-server/$1'
  },
  
  // Transform files
  transform: {
    '^.+\\.(ts|tsx)$': ['ts-jest']
  },
  
  // Test patterns
  testMatch: [
    '<rootDir>/tests/mcp-server/**/*.{test,spec}.{js,ts}',
    '<rootDir>/src/mcp-server/**/__tests__/**/*.{js,ts}'
  ],

  // Exclude Playwright tests from Jest
  testPathIgnorePatterns: [
    '/node_modules/',
    '/build/',
    '/dist/',
    '/.next/',
    '/out/',
    '/tests/mcp-server/playwright/'
  ],
  
  // Coverage configuration
  collectCoverage: true,
  collectCoverageFrom: [
    'src/mcp-server/**/*.{js,ts}',
    '!src/mcp-server/**/*.d.ts',
    '!src/mcp-server/**/index.{js,ts}',
    '!src/mcp-server/types/**',
    '!src/mcp-server/**/__tests__/**',
    '!src/mcp-server/**/__mocks__/**'
  ],
  coverageDirectory: 'coverage/mcp-server',
  coverageReporters: ['text', 'lcov', 'html', 'json'],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    }
  },
  
  // Module directories
  moduleDirectories: ['node_modules', 'src'],
  
  // File extensions
  moduleFileExtensions: ['js', 'ts', 'json'],
  

  
  // Globals
  globals: {
    'ts-jest': {
      isolatedModules: true
    }
  },
  
  // Fake timers
  fakeTimers: {
    enableGlobally: false
  },
  
  // Verbose output
  verbose: true,
  
  // Max workers
  maxWorkers: '50%',
  
  // Test timeout
  testTimeout: 30000
};